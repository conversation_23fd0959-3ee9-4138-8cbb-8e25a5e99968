/**
 * 定义一下方法
 * from http://xh829.com/
 * 来自信呼开发团队
 * */

package com.dialog;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;


import com.baselib.AR;
import com.baselib.Rock;
import com.baselib.CallBack;

import java.util.Timer;
import java.util.TimerTask;


public class DialogMsg {

    private static View view = null;
    private static AlertDialog dialog = null;
    private static Timer sTimer = null;

    /**
     * 显示
     * */
    public static void show(Context context, String msg, int lx, int time, CallBack call)
    {
        hide();
        DialogLoad.hide();
        view = Rock.getView(context, AR.getlayoutID("dialog_msg"));
        TextView tv = (TextView)view.findViewById(AR.getID("msg_cont"));
        tv.setText(msg);
        if(lx==0){
            tv.setTextColor(Color.parseColor("#D9534F"));
            ImageView img = (ImageView)view.findViewById(AR.getID("msg_icon"));
            img.setImageDrawable(context.getResources().getDrawable(AR.getmipmapID("error")));
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context);

        builder.setView(view);
        dialog = builder.show();

        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.getWindow().setDimAmount(0f);

        sTimer = new Timer();
        sTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                hide();
                if(call!=null)call.back();
            }
        }, time * 1000);
    }

    /**
     * 成功的显示
     * */
    public static void success(Context context, String msg)
    {
        show(context, msg, 1,2, null);
    }
    public static void success(Context context, String msg, int time)
    {
        show(context, msg, 1,time, null);
    }

    /**
     * 错误的显示
     * */
    public static void error(Context context, String msg)
    {
        show(context, msg, 0,2, null);
    }
    public static void error(Context context, String msg, int time)
    {
        show(context, msg, 0,time, null);
    }

    /**
     * 显示
     * */
    public static void hide()
    {
        if(dialog!=null){
            dialog.dismiss();
            dialog  = null;
            view    = null;
        }
        if(sTimer!=null){
            sTimer.cancel();
            sTimer = null;
        }
    }

}