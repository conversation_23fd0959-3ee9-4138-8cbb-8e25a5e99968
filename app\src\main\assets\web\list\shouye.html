<script>
	$(document).ready(function() {
		var c = {
			init: function() {

				$('#myshow_name').html('' + adminname + '');
				this.jiazai();
			},
			jiazai: function() {
				js.ajax('tt|index', {}, function(e) {
					if (e.code == 200) {
						$("#tianqi").html(e.data.tianqi)
						if(e.data.gonggao.length > 0){
							$("#tongzhi").html(e.data.gonggao[0].title)
							$("#tongzhi").data('id',e.data.gonggao[0].id)
						}
						var str='',news=e.data.redian;
						for (var i = 0; i < (news.length>1?1:news.length); i++) {
							console.log(i)
							console.log(news[i])
							if(news[i] && news[i].fengmian !=''){
								str+='<div class="swiper-slide"  onclick="lx{rand}.dakai('+news[i].id+')">'
								str+='<img src="'+news[i].fengmian+'" style="width:100%;height:150px;object-fit: cover; border-radius:5px;" />'
								str+='</div>'
							}
							
						}
						$("#banner").html(str)
						var str2='';
						e.data.news.forEach(e=>{
							
							str2+='<div style="display:flex;flex-direction: row;justify-content: space-around;padding: 10px;border-radius: 8px; background-color: #fff;margin-bottom: 10px;" onclick="lx{rand}.dakai('+e.id+')">'
							str2+='	<div style="width:110px ;">'
							str2+='		<img src="'+e.fengmian+'" style="width: 100%;height: 80px;object-fit:cover; border-radius: 6px;" />'
							str2+='	</div>'
							str2+='	<div style="flex: 1;padding-left:10px" >'
							str2+='		<div style="width:100%;height:20px; text-align:left;overflow: hidden;font-size: 16px;font-weight: 900;">'+e.title+'</div>'
							str2+='		<div style="width:100%;height:38px;margin-top: 10px;text-align:left;overflow: hidden;font-size: 13px;color: #888;">'+e.content+'</div>'
							str2+='		<div style="margin-top: 10px;display: flex;flex-direction: row;align-items: center;justify-content: flex-start;">'
							str2+='			<img src="images/c4.png" style="height: 15px;" />'
							str2+='			<span style="font-size: 12px;color: #888;margin-left: 5px;">发布时间:'+e.optdt+'</span>'
							str2+='		</div>'
							str2+='	</div>'
							str2+='</div>'
						})
						$("#newslist").html(str2)
						
					}
				})
			},
			dakai:function(id){
				xcy.openurls('task.php?a=x&num=news&mid='+id)
			},
			dakai2:function(id){
				xcy.openurls('task.php?a=x&num=gong&mid='+$("#tongzhi").data("id"))
			},
			dakai3:function(id){
				xcy.openurls('index.php?m=ying&d=we&num=gong&yingnum=gong')
			},
			dakai4:function(){
				xcy.openurls('index.php?d=we&m=ying&a=daka&yingnum=kqdaka&device='+device)
			},
			dakai5:function(){
				xcy.openurls('index.php?d=we&m=ying&num=pxgl&openfrom=reim&winobj=agentpxgl')
			},
			dakai6:function(){
				xcy.openurls('index.php?m=ying&d=we&num=news')
			}
		}

		c.init();
		lx{rand} = c;




	});

	function tiaozhuan() {
		// alert(123456)
		xcy.addtabs('识别', 'shibie')
	}
</script>




<div align="center" style="padding:0px">
	<div
		style="display: none;justify-content: space-between;align-items: center;background-color: #fe0000;color: #fff;padding: 10px;">
		<div id="tianqi">天气预报</div>
		<div id="myshow_name"></div>
	</div>
	<div style="padding:0 10px 10px 10px;background-image: linear-gradient(#fe0000,#f2f2f2);">
		<div class="swiper mySwiper">
			<div class="swiper-wrapper" id="banner">
			</div>
			<div class="swiper-pagination"></div>
		</div>
	</div>
	<div style="padding: 0 10px 0 10px;">
		<div style="display: flex;justify-content: space-between;align-items: center;flex-direction: row;padding: 8px;background-color: #fff;border-radius: 20px;"  >
			<img src="images/b1.png" style="height: 24px;" />
			<div style="height: 24px;flex: 1;line-height: 24px;overflow: hidden;text-align: left; text-overflow:ellipsis;white-space: nowrap;padding-left: 10px;padding-right: 10px;"  onclick="lx{rand}.dakai2()">
				<span style="font-size:14px;color:#888;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;line-height:24px;" data-id='' id="tongzhi">暂无通知</span>
			</div>
			<img  onclick="lx{rand}.dakai3()" src="images/b2.png" style="height: 18px;" />
		</div>
	</div>
	<div style="display: flex;justify-content: space-between;align-items: center;flex-direction: row;padding:10px;flex-wrap: nowrap;">
		<div style="flex:1;padding-right: 5px;">
			<img src="images/c1.png" onclick="lx{rand}.dakai4()" style="width: 100%;" />
		</div>
		<div style="flex:1;padding-left: 5px;">
			<img src="images/c2.png" onclick="lx{rand}.dakai5()" style="width: 100%;" />
		</div>
	</div>
	
	<div style="display: flex;justify-content: space-between;align-items: center;flex-direction: row;padding: 0 10px;">
		<div style="flex: 1; display: flex;flex-direction: row;align-items: center;justify-content: flex-start;">
			<img src="images/c3.png" " style="height: 18px;" />
			<span style="margin-left:5px;">公司资讯</span>
		</div>
		<div style="flex: 1; display: flex;flex-direction: row;align-items: center;justify-content: flex-end;" @click="to_page('home/zixun.html','公司资讯')">
			<img src="images/c5.png" onclick="lx{rand}.dakai6()" style="height: 18px;" />
		</div>
	</div>
	
	<div style="padding: 10px;" id="newslist">
		
		
	</div>
	
</div>