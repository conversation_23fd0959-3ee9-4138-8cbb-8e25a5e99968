<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">


	<item android:state_focused="true">
		<shape>
			<solid android:color="#ffffff" ></solid>
			<corners android:radius="8dp" />
			<stroke android:color="@color/mcolors"  android:width="1px" />
		</shape>
	</item>


	<item android:state_pressed="true">
		<shape>
			<solid android:color="#ffffff" ></solid>
			<corners android:radius="8dp" />
			<stroke android:color="@color/mcolors"  android:width="1px" />
		</shape>
	</item>

	<item>
		<shape>
			<solid android:color="#ffffff" ></solid>
			<corners android:radius="8dp" />
			<stroke android:color="#cccccc"  android:width="1px" />
		</shape>
	</item>
</selector>