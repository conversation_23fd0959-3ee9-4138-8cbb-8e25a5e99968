<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#555555"
    android:layout_height="fill_parent">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:id="@+id/zongmain"
        android:orientation="horizontal">
    </LinearLayout>



    <LinearLayout
        android:id="@+id/back"
        android:layout_width="@dimen/headheight"
        android:layout_height="@dimen/headheight"
        android:background="@drawable/btn_tm"
        android:layout_alignParentLeft="true"
        android:layout_marginTop="40dp"
        android:gravity="center"
        android:orientation="vertical">
        <com.view.RockTextViewIcon
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_back"
            android:textSize="20dp"
            android:textColor="@color/white" />
    </LinearLayout>




    <!--视频通话时显示-->
    <LinearLayout
         android:layout_marginTop="50dp"
         android:layout_marginLeft="10dp"
         android:gravity="center"
         android:id="@+id/namedivs"
         android:visibility="gone"
         android:layout_width="wrap_content"
         android:layout_height="wrap_content"   >

         <com.view.RockImageView
             android:id="@+id/faces"
             android:layout_width="50dp"
             android:layout_height="50dp"
             android:layout_centerHorizontal="true"
             android:layout_gravity="center"
             android:src="@mipmap/logo"
             app:radius="180" />

         <LinearLayout
             android:layout_width="wrap_content"
             android:layout_marginLeft="10dp"
             android:orientation="vertical"
             android:layout_height="wrap_content">
             <TextView
                 android:id="@+id/names"
                 android:layout_width="wrap_content"
                 android:text="赵子龙"
                 android:textSize="18dp"
                 android:textColor="@color/white"
                 android:layout_height="wrap_content" />

             <TextView
                 android:id="@+id/titles"
                 android:layout_width="wrap_content"
                 android:layout_height="wrap_content"
                 android:layout_marginTop="3dp"
                 android:text="选择通话类型"
                 android:textColor="#aaaaaa"
                 android:textSize="14dp" />
         </LinearLayout>

     </LinearLayout>

    <!--头像界面1-->
    <LinearLayout
         android:layout_width="match_parent"
         android:layout_marginTop="120dp"
         android:gravity="center"
         android:id="@+id/namediv"
         android:orientation="vertical"
         android:layout_height="wrap_content" >
         <com.view.RockImageView
             android:id="@+id/face"
             android:layout_width="100dp"
             android:layout_height="100dp"
             android:layout_centerHorizontal="true"
             android:layout_gravity="center"

             android:src="@mipmap/logo"
             app:radius="180" />

         <TextView
             android:id="@+id/name"
             android:layout_width="wrap_content"
             android:layout_gravity="center"
             android:text="赵子龙"
             android:layout_marginTop="20dp"
             android:textSize="20dp"
             android:textColor="@color/white"
             android:layout_height="wrap_content" />

         <TextView
             android:id="@+id/title"
             android:layout_width="wrap_content"
             android:layout_height="wrap_content"
             android:layout_gravity="center"
             android:layout_marginTop="20dp"
             android:text="选择通话类型"
             android:textColor="#aaaaaa"
             android:textSize="16dp" />

     </LinearLayout>

    <!--呼叫界面-->
    <LinearLayout
            android:id="@+id/btn_callstart"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"

            android:layout_marginBottom="80dp"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/btn_yuyin"
                android:layout_width="90dp"
                android:layout_height="90dp"
                android:background="#ff6600"
                android:gravity="center"

                >

                <com.view.RockTextViewIcon
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="&#xeca7; 语音"
                    android:textColor="@color/white"
                    android:textSize="18dp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btn_shipin"
                android:layout_width="90dp"
                android:layout_height="90dp"
                android:layout_marginLeft="60dp"
                android:background="#339933"
                android:gravity="center">

                <com.view.RockTextViewIcon
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="&#xeca5; 视频"
                    android:textColor="@color/white"
                    android:textSize="18dp" />

            </LinearLayout>
        </LinearLayout>

    <!--取消呼叫-->
    <LinearLayout
        android:id="@+id/btn_callcancals"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="50dp"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/btn_callcancal"
                android:layout_width="90dp"
                android:layout_height="90dp"
                android:background="#D9534F"
                android:gravity="center">

                <com.view.RockTextViewIcon
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/icon_phone"
                    android:textColor="@color/white"
                    android:textSize="40dp" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="取消呼叫"
                android:textColor="#aaaaaa"
                android:textSize="14dp" />

        </LinearLayout>


    </LinearLayout>

    <!--接通后显示-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:id="@+id/btn_callone"
        android:visibility="gone"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="80dp"
        android:gravity="center"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/btn_maike"
                    android:layout_width="@dimen/tonghuabtn"
                    android:layout_height="@dimen/tonghuabtn"
                    android:background="#666666"
                    android:gravity="center">

                    <com.view.RockTextViewIcon
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/btn_maikeicon"
                        android:text="@string/icon_maikf"
                        android:textColor="@color/white"
                        android:textSize="40dp" />

                </LinearLayout>

                <TextView
                    android:id="@+id/btn_maikemsg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="麦克风已开"
                    android:textColor="#aaaaaa"
                    android:textSize="14dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:visibility="gone"
                android:id="@+id/btn_quxiaom"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/btn_quxiao"
                    android:layout_width="@dimen/tonghuabtn"
                    android:layout_height="@dimen/tonghuabtn"
                    android:background="#D9534F"
                    android:gravity="center">

                    <com.view.RockTextViewIcon
                        android:id="@+id/btn_quxiaocon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_phone"
                        android:textColor="@color/white"
                        android:textSize="40dp" />


                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:id="@+id/btn_quxiaomsg"
                    android:text="取消"
                    android:textColor="#aaaaaa"
                    android:textSize="14dp" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/btn_yangsq"
                    android:layout_width="@dimen/tonghuabtn"
                    android:layout_height="@dimen/tonghuabtn"
                    android:background="#666666"
                    android:gravity="center">

                    <com.view.RockTextViewIcon
                        android:id="@+id/btn_yangsqicon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_sheng"
                        android:textColor="@color/white"
                        android:textSize="40dp" />


                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:id="@+id/btn_yangsqmsg"
                    android:text="扬声器已开"
                    android:textColor="#aaaaaa"
                    android:textSize="14dp" />

            </LinearLayout>



            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:id="@+id/btn_vadiom"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/btn_vadio"
                    android:layout_width="@dimen/tonghuabtn"
                    android:layout_height="@dimen/tonghuabtn"
                    android:background="#666666"
                    android:gravity="center">

                    <com.view.RockTextViewIcon
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/btn_vadioicon"
                        android:text="@string/icon_vadio"
                        android:textColor="@color/white"
                        android:textSize="40dp" />


                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:id="@+id/btn_vadiomsg"
                    android:text="摄像头已开"
                    android:textColor="#aaaaaa"
                    android:textSize="14dp" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:id="@+id/btn_shipinm"
            android:visibility="gone"
            android:orientation="horizontal"
            android:gravity="center"
            >

            <LinearLayout
                android:layout_width="@dimen/tonghuabtn"
                android:layout_height="@dimen/tonghuabtn"
                android:gravity="center"
                >


            </LinearLayout>

            <LinearLayout
                android:layout_width="@dimen/tonghuabtn"
                android:layout_height="@dimen/tonghuabtn"
                android:background="#D9534F"
                android:id="@+id/btn_cancel"
                android:layout_marginLeft="40dp"
                android:gravity="center"
                >
                <com.view.RockTextViewIcon
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="40dp"
                    android:text="@string/icon_phone"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="@dimen/tonghuabtn"
                android:layout_height="@dimen/tonghuabtn"
                android:id="@+id/btn_zhuan"
                android:layout_marginLeft="40dp"
                android:gravity="center"
                >
                <com.view.RockTextViewIcon
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="20dp"
                    android:text="@string/icon_zhuan"/>

            </LinearLayout>


        </LinearLayout>



    </LinearLayout>

    <!--被叫时接通/拒绝-->
    <LinearLayout
            android:id="@+id/btn_calljie"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:visibility="gone"
            android:layout_marginBottom="80dp"
            android:gravity="center"
            android:orientation="horizontal">


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/btn_jujue"
                    android:layout_width="@dimen/tonghuabtn"
                    android:layout_height="@dimen/tonghuabtn"
                    android:background="#D9534F"
                    android:gravity="center">

                    <com.view.RockTextViewIcon
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_phone"
                        android:textColor="@color/white"
                        android:textSize="40dp" />


                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="拒绝"
                    android:textColor="#aaaaaa"
                    android:textSize="14dp" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="70dp"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/btn_tongyi"
                    android:layout_width="@dimen/tonghuabtn"
                    android:layout_height="@dimen/tonghuabtn"
                    android:background="#339933"
                    android:gravity="center">

                    <com.view.RockTextViewIcon
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_phonej"
                        android:textColor="@color/white"
                        android:textSize="40dp" />


                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="接听"
                    android:textColor="#aaaaaa"
                    android:textSize="14dp" />

            </LinearLayout>


        </LinearLayout>

    <!--预览自己的头像-->
    <LinearLayout
            android:layout_width="150dp"
            android:layout_height="200dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="50dp"
            android:id="@+id/txcvv_main_localv"
            android:layout_marginRight="10dp"
            android:orientation="horizontal"
            >
        </LinearLayout>


</RelativeLayout>