var touchobj=false;
var strformat = {
	sendcodearr:{},
	sendcuxo:0,
	emotsstr:',[微笑],[撇嘴],[色],[发呆],[得意],[流泪],[害羞],[闭嘴],[睡],[大哭],[尴尬],[发怒],[调皮],[呲牙],[惊讶],[难过],[酷],[冷汗],[抓狂],[吐],[偷笑],[愉快],[白眼],[傲慢],[饥饿],[困],[恐惧],[流汗],[憨笑],[悠闲],[奋斗],[咒骂],[疑问],[嘘],[晕],[疯了],[衰],[骷髅],[敲打],[再见],[擦汗],[抠鼻],[鼓掌],[糗大了],[坏笑],[左哼哼],[右哼哼],[哈欠],[鄙视],[委屈],[快哭了],[阴险],[亲亲],[吓],[可怜],[菜刀],[西瓜],[啤酒],[篮球],[乒乓],[咖啡],[饭],[猪头],[玫瑰],[凋谢],[嘴唇],[爱心],[心碎],[蛋糕],[闪电],[炸弹],[刀],[足球],[瓢虫],[便便],[月亮],[太阳],[礼物],[拥抱],[强],[弱],[握手],[胜利],[抱拳],[勾引],[拳头],[差劲],[爱你],[NO],[OK],[爱情],[飞吻],[跳跳],[发抖],[怄火],[转圈],[磕头],[回头],[跳绳],[投降],[激动],[街舞],[献吻],[左太极],[右太极]',
	addcode:function(key, val){
		this.sendcuxo++;
		key	= key+','+this.sendcuxo;
		this.sendcodearr[key] = val;
		return '[C]'+key+'[/C]'
	},
	geturl:function(d){
		if(!d)d={'url':''};
		var url = d.url;
		if(!url&&d.table&&d.mid)url='?m=flow&a=view&d=taskrun&table='+d.table+'&mid='+d.mid+'&uid='+adminid+'';
		return url;
	},
	emotspath:'',
	openurl:function(dz){
		js.location(dz);
	},
	urlpipei:function(str){
		urlpipeiarr = [];
		var strv = this.urlpipeiss(str,'http://');
		strv = this.urlpipeiss(strv,'https://');
		var i,len = urlpipeiarr.length,sv;
		if(len>0)for(i=0;i<len;i++){
			sv   = urlpipeiarr[i];
			strv = strv.replace('{URL'+i+'}','<a onclick="return strformat.openurl(\''+sv+'\')" href="javascript:;">'+sv+'</a>');
		}
		return strv;
	},
	urlpipeiss:function(str,gz){
		var s1 = str.toLowerCase();
		var xu = s1.indexOf(gz);
		if(xu==-1)return str;
		var len = gz.length,zlen = s1.length;
		var jg = 0,s2,i,ym,tsx=',./?&=#@*()_-+;!|:%',cd;
		for(i=0;i<zlen-len;i++){
			s2 = s1.substr(xu+len+i,1);
			if(!s2 || (tsx.indexOf(s2)==-1 && !/[a-zA-Z0-9]{1,}/.test(s2)))break;
			jg++;
		}
		ym = str.substr(xu,len+jg);
		cd = urlpipeiarr.push(ym);
		var strv= str.replace(ym,'{URL'+(cd-1)+'}');
		return this.urlpipeiss(strv,gz);
	},
	strcont:function(nr){
		var str = unescape(nr),patt1,emu,i,st1,oi;
		
		if(str.indexOf('<img')==-1)str = this.urlpipei(str);
		
		patt1	= new RegExp("\\[(.*?)\\](.*?)", 'gi');
		emu		= str.match(patt1);
		if(emu!=null){
			for(i=0;i<emu.length; i++){
				st1=emu[i];
				oi=this.emotsarrss[st1];
				if(oi)str	= str.replace(st1, '<img height="30" width="30" src="'+this.emotspath+'images/im/emots/qq/'+(oi-1)+'.gif">');
			}
		}
		str	= str.replace(/\n/gi, '<br>');
		return str;
	},
	openurl:function(sid){
		xcy.dakaiurl('new',sid);
		return false;
	},
	strcontss:function(str,bq,rstr){
		var patt1	= new RegExp("\\["+bq+"\\](.*?)\\[\\/"+bq+"\\]", "gi");
		var emu		= str.match(patt1);
		if(emu != null){
			bq1	= bq.toLowerCase();
			for(var i=0;i<emu.length; i++){
				var s0	= emu[i].replace('['+bq+']','').replace('[/'+bq+']','');
				s0		= s0.replace('['+bq1+']','').replace('[/'+bq1+']','');
				var s1	= s0,s2 = s0,s3='',sa;
				if(s0.indexOf('|')>0){
					sa = s0.split('|');
					s1 = sa[1];
					s2 = sa[0];
					s3 = sa[2];
				}
				var s4	= rstr.replace('{s1}',s1).replace('{s2}',s2).replace('{s3}',s3);
				str		= str.replace(emu[i], s4);
			}
		}
		return str;
	},
	sendinstr:function(str, tuas){
		var bq		= 'C';
		var patt1	= new RegExp("\\["+bq+"\\](.*?)\\[\\/"+bq+"\\]", "gi");
		var emu		= str.match(patt1);
		
		if(emu != null){
			for(var i=0;i<emu.length; i++){
				var s0	= emu[i].replace('['+bq+']','').replace('[/'+bq+']','');
				str		= str.replace(emu[i], this.sendcodearr[s0]);
			}
		}
		var nowa	= js.serverdt('Y-m-d H:i:s 星期W'),
			nowas	= nowa.split(' ');
		var ztstr	= [['now',nowa],['date',nowas[0]],['time',nowas[1]],['week',nowas[2]]];
		var patt1,a,thnr,ths='';
		for(var i=0; i<ztstr.length; i++){
			a	=	ztstr[i];
			if(a[2] == 1){
				patt1	= new RegExp(""+a[0]+"", "gi");
				thnr	= '[A]'+a[0]+'|'+a[1]+'[/A]';
			}else{
				thnr	= a[1];
				patt1	= new RegExp("\\["+a[0]+"\\]", "gi");
			}
			str	= str.replace(patt1, thnr);
		}
		return str;
	},
	picshow:function(str, wj){
		var s=str,sa;
		if(s.indexOf('[图片.')==0){
			s=s.substr(1,s.length-1);
			sa=s.split('.');
			if(wj)s='<img src="'+apiurl+''+wj+'">';
		}
		return s;
	},
	showdt:function(sj){
		if(!sj)sj='';
		var s='';
		sja=sj.split(' ');
		if(sj.indexOf(this.dt)==0){
			s=sja[1];
		}else{
			s=sj.substr(5,11);
		}
		return s;
	},
	showqp:function(type,name,dt,cont,nuid, fase,rnd,bqname,bqcor){
		var str = this.strcont(cont);
		if(!rnd)rnd=js.getrand();
		var nr	= '',bqs='';
		if(bqname && bqcor)bqs='<font style="background:'+bqcor+';font-size:10px;margin-right:2px;color:white;padding:1px 2px;border-radius:2px" >'+bqname+'</font>';
		this.showqpid = 'ltcont_'+rnd+'';
		nr+='<div id="'+this.showqpid+'" class="ltcont">';
		nr+='	<div class="qipao" align="'+type+'">';
		nr+='		<div class="dt" style="padding-'+type+':65px">'+bqs+'<font id="ltname_'+rnd+'">'+name+'</font>('+this.showdt(dt)+')</div>';
		
		nr+='		<table border="0" cellspacing="0" cellpadding="0">';
		
		nr+='		<tr valign="top">';
		if(type == 'left'){
			nr+='			<td width="50" align="center"><img src="'+fase+'" class="qipaoface r-face" width="40" height="40" onclick="strformat.clickface(this,\''+rnd+'\')"></td>';
			nr+='			<td><div class="qipao'+type+'"></div></td>';
		}else{
			nr+='			<td width="50" id="qipaomsg_'+rnd+'" align="right">';
			if(nuid)nr+='<img src="images/loadings.gif" title="发送中..." id="'+nuid+'" style="margin-top:5px" align="absmiddle">&nbsp;';
			nr+='			</td>';
		}
		
		nr+='			<td>';
		nr+='			<div ontouchstart="touchobj=this" id="qipaocont_'+rnd+'" rand="'+rnd+'" class="qipaocont qipaocont'+type+'">'+str+'</div>';
		nr+='			</td>';
		
		if(type == 'right'){
			nr+='			<td><div class="qipao'+type+'"></div></td>';
			nr+='			<td width="50" align="center"><img src="'+fase+'" class="qipaoface r-face" width="40" height="40" onclick="strformat.clickface(this,\''+rnd+'\')"></td>';
		}else{
			nr+='			<td width="50"  id="qipaomsg_'+rnd+'"></td>';
		}
		
		nr+='		</tr></table>';
		nr+='	</div>';
		nr+='</div>';
		return nr;
	},
	showupfile:function(f, snr){
		var nuid= js.now('time'),optdt = js.serverdt(),nr='';
		nr = '<div id="showve_'+nuid+'">';
		if(f && f.filename){
			if(f.isimg){
				var src = ''+this.emotspath+'images/noimg.jpg';
				if(f.thumbpath)src = ''+apiurl+''+f.thumbpath+'';
				if(f.imgviewurl)src = f.imgviewurl;
				nr+='<div><img width="150" id="imgview_'+nuid+'" src="'+src+'"><br>'+f.filesizecn+'</div>';
			}else if(f.fileext=='amr'){
				nr+='<div><i class="icon-volume-up"></i> '+(parseInt(f.filesize/1500))+'"</div>';
			}else{
				nr+= '<div><img src="'+this.emotspath+'images/fileicons/'+js.filelxext(f.fileext)+'.gif" align="absmiddle">&nbsp;'+f.filename+'('+f.filesizecn+')</div>';
			}
		}
		if(snr){
			nr+= '<div><img src="'+snr+'" id="jietuimg_'+nuid+'" width="150"></div>';
			//nr+= '<div><a onclick="im.upbase64(\''+nuid+'\')" href="javascript:;">[发送截图]</a>';
		}
		nr+= '<div class="progresscls"><div id="progresscls_'+nuid+'" class="progressclssse"></div><div class="progressclstext"  id="progresstext_'+nuid+'">0%</div></div>';
		nr+= '<div id="progcanter_'+nuid+'"><a href="javascript:;" onclick="strformat.cancelup(\''+nuid+'\')">取消</a></div>';
		nr+= '</div>';
		this.nuidup_tep = nuid;
		var nas = f.sendname;
		if(!nas)nas='我';
		var cont= this.showqp('right',nas,optdt, nr, nuid, f.face, nuid,f.bqname,f.bqcolor);
		return {'cont':cont,optdt:optdt,nuid:nuid};
	},
	upprogresss:function(per, nuid){
		if(!nuid)nuid=this.nuidup_tep;
		$('#progresscls_'+nuid+'').css('width',''+per+'%');
		$('#progresstext_'+nuid+'').html(''+per+'%');
		if(per==100)$('#progcanter_'+nuid+'').remove();
	},
	upsuccess:function(f,nuid){
		if(!nuid)nuid=this.nuidup_tep;
		this.upprogresss(100, nuid);
		$('#progresstext_'+nuid+'').html('上传成功');
		var contss;
		if(js.isimg(f.fileext)){
			contss = '[图片 '+f.filesizecn+']';
		}else{
			contss = '['+f.filename+' '+f.filesizecn+']';
		}
		var s = this.contshozt(f, nuid);
		$('#showve_'+nuid+'').html(s);
		return contss;
	},
	uperror:function(nuid){
		if(!nuid)nuid=this.nuidup_tep;
		$('#progresstext_'+nuid+'').html('<font color=red>上传失败</font>');
		$('#progcanter_'+nuid+'').remove();
	},
	cancelup:function(nuid){
		if(!nuid)nuid=this.nuidup_tep;
		try{if(this.upobj)this.upobj.abort();}catch(e){}
		$('#ltcont_'+nuid+'').remove();
		if(apixhbool)api.rockFun('uploadcancel');
	},
	openimg:function(src){
		var img = src;
		if(src.indexOf('thumb')>0){
			var ext = src.substr(src.lastIndexOf('.')+1);
			img = src.substr(0,src.lastIndexOf('_'))+'.'+ext;
		}
		js.open(img);
	},
	clickface:function(o1){
	},
	emotsarrss:{},
	init:function(){
		var a = this.emotsstr.split(',');
		this.emotsarr=a;
		var len = a.length,i;
		for(i=1;i<len;i++){
			this.emotsarrss[a[i]]=i;
		}
		this.dt=js.now();
	},
	contshozt:function(d, nuid){
		var s='',slx,sttr;
		if(!d)return s;
		if(!d.fileid)d.fileid=d.id;
		if(js.isimg(d.fileext)){
			sttr='';
			if(d.thumbpath){
				s='<img src="'+d.thumbpath+'" style="max-width:200px" onclick="strformat.openimg(this, \''+d.fileid+'\')">';
			}else{
				if(d.width){
					if(d.width>150)sttr='width="150"';
				}else{
					sttr='width="150"';
				}
				s='<img src="'+d.filepath+'" '+sttr+'  onclick="strformat.openimg(this, \''+d.fileid+'\')">';
			}
		}else if(d.filename.indexOf('rockyuyin')==0){ 
			var miao= this.getmiao(d.filename)
			if(!miao)miao = parseInt(d.filesize/1500)
			s='<div class="notselect" onclick="strformat.openfile(\''+d.fileid+'\',\''+d.fileext+'\',\''+nuid+'\')"><i class="icon-volume-up"></i> '+miao+'"</div>';
		}else{
			slx = d.fileext;
			if(js.fileall.indexOf(','+slx+',')<0)slx='wz';
			s=''+d.filename+'<br><span style="font-size:14px;color:#888888">'+d.filesizecn+'</span>';
			s='<table class="notselect" onclick="strformat.openfile(\''+d.fileid+'\',\''+d.fileext+'\',\''+nuid+'\')"><tr><td><div class="qipaofile">'+d.fileext.toUpperCase()+'</div></td><td>'+s+'</td></tr></table>';
		}
		return s;
	},
	openfile:function(){
		js.msg('msg','未开发打开文件');
	},
	openimg:function(){
		js.msg('msg','未开发打开图片');
	},
	getmiao:function(name){
		var arr = name.split('_')
		if (!arr[1])return 0;
		arr =  arr[1].split('.')
		return arr[0];
	}
}
strformat.init();