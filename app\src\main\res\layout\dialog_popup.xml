<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:gravity="right"
    android:orientation="vertical">

  <View
      android:layout_width="12dp"
      android:layout_height="12dp"
      android:layout_marginRight="16dp"
      android:background="#5C5C5C"
      android:layout_marginTop="3dp"
      android:layout_alignParentRight="true"
      android:rotation="45" />

  <LinearLayout
      android:id="@+id/dialog_popup_main"
      android:layout_alignParentRight="true"
      android:layout_width="180dp"
      android:layout_marginTop="8dp"
      android:layout_marginRight="2dp"
      android:layout_height="wrap_content"
      android:background="@drawable/dialog_popup_bg"
      android:orientation="vertical"
      >

    <!--
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="15dp"
        android:text="这是一个菜单"
        android:textColor="@color/white" />
        -->

  </LinearLayout>

</RelativeLayout>