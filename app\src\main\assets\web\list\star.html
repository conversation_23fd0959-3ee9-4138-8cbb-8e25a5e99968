<link rel="stylesheet" href="res/css/chat.css"/>
<script src="js/strformat.js"></script>
<script>
$(document).ready(function(){
	{params}
	
	var c = {
		
		init:function(){
			this.type = params.type;
			this.gid = params.gid;
			//var str = '<span clickevt="addnew" style="width:auto;padding:0px 10px;font-size:14px" class="r-position-right r-header-btn">管理</span>';
			//$('#header_show').append(str);
			this.starisguan = false;
			this.loaddata();
			
			strformat.openimg=function(o1,fid1){
				
			};
			strformat.openfile=function(cs1,cs2,cs3){
				
			};
		},
		loaddata:function(){
			js.loading('加载中...');
			js.ajax('reim|getstar',{},function(ret){
				c.showstarshow(ret.data);
			});
		},
		showstarshow:function(a){
			this.starlistarr = a;
			var i,len=a.length,s='',oi=0,s1='',s2='',zx=0,d1;
			s+='';
			for(i=0;i<len;i++){
				if(!a[i])continue;
				oi++;
				d1= js.decode(jm.base64decode(a[i].value));
				s1= jm.base64decode(d1.cont);
				if(d1.fileid>0){
					if(this.type=='gout' || this.type=='uout'){
						if(d1.type=='user' || d1.type=='group')continue
					}
					if(this.type=='user' || this.type=='group'){
						if(d1.type=='gout' || d1.type=='uout')continue
					}
				}
				if(d1.imgurl){
					s2+='<div  clickevt="clickcont,'+i+'" class="rock_grid rock_access" style="width:25%;padding:10px 0px"><div class="rock_grid__icon"><img src="'+xcy.getface(d1.imgurl)+'"></div></div>';
					continue;
				}
				if(d1.filename){
					if(d1.filenum)d1.fileid = d1.filenum;
					s1=strformat.contshozt(d1);
				}
				s+='<div class="rock_cell rock_cell_access" clickevt="clickcont,'+i+'">';
				s+='<div class="rock_cell__bd">'+s1+'</div>';
				s+='</div>';
			}
			$('#imglist').html(s2);
			$('#liststre').html(s);
			js.initbtn(this);
			var o1 = $('#historylist_tems');
			o1.hide();
			if(s2=='' && s=='')o1.show();
		},
		addnew:function(o1){
			if(!this.starisguan){
				this.starisguan = true;
				$(o1).html('退出管理')
			}else{
				this.starisguan = false;
				$(o1).html('管理')
			}
		},
		raloads:function(){
			
			this.loaddata();
		},
		clickcont:function(o1,i){
			var d1 = this.starlistarr[i];
			var d  = js.decode(jm.base64decode(d1.value));
			if(!this.starisguan){
				d.type = this.type;
				d.gid = this.gid;
				xcy.sendEvent('star', 'rockchat', d);
				xcy.back();
			}else{
				rockconfirm('确定要删除此收藏吗？', function(jg){
					if(jg=='yes'){
						c.starlistarr[i]=false;
						$(o1).remove();
						js.ajax('reim|delstar',{id:d1.id});
					}
				});
			}
		}
	}
	c.init();
});
</script>
<div style="height:15px"></div>
<div id="imglist" style="background:white" class="rock_grids"></div>
<div class="rock_cells qipao" id="liststre"></div>

<div id="historylist_tems" style="padding:40px 10px;text-align:center;color:#cccccc;display:none">暂无收藏</div>

<div class="rock_footer" style="margin-top:20px">
	<p class="rock_footer__links">
		<a href="javascript:;" class="zhu" clickevt="raloads" class="rock_footer__link"><i class="icon-spinner"></i> 刷新</a>
		&nbsp;&nbsp;
		<a href="javascript:;" class="zhu"  clickevt="addnew" class="rock_footer__link">管理</a>
	</p>
</div>
<div class="blank15"></div>