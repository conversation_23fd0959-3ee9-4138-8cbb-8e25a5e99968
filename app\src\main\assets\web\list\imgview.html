<script>
$(document).ready(function(){
	{params}
	
	var c={
		init:function(){
			$('#header').hide();
			$('body').css('background','#000000');
		},
		initapp:function(){
			var src = params.src;
			photoBrowser = api.require('photoBrowser');
			photoBrowser.open({
				images: [src],
				bgColor: '#000'
			}, function(ret, err) {
				if(ret.eventType=='click'){
					api.closeWin();
				}
			});
		}
	}
	
	c.init();
	
	xcy.initApp=function(){
		initAppbool = true;
		c.initapp();
	}
});
</script>