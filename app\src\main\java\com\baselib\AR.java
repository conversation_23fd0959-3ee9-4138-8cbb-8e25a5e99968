/**
 * 说明：Android基础使用
 * 创建：雨中磐石  from www.rili123.cn/xh829.com
 * 时间：2014-11-28
 * 邮箱：<EMAIL>
 * QQ：290802026
 * */

package com.baselib;

import android.content.Context;
import android.content.res.Resources;

import com.rock.xinhuoanew.BuildConfig;


public final class AR{

	public static String packagename = BuildConfig.APPLICATION_ID;
	public static Resources resources;

	public static void setContext(Context context)
	{
		resources 	= context.getResources();
		packagename = context.getPackageName();
	}

	/**
	 * 获取布局id
	 * */
	public static int getlayoutID(String key)
	{
		return resources.getIdentifier(key, "layout", packagename);
	}

	/**
	 * 获取定义id
	 * */
	public static int getID(String key)
	{
		return resources.getIdentifier(key, "id", packagename);
	}

	/**
	 * 获取图片id
	 * */
	public static int getmipmapID(String key)
	{
		return resources.getIdentifier(key, "mipmap", packagename);
	}

	/**
	 * 获取颜色id
	 * */
	public static int getcolorID(String key)
	{
		return resources.getIdentifier(key, "color", packagename);
	}

	/**
	 * 获取预定字符串
	 * */
	public static String getString(String key)
	{
		int id = resources.getIdentifier(key, "string", packagename);
		return resources.getString(id);
	}

}