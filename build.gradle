// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        jcenter()
        google()
        mavenCentral()
        maven {url 'https://developer.huawei.com/repo/'}
        maven {
            url "https://maven.aliyun.com/nexus/content/repositories/releases"
        }

    }
    dependencies {
        classpath "com.android.tools.build:gradle:7.0.1"
        classpath "com.huawei.agconnect:agcp:1.6.0.300"
        classpath "com.android.tools.build:gradle:7.2.1"  // 检查版本号
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
