<script>
$(document).ready(function(){
	var im = {
		splittime:10,
		init:function(){
			
			var str = js.getoption('agentjson');
			if(str)xcy.agentarr = js.decode(str);

			str = js.getoption('historyjson');
			if(str)xcy.historyarr = js.decode(str);
			
			this.showbart();	
			this.reloadss();
			this.showagent(xcy.agentarr);
			this.showhistory(xcy.historyarr);
			js.setoption('tonghuachannel', '');
			var str = js.getoption('silderarr');
			if(str)this.showsilder(js.decode(str));
			
		},
		showbart:function(){
			if(get('footerdivss'))return;
			var str = '<div class="rock_tabbar" ontouchstart="return false" id="footerdivss" style="position:fixed;width:100%;bottom:0px;z-index:80;">'+
				'<div ontouchend="return xcy.changetab(0)" id="tabzhu0" class="rock_tabbar__item rock_bar__item_on">'+
				'	<span style="display: inline-block;position: relative;">'+
			'			<div class="rock_tabbar__icon"><i class="iconfont iconmy-chat"></i></div>'+
				'		<span class="rock_badge" id="stotal_ss0" style="position: absolute;top: -2px;right: -15px;"></span>'+
				'	</span>'+
				'	<p class="rock_tabbar__label">'+tabhomearr[0]+'</p>'+
				'</div>'+
				'<div ontouchend="return xcy.changetab(1)" id="tabzhu1" class="rock_tabbar__item">'+
				'	<span style="display: inline-block;position: relative;">'+
			'		<div class="rock_tabbar__icon"><i class="iconfont iconmy-ying"></i></div>'+
				'		<span class="rock_badge" id="stotal_ss1" style="position: absolute;top: -2px;right: -15px;"></span>'+
				'	</span>'+
				'	<p class="rock_tabbar__label">'+tabhomearr[1]+'</p>'+
				'</div>'+
				'<div ontouchend="return xcy.changetab(2)" id="tabzhu2" class="rock_tabbar__item">'+
			'		<div class="rock_tabbar__icon"><i class="iconfont iconmy-tong"></i></div>'+
				'	<p class="rock_tabbar__label">'+tabhomearr[2]+'</p>'+
				'</div>'+
				'<div ontouchend="return xcy.changetab(3)" id="tabzhu3" class="rock_tabbar__item">'+
			'		<div class="rock_tabbar__icon"><i class="iconfont iconmy-user"></i></div>'+
				'<p class="rock_tabbar__label">'+adminname+'</p>'+
				'</div>'+
			'</div>';
			$('#mainbody').after(str);
			if(js.getoption('lxrshow')==2)$('#tabzhu2').hide();
			xcy.otherheight=$('#footerdivss').height();
			xcy.resiezess();
            xcy.addtabs('首页','shouye') //强制设为首页
		},
		reloadss:function(){
			js.ajax('indexreim|mwebinit',false,function(da){
				var ret = da.data;
				im.reloadshow(ret);
			}, 'get', function(){
				xcy.showerror();
			});
		},
		reloadshow:function(ret){
			this.loadtime = ret.loadtime;
			if(!this.loadtime){
				js.msg('msg','不是最新系统，可能会影响APP正常使用');
			}
			if(!ret.tonghuabo)ret.tonghuabo = '2';
			js.setoption('agentjson', ret.agentjson);
			js.setoption('historyjson', ret.historyjson);
			js.setoption('myhomenum', ret.myhomenum);
			js.setoption('lxrshow', ret.lxrshow);
			if(ret.config)js.setoption('chehui',ret.config.chehui);
			(ret.lxrshow==2) ? $('#tabzhu2').hide() : $('#tabzhu2').show();
			if(typeof(ret.outgroupopen)=='string')js.setoption('outgroupopen', ret.outgroupopen);
			js.setoption('tonghuabo', ret.tonghuabo);
			if(ret.silderarr){
				var str = '';
				if(ret.silderarr.length>0)str=JSON.stringify(ret.silderarr);
				js.setoption('silderarr', str);
				this.showsilder(ret.silderarr);
			}
			if(ret.loaddt){
				js.servernow = ret.loaddt;
				js.getsplit();
			}
			xcy.agentarr = js.decode(ret.agentjson);
			this.showagent(xcy.agentarr);
			xcy.historyarr = js.decode(ret.historyjson);
			this.showhistory(xcy.historyarr);
			xcy.reloadok();
			if(ret.editpass==0){
				js.wx.alert('请先修改密码后在使用', function(){
					xcy.opennei({name:'修改密码','url':'editpass',type:1});
				});
			}else{
				if(apixhbool)this.splittime = 120;
				this.timeajaxs();
				this.initWebsockt(ret.config);
				this.initPush();
				this.outgroupload();
				this.timerAlarm();
			}
		},
		outgroupload:function(){
			xcy.outgroup.loaddata(this,0);
		},
		pushListener:function(ret){
			if(ret.name!='rockhome')return;
			var slx = ret.stype;
			if(slx=='runtimer')this.timeload(true);
			if(slx=='reload')this.timeload(true);
			if(slx=='onstop')windowfocus=false;
			if(slx=='onresume'){
				windowfocus=true;
				nowchat='';
				xcy.cancelnotif();
				this.timeload(true);
			}
			if(slx=='biaoyd')this.biaoyidus(ret.type,ret.gid);
			if(slx=='nowchat')nowchat = ret.nowchat;
			if(slx=='addout')this.showaddout();
			if(slx=='websocket')this.websocketData(ret);
			if(slx=='regout')this.outgroupload();
            if(slx=='longmenu')xcy.longmenu();
			if(slx=='resize')xcy.resiezess();
			if(slx=='jpush')this.initPushback(ret);;
		},
		initWebsockt:function(ret){
			this.reimconfig = ret;
			if(!apixhbool || xcy.initPushBool)return;
			if(ret && ret.recid && ret.wsurl && ret.appwx=='1'){
				ret.adminid 	= adminid;
				ret.adminname 	= adminname;
				ret.actionname	= 'rockhome';
				if(api.startWebsocket){
					api.startWebsocket(ret);
				}else{
					api.rockFun('startWebsocket',ret);
				}
			}else{
				api.rockFun('startService',{
					time: '10',
					name:'rockhome'
				});
				clearTimeout(xcy.homeretime);
			}
		},
		websocketreStart:function(){
			xcy.initPushBool = false;
			xcy.startTimes = setTimeout(function(){im.initWebsockt(im.reimconfig);},8*1000);
		},
		websocketreOpen:function(){
			xcy.initPushBool = true;
			clearTimeout(xcy.startTimes);
		},
		websocketData:function(ret){
			var slx = ret.sockettype;
			if(slx=='open')this.websocketreOpen();
			if(slx=='close')this.websocketreStart();
			if(slx=='message'){
				var da = ret.socketdata;
				if(da.msgtype=='calltonghua'){
					this.calltonghua(da);
					return;
				}
				this.splittime = 120;
				this.timeload(true);
				xcy.sendEvent('reload','rockchat', da);
			}
		},
		calltonghua:function(d){
			//iphone端才需要处理
			if(isphone)api.rockFun('calltonghua',d);
		},
		timeajaxs:function(){
			clearTimeout(xcy.homeretime);
			xcy.homeretime=setTimeout(function(){im.timeload(true)},this.splittime*1000);
		},
		timeload:function(bo){
			if(this.loadtime)js.ajax('indexreim|loadhit', {time:this.loadtime}, function(da){
				var ret = da.data;
				im.loadtime = ret.loadtime;
				if(ret.rows.length>0){
					im.shownewhistory(ret.rows);
				}
			},'get', function(){
				js.msg();
			});
			if(bo)this.timeajaxs();
		},
		showsilder:function(a){
			if(this.showsilderobj)this.showsilderobj.remove();
			if(!a || a.length==0)return;
			this.showsilderobj = $.rocksilder({
				view:'home_silder',
				titlebool:true,
				data:a,
				onclick:function(d){
					xcy.openurls(d.url);
				}
			});
		},
		shownewhistory:function(a){
			var i,len=a.length,d=false;
			for(i=0;i<len;i++){
				if(!d && a[i].stotal>0)d=a[i];
				this.showhistorys(a[i], true);
			}
			this.changestotl(false,0);
			if(i>0)$('#historylist_tems').hide();
			if(!d)return;
			xcy.sendEvent('newmess','rockchat', {type:d.type,gid:d.receid});
			
			//任务栏通知进入后台windowfocus
			var tontzi = false,nr = jm.base64decode(d.cont),typelx=''+d.type+''+d.receid+'';
			if(nr=='[语音通话]'|| nr=='[视频通话]' || typelx==nowchat)return;
			if(nowurl=='home' && !nowchat && windowfocus)return;
			if(apixhbool){
				api.Notification({
					title:d.name,
					msg:nr
				});
				tontzi = true;
			}
			if(!tontzi && nowurl!='home'){
				rockconfirm(nr, function(jg){
					if(jg=='yes')im.openguser(d.type, d.receid);
				}, d.name);
			}
		},
		showagent:function(d){
			var stotal=0,i,j=0,str,zs=yingshu,nar=[];
			var myhomenum = ','+js.getoption('myhomenum')+',';
			$('#home_yinglistdiv').hide();
			$('#home_yinglist').html('');
			str='<div style="border-bottom:0.5px '+bordercolor+' solid;border-top:0.5px '+bordercolor+' solid"><table width="100%" ><tr>';
			for(i=0;i<d.length;i++){
				stotal+=parseFloat(d[i].stotal);
				agentarr[d[i].id]=d[i];
				if(myhomenum.indexOf(','+d[i].num+',')>-1)nar.push(d[i]);
			}
			var len = nar.length;
			for(i=0;i<len;i++){
				j++;
				str+=this.showagents(nar[i],zs,j,len);
				if(j%zs==0)str+='</tr><tr>';
			}
			
			if(stotal==0)stotal='';
			$('#stotal_ss1').html(''+stotal);
			if(j>0 && j<zs)str+='<td>&nbsp;</td>';
			str+='</tr></table></div>';
			if(j>0){
				$('#home_yinglistdiv').show();
				$('#home_yinglist').html(str);
			}
		},
		showagents:function(d,zs,j,len){
			var rnd= 'a'+js.getrand();
			xcytouch[rnd] = new touchclass({
				receid:d.id,num:d.num,
				onlongclick:function(){im.showagentsmenu(this.num,this.receid,this.obj);},
				onclick:function(){im.openguser('agenk',this.receid);}
			});
			var s='',hei=34,col=bordercolor,w=100/zs,bt='.5',br='.5',sy=len%zs;
			if(j%zs==0)br='0';
			if(sy>0 && len-j<sy)bt='0';
			if(sy==0 && len-j<zs)bt='0';
			if(zs<4)hei=40;
			var s1=d.stotal;if(s1==0)s1='';
			s='<td class="actives" ontouchstart="return xcytouch.'+rnd+'.touchstart(this,event)" style="width:'+w+'%;padding:16px 5px;position:relative;border-bottom:'+bt+'px '+col+' solid;border-right:'+br+'px '+col+' solid" align="center">';
			s+='<div style="height:'+hei+'px;overflow:hidden;">';
			s+='	<img src="'+xcy.getface(d.face, this)+'" style="border-radius:6px;height:'+hei+'px;width:'+hei+'px">';
			s+='	<span style="position:absolute;top:3px;right:3px;" id="histotal_agenk_'+d.id+'" class="rock_badge">'+s1+'</span>';
			s+='</div>';
			s+='<div style="font-size:16px;margin-top:3px;">';
			s+=' '+d.name+'';
			s+='</div>';
			s+='</td>';
			return s;
		},
		showagentsmenu:function(num,id,o1){
			if(apixhbool)api.createMenu({
				menu:'打开,取消首页显示|'+deletecolor+''
			},function(ret){
				if(ret.menuIndex==0)im.openguser('agenk',id);
				if(ret.menuIndex==1){
					js.ajax('indexreim|shecyy',{yynum:num},function(ret){
						$(o1).remove();
						api.toast({msg:ret.data.msg});
					});
				}
			});
		},
		showhistorydata:{},
		showhistory:function(a){
			if(!get('historylist')){
				xcy.showqipao('historyjson', '0');
				return;
			}
			var outstr = js.getoption('outgrouplist0'),i,outa,ds=[];
			for(i=0;i<a.length;i++)ds.push(a[i]);
			if(outstr){
				outa = JSON.parse(outstr);
				for(i=0;i<outa.length;i++)ds.push(outa[i]);
				ds.sort(function(d1, d2){
					if(d1.optdt > d2.optdt){
						return -1;
					}else if(d1.optdt < d2.optdt){
						return 1;
					}else{
						return 0;
					}
				});
			}
			var len=ds.length;
			$('#historylist').html('');
			$('#historylist_tems').show();
			for(i=0;i<len;i++){
				this.showhistorys(ds[i]);
			}
			this.changestotl(false,0);
			if(i>0)$('#historylist_tems').hide();
		},
		showhistorys:function(d,pad){
			var s,ty,o=$('#historylist'),d1,st,attr;
			var num = ''+d.type+'_'+d.receid+'';
			$('#index_'+num+'').remove();
			this.showhistorydata[num]=d;
			st	= d.stotal;if(st=='0')st='';
			var ops = d.optdt.substr(11,5);
			if(d.optdt.indexOf(date)!=0)ops=d.optdt.substr(5,5);
			ty	= d.type;
			var s1 = xcy.grouptype(d.deptid, ty);
			var rnd= 'a'+js.getrand();
			xcytouch[rnd] = new touchclass({
				type:ty,receid:d.receid,
				onlongclick:function(){
					im.clickitems(this.type,this.receid,this.obj);
				},
				onclick:function(){
					im.openguser(this.type,this.receid);
				}
			});
			s='<div id="index_'+num+'" class="lists" ontouchstart="return xcytouch.'+rnd+'.touchstart(this, event)" style="display:flex;align-items:center;">';
			
			s+='<div style="padding-right:10px"><div  class="img"><img style="height:50px;width:50px" src="'+xcy.getface(d.face,this)+'"></div></div>';
			s+='<div style="flex:1;overflow:hidden;">';
			s+='	<div style="display:flex;align-items:center;">';
			s+='		<div style="flex:1;" class="name fontsize">'+d.name+''+s1+'</div>';
			s+='		<span id="histotal_'+num+'" class="rock_badge">'+st+'</span>';
			s+='	</div>';
			s+='	<div style="display:flex;align-items:center;margin-top:2px;color:#bbbbbb;font-size:14px;">';
			s+='		<div style="flex:1;" class="huicont">'+jm.base64decode(d.cont)+'</div>';
			s+='		<div style="white-space:nowrap;">'+ops+'</div>';
			s+='	</div>';
			s+='</div>';
			
			s+='</div>';
			if(!pad){
				o.append(s);
			}else{
				o.prepend(s);
			}
		},
		changestotl:function(jg,lx){
			if(!jg)jg='histotal_';
			var o=$("span[id^='"+jg+"']"),oi=0,i,len=o.length,v1;
			for(i=0;i<len;i++){
				v1=$(o[i]).text();
				if(v1=='')v1='0';
				oi=oi+parseFloat(v1);
			}
			if(lx==0 && apixhbool)api.rockFun('setBadge',{num:oi});
			if(oi==0)oi='';
			$('#stotal_ss'+lx+'').html(''+oi);
		},
		clickitems:function(ty,id,o){
			var o1=$(o),num=''+ty+'_'+id+'',d=this.showhistorydata[num];
			var da = [{name:'打开',lx:0}];
			if(d && d.stotal>0)da.push({name:'标识已读',lx:2})
			da.push({name:'删除此记录|'+deletecolor+'',lx:1});
			var cans = {
				data:da,
				onclick:function(a){
					var lx=a.lx;
					if(lx==0){im.openguser(ty,id);}
					if(lx==1){
						o1.remove();
						var tst=$('#historylist').text();if(tst=='')$('#historylist_tems').show();
						var u1rl = 'reim|delhistory';
						if(ty=='gout'){
							u1rl = xcy.outgroup.geturl('delhistory');
						}
						js.ajax(u1rl,{type:ty,gid:id});
					}
					if(lx==2){im.biaoyidu(ty,id, true);}
				}
			};
			if(!apixhbool){
				js.showmenu(cans);
			}else{
				var str = '';
				for(var i=0;i<da.length;i++)str+=','+da[i].name+'';
				api.createMenu({menu:str.substr(1)},function(ret){
					var lx = 0;
					if(ret.name=='标识已读')lx=2;
					if(ret.name=='删除此记录')lx=1;
					cans.onclick({lx:lx});
				});
			}
		},
		biaoyidu:function(ty,id,lbo){
			if(lbo)this.biaoyidus(ty,id);
			var u1rl = 'reim|yiduall';
			if(ty=='gout'){
				u1rl = xcy.outgroup.geturl('yiduall');
			}
			js.ajax(u1rl,{type:ty,gid:id});
		},
		biaoyidus:function(ty,id){
			var num = ''+ty+'_'+id+'';
			$('#histotal_'+num+'').html('');
			this.changestotl(false,0);
			var d = this.showhistorydata[num];
			if(d)d.stotal = '0';
		},
		openguser:function(ty,id){
			var num = ''+ty+'_'+id+'';
			$('#histotal_'+num+'').html('');
			this.changestotl(false,0);
			var d = this.showhistorydata[num];
			if(ty=='agent' || ty=='agenk'){
				var d = this.showhistorydata[num];
				var url='';
				if(d && d.stotal>0 && !isempt(d.xgurl)){
					d.stotal = '0';
					var xga = d.xgurl.split('|');
					if(xga[1]>0)url='task.php?a=x&num='+xga[0]+'&mid='+xga[1]+'';
				}
				if(!url){
					this.openagent(id);
				}else{
					this.biaoyidu(ty,id,false);
					xcy.openurls(url);
				}
			}else{
				if(d)d.stotal = '0';
				xcy.openchat(d.name, ty, id);
			}
			
		},
		openagent:function(yyid){
			var d = agentarr[yyid];
			var url = d.urlm;
			if(!url)url = 'index.php?m=ying&d=we&num='+d.num+'';
			xcy.openurls(url+'&yingnum='+d.num+'');
		},
		unload:function(){
			
		},
		showaddout:function(){
			var isop='open';
			js.setoption('outgroupopen', isop);
			js.ajax('indexreim|openoutqun',{isop:isop},false,'get',function(){js.msg('none');});
			this.outgroupload();
		},
		saoyisaook:function(str){
			if(str.substr(0,4)!='http'){
				rockalert('无法识别扫描结果：\n'+str+'');
			}else{
				if(str.indexOf(apiurl)==0){
					xcy.openurls(str.replace(apiurl,''));
				}else{
					xcy.dakaiurl('new',str);
				}
			}
		},
		initPush:function(){
			if(!apixhbool)return;
			var shouji = api.deviceName.toLowerCase();
			if(shouji.indexOf('mi')>-1 || shouji.indexOf('huawei')>-1 || isphone){
				api.rockFun('initPush',{
					'alias':TOKEN,
				},function(ret){
					im.initPushback(ret);
				});
			}
			if(!isphone)api.rockFun('regJPush',{
			},function(ret){
				im.initPushback(ret);
			});
		},
		initPushback:function(ret){
			//showAlert(ret);
			if(ret.pushstate=='alias'){
				var ailas = ret.pushmsg;
				xcy.outgroup.uppush(ailas);
				js.ajax('index|updateTokenIp',{ispush:1,'pushtoken':ailas,'appversion':api.appVersion});
			}
			if(ret.pushstate=='regerr'){
				js.ajax('index|updateTokenIp',{ispush:0,'pushtoken':''});
			}
			if(ret.pushstate=='hwreg'){
				var token = ret.pushmsg;
				xcy.outgroup.uppush(token);
				js.ajax('index|updateTokenIp',{hwtoken:token,'pushtoken':token,ispush:1,'appversion':api.appVersion});
			}
			if(ret.pushstate=='getui'){
				var clientid = ret.clientid;
				var web = 'app2023getui'+api.deviceName+''+api.deviceModel+'';
				if(!apptypeleixing)web+='custpile';
				web 	= strreplace(web.toLowerCase());
				xcy.outgroup.uppush(clientid, web);
				js.ajax('index|updateTokenIp',{'pushtoken':clientid,ispush:1,web:web});
			}
			if(ret.pushstate=='jpush'){
				js.ajax('index|updateJPush',{'regid':ret.regid});
			}
		},
		timerAlarm:function(){
			if(!apixhbool)return;
			api.rockFun('timerAlarm',{
				'token':TOKEN,
				'adminid':adminid,
				'apiurl':apiurl
			});
		}
	}
	im.init();
	js.initbtn(im);
	xcy.touchload['home']=function(){
		im.reloadss();
	}
	xcy.unloadarr['home']=function(){
		im.unload();
	}
	im{rand}=homeim=im;
	
	xcy.changetab=function(tab, bo){
		if(this.index==tab){return;}
		var tabs=['chat','ying','shouye','user','my'];
		$('#tabzhu'+this.index+'').removeClass('rock_bar__item_on');
		$('#tabzhu'+tab+'').addClass('rock_bar__item_on');
		this.index=tab;
		js.setoption('nowindex', tab);
		if(bo)return false;
		if(tab==0)this.addtabs('','home');
		if(tab==1)this.addtabs(tabhomearr[1],'ying');
        if(tab==2)this.addtabs(tabhomearr[2],'shouye');
		if(tab==3)this.addtabs(tabhomearr[3],'lianxi');
		if(tab==4)this.addtabs(tabhomearr[4],'user');
		return false;
	}
	
	xcy.showqipao=function(lx, xu){
		var d = this.historyarr;
		if(xu==1)d = this.agentarr;
		var stotal=0;
		for(var i=0;i<d.length;i++)stotal+=parseFloat(d[i].stotal);
		var myhomenum = js.getoption('myhomenum');
		if(xu=='0' && myhomenum){
			myhomenum = ','+myhomenum+',';
			d = this.agentarr;
			for(var i=0;i<d.length;i++){
				if(myhomenum.indexOf(','+d[i].num+',')>-1){
					stotal+=parseFloat(d[i].stotal);
				}
			}
		}
		if(stotal==0)stotal='';
		$('#stotal_ss'+xu+'').html(''+stotal);
	}
	
	xcy.cancelnotif=function(){
		api.rockFun('NotificationCancel');
	}
	
	xcy.openScan=function(){
		api.openScan({},function(ret){
			im.saoyisaook(ret.result);
		});
	}
	xcy.initApp=function(){
		initAppbool = true;
		if(api.regHome)api.regHome();
		xcy.cancelnotif();
		api.addEventListener({
			name:'rockhome'
		}, function(ret){
			im.pushListener(ret);
		});
		var menustr = '扫一扫';
		if(!isphone)menustr+=',创建快捷方式';
		api.setMenu({
			menu:menustr
		},function(ret){
			if(ret.menuIndex==1)xcy.openScan();
			if(ret.menuIndex==2)api.rockFun('createShortcut');
			if(ret.menuIndex==3)js.location('http://*************/app/xinhuoa_java/app/src/main/assets/web/index.html');
		});
		api.rockFun('setOption',{value:title,key:'title'});
	};
});
</script>
<div id="home_silder"></div>
<div id="home_yinglistdiv" style="margin-bottom:15px;display:none;user-select:none">
<div style="color:#888888;padding:10px 10px 5px 10px;font-size:14px;border-bottom:0px #eeeeee solid">应用</div>
<div id="home_yinglist" style="background:white"><table border="0" width="100%"></table></div>
</div>

<div style="margin:0px;background:white;user-select:none"><div class="lista" id="historylist"></div></div>
<div id="historylist_tems" style="padding-top:50px;text-align:center;color:#cccccc;user-select:none"><span style="font-size:40px"><i class="icon-comment-alt"></i></span><br>暂无消息</div>


