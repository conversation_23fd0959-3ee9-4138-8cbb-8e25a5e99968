<script>
$(document).ready(function(){
	{params}
	
	var c = {
		init:function(){
			var bsize = js.getoption('bodyzoom');
			if(!bsize)bsize = 0;
			this.bodyzoom = bsize;
			var zys = ['正常','中号','大号'],str='',oi,sel='',xzl=false;
			for(var i=0;i<zys.length;i++){
				sel = '';
				if(i==bsize){sel='checked';xzl=true;}
				oi = i+1;
				str+='<label onclick="xuanzhe1('+i+')" class="rock_cell rock_cell_active rock_check__label" for="ys'+oi+'">'+
					'<div class="rock_cell__hd">'+
					'	<input type="radio" name="checkbox1" '+sel+' class="rock_check" id="ys'+oi+'">'+
					'	<i class="rock_icon-checked"></i>'+
					'</div>'+
					'<div class="rock_cell__bd">'+
					'	<p style="font-size:'+sizearr[i]+'px">'+zys[i]+'</p>'+
					'</div>'+
				'</label>';
			}
			$('#listdiv').append(str);
		
		},
		loginsubmit:function(){
			js.msg('success','需要重启才能生效');
			xcy.sendEvent('fontsize','cog', {zoom:this.bodyzoom});
			if(this.bodyzoom==0)this.bodyzoom='';
			js.setoption('bodyzoom', this.bodyzoom);
			if(apixhbool)api.rockFun('setOption',{value:this.bodyzoom,'key':'bodyzoom'});
		}
	}
	c.init();
	js.initbtn(c);
	
	xuanzhe1 = function(lx){
		//document.body.style.zoom = bodyarr[lx];
		c.bodyzoom = lx;
	}
	
});
</script>

<div class="rock_cells__title">根据自己喜欢选择字体大小</div>
<div class="rock_cells__group rock_cells__group_form">
<div class="rock_cells rock_cells_checkbox" id="listdiv">
	
</div>

<div align="center" style="margin-top:20px">
  <button class="webbtn webbtn_big fontsize" clickevt="loginsubmit" type="button">保存</button>
</div>

</div>
    

<div class="blank15"></div>