
package com.rock.xinhu;


import android.content.Context;
import android.view.View;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import com.dialog.Dialog;
import com.dialog.DialogMsg;
import com.baselib.AR;
import com.baselib.CallBack;
import com.baselib.Rock;


public class XinhuBase_theme extends XinhuBase {

    protected String nowcolor;

    public XinhuBase_theme(Context context, View view) {
        super(context, view);
    }

    public void initBase()
    {
        String val  = "0";
        String col  = Rock.nowtheme;
        nowcolor    = col;
        if(Rock.isEmpt(col) || col.equals("#1389D3"))val="1";
        if(col.equals("#99CC66"))val="2";
        if(col.equals("#003366"))val="3";
        if(col.equals("#6666CC"))val="4";
        if(col.equals("#CC3333"))val="5";
        if(col.equals("#009966"))val="6";
        if(col.equals("#333333"))val="7";

        RadioButton radioBtn = (RadioButton) mView.findViewById(AR.getID("radioGroupButton"+val+""));;
        radioBtn.setChecked(true);
    }

    public void setOnClickListener(View.OnClickListener cick)
    {
        View tv = mView.findViewById(AR.getID("button"));
        tv.setOnClickListener(cick);
        Rock.setBackground(tv);

        mView.findViewById(AR.getID("radioGroupButton0")).setOnClickListener(cick);
    }

    public void onViewClick(int id)
    {
        if(id==AR.getID("button")){
            queding();
        }
        if(id==AR.getID("radioGroupButton0")){
            zidingy();
        }
    }

    private void queding(){
        RadioGroup radiogroup = (RadioGroup) mView.findViewById(AR.getID("radioGroup1"));
        int selid   = radiogroup.getCheckedRadioButtonId();
        RadioButton radioBtn2 = (RadioButton) radiogroup.findViewById(selid);

        String str  = radioBtn2.getText().toString();
        String color = nowcolor;
        if(Rock.contain(str, "默认"))color="#1389D3";
        if(Rock.contain(str, "2"))color="#99CC66";
        if(Rock.contain(str, "3"))color="#003366";
        if(Rock.contain(str, "4"))color="#6666CC";
        if(Rock.contain(str, "5"))color="#CC3333";
        if(Rock.contain(str, "6"))color="#009966";
        if(Rock.contain(str, "7"))color="#333333";

        Rock.nowtheme = color;
        Rock.Sqlite.setOption("nowtheme", color);

        DialogMsg.success(mContext, "保存成功，需重启生效");
    }

    private void zidingy()
    {
        Dialog.prompt(mContext, "请输入颜色值如#开头如：#ff0000", "自定义主题", nowcolor, new CallBack(){
            @Override
            public void back() {
                String col = Dialog.getText();
                if(!Rock.isEmpt(col))zidingys(col);
            }
        }, null);
    }
    private void zidingys(String col)
    {
        if(col.length()!=7){
            DialogMsg.error(mContext,"无效颜色值");
            return;
        }
        nowcolor = col;
    }
}