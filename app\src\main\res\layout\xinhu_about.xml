<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"

    android:orientation="vertical"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">



    <com.view.RockImageView
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginTop="40dp"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:src="@mipmap/logo"
        app:radius="50.0"
        />


    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_gravity="center"
        android:text="@string/app_name"
        android:layout_marginTop="10dp"
        android:textSize="30dp"
        android:textColor="@color/black"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/version"
        android:layout_width="wrap_content"
        android:layout_gravity="center"
        android:text="Version 0.0.01"
        android:layout_marginTop="10dp"
        android:textSize="18dp"
        android:textColor="@color/black"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="30dp"
        android:layout_marginRight="40dp"
        android:layout_marginLeft="40dp"
        android:background="@color/white"
        >

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="@color/line2" />

        <RelativeLayout
            android:id="@+id/btn01"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:background="@drawable/btn_list">

            <com.view.RockTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:text="软件介绍"
                android:textColor="@color/black"
                android:textSize="@dimen/listdp" />

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:src="@mipmap/right"
                />
        </RelativeLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="@color/line2" />

        <RelativeLayout
            android:id="@+id/btn06"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:background="@drawable/btn_list">

            <com.view.RockTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:text="应用权限"
                android:textColor="@color/black"
                android:textSize="@dimen/listdp" />

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:src="@mipmap/right"
                />
        </RelativeLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="@color/line2" />

        <RelativeLayout
            android:id="@+id/btn02"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:background="@drawable/btn_list">

            <com.view.RockTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:text="建议反馈"
                android:textColor="@color/black"
                android:textSize="@dimen/listdp" />



            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:src="@mipmap/right"
                />
        </RelativeLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="@color/line2" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="40dp"
        android:gravity="center"
        >
        <TextView
            android:id="@+id/btn03"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="用户协议"
            android:textColor="@color/mcolor"
            android:textSize="16dp"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="|"
            android:textColor="#cccccc"
            android:layout_marginLeft="15dp"
            android:textSize="16dp"
            />

        <TextView
            android:id="@+id/btn04"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="隐私政策"
            android:textColor="@color/mcolor"
            android:layout_marginLeft="15dp"
            android:textSize="16dp"
            />

    </LinearLayout>


</LinearLayout>