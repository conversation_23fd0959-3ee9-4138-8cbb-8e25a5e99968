package com.baselib;

import android.view.MotionEvent;
import android.view.View;

public class RTouchListener implements View.OnTouchListener {

    @Override
    public boolean onTouch(View view, MotionEvent motionEvent) {
        switch (motionEvent.getAction()&motionEvent.getActionMasked()) {
            case MotionEvent.ACTION_DOWN://单指触碰
                CLog.error("按下");
            case MotionEvent.ACTION_UP://单指离开
                CLog.error("离开");
            case MotionEvent.ACTION_MOVE:
                CLog.error("移动");
        }
        return false;
    }

    /**
     * 是否可移动
     */
    private Boolean isMove = false;

    /**
     * 是否可移动
     */
    public RTouchListener setIsMove()
    {
        isMove = true;
        return this;
    }
}
