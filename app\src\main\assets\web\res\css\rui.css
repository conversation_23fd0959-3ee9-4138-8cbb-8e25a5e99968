/*
*	主颜色：1389D3，可替换成你想要的颜色
*/

*{padding:0px;margin:0px;list-style-type:none;-webkit-tap-highlight-color:transparent;}

body{
	--main-color:#1389D3;
	--border:0.5px rgba(0,0,0,0.1) solid;
	--font-size:16px;
}

body{overflow-x:hidden;background-color:#f5f5f5;font-size:var(--font-size);-webkit-overflow-scrolling:touch}
table{border-spacing: 0;border-collapse: collapse;}

input,select,textarea,button{resize:none;outline:none;font-size:var(--font-size)}
input:focus,textarea:focus,select:focus{border-color:var(--main-color)}
input[readonly],select[readonly],textarea[readonly]{ background-color:#f8f8f8}
.fontsize{}
a:link,a:visited{TEXT-DECORATION:none}
.r-zhu,.zhu{color:#1389D3;color:var(--main-color)}
.actives:active{background:rgba(0,0,0,0.05)}
.notselect{user-select:none;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;-khtml-user-select:none;}
::placeholder{color:#bbbbbb;}

.blank1{ height:1px; overflow:hidden; border-bottom:var(--border)}
.blank10{ height:10px; overflow:hidden;width:100%}
.blank5{ height:5px; overflow:hidden;width:100%}
.blank15{ height:15px;overflow:hidden;width:100%}
.blank20{ height:20px;overflow:hidden;width:100%}
.blank25{ height:25px;overflow:hidden;width:100%}
.blank30{ height:30px;overflow:hidden;width:100%}
.blank40{ height:40px; overflow:hidden;width:100%}

.r-wrap{word-wrap:break-word;word-break:break-all;white-space:normal;}

.r-click{TEXT-DECORATION:none;color:#1389D3;color:var(--main-color);cursor:pointer}
.r-cursor,button{cursor:pointer}

.r-touch{-webkit-overflow-scrolling:touch;overflow-scrolling:touch;overflow-y:auto;overflow-x:hidden}

.r-zhu-bgcolor{background-color:var(--main-color);}
.r-success-bgcolor{background-color:#449d44;}
.r-info-bgcolor{background-color:#d9edf7;}
.r-error-bgcolor{background-color:#f2dede;}

.r-header{height:50px;line-height:50px;background-color:#1389D3; background-color:var(--main-color);overflow:hidden;color:white;font-size:18px;text-align:center;position:fixed;left:0px;top:0px;width:100%;z-index:10}
.r-header-text{text-align:center;}
.r-float-right{float:right;}
.r-float-left{float:left;}
.r-position-right{position:absolute;right:0px;bottom:0px}
.r-position-left{position:absolute;left:0px;bottom:0px}
.r-input{background:white;border:1px #eeeeee solid;}
.r-input:focus{background:white;border:1px var(--main-color) solid;}

.r-padding10{padding:10px}
.r-padding20{padding:20px}

.r-header-btn{display:block;height:50px;width:40px;text-align:center;line-height:50px;}
.r-header-btn:active{color:#aaaaaa}

.r-label{padding:3px;color:white;font-size:12px;border-radius:2px}

.blank50{height:50px;overflow:hidden}
.r-input{height:30px;width:97%;border:0.5px #cccccc solid;padding:2px}

.r-border-t,.r-border-b,.r-border-l,.r-border-r,.r-border{position: relative;}

.r-border-t:after {
  content: " ";
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  border-top: 1px solid #cccccc;
  color: #cccccc;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
}

.r-face{border-radius:6px}

.r-border:after {
  content: '';
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  width: 200%;
  height: 200%;
  border: 1px solid #cccccc;
  color: #cccccc;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  -webkit-transform: scale(0.5);
		transform: scale(0.5);
    pointer-events: none;
}

.r-border-b:before {
  content: " ";
  position: absolute;
  display: block;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  border-bottom: 1px solid #cccccc;
  color: #cccccc;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
}




.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 5px;
  font-size: 12px;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  background-color:red;font-size:12px;
  border-radius: 10px;
}
.badge:empty {
  display: none;
}

.r-btn-active:active{opacity:0.8}

.r-chat{height:50px;overflow:hidden;background-color:#f7f7f7;position:fixed;left:0px;bottom:0px;width:100%}
.r-chat-input{height:30px;overflow:hidden;border:none;width:100%;background:none;border-bottom:0.5px #cccccc solid;font-size:16px;}
.r-chat-btn{height:30px;font-size:16px;overflow:hidden;border:none;width:50px;color:white; background-color:var(--main-color);display:block}


.r-tab{display: -webkit-box;display: -webkit-flex;display: flex;width: 100%;background-color:white;height:50px;overflow:hidden}
.r-tab-item{height:50px;overflow:hidden;line-height:49px;display: block;-webkit-box-flex: 1;-webkit-flex: 1; flex: 1;text-align:center}
.r-tab-item.active{color:var(--main-color);border-bottom:0.5px var(--main-color) solid;height:49px;}
.r-subtitle{color:#888888;padding:5px;font-size:14px;margin-top:5px}

.r-tabs{overflow:hidden;height:36px}
.r-tabs .r-tabs-item{height:36px;overflow:hidden;line-height:36px;display: block;float:left;text-align:center;padding:0px 10px;border:1px #dddddd solid;border-bottom:0px;border-right-width:0px;cursor:pointer;color:#888888}
.r-tabs .r-tabs-item:first-child{border-top-left-radius:5px}
.r-tabs .r-tabs-item:last-child{border-top-right-radius:5px;border-right-width:1px}
.r-tabs .r-tabs-item:not(:first-child):not(:last-child){border-radius:0}
.r-tabs .r-tabs-item.r-tabs-item.active{color:var(--main-color);}


.r-btn{background-color:var(--main-color);height:36px;overflow:hidden;border:none;color:white;line-height:20px;cursor:pointer;border-radius:5px;opacity:0.8;padding:0px 10px}
.r-btn[disabled]{background-color:#dddddd;}
.r-btn:hover,.r-btn:active{opacity:1}

a.webbtn:link,a.webbtn:visited,.webbtn{color:#ffffff;opacity:1;background-color:#1389D3; background-color:var(--main-color); padding:5px 10px; border:none; cursor:pointer;border-radius:5px;font-size:var(--font-size);-webkit-appearance: none;}
.webbtn:disabled{background-color:#aaaaaa; color:#eeeeee}
.webbtn:hover{box-shadow:0px 0px 5px rgba(0,0,0,0.3);opacity:0.8}
.webbtn_big{min-width:180px;border-radius:5px;padding:13px 10px;}

.menulist{background-color:white;user-select:none}
.menulist div{padding:12px 0px;text-align:center;border-top:var(--border)}
.menulist div:first-child{border:0px}
.menulist div:active{ background-color:rgba(0,0,0,0.05)}

.reimlabel{background:#93cdf2;color:white;padding:1px 2px;font-size:12px;border-radius:2px}
.reimlabel1{background:#f9af7e;color:white;padding:1px 2px;font-size:12px;border-radius:2px}
.reimlabel2{background:#99CCCC;color:white;padding:1px 2px;font-size:12px;border-radius:2px}

.flex{display: flex;-webkit-box-align: center;align-items: center;}


.lista>.lists:first-child{border-top:var(--border);}
.lists{padding:12px;cursor:pointer;border-bottom:var(--border);}
.lists img,.lists .img{height:50px;width:50px;border-radius:5px;overflow:hidden}
.lists .close{position:absolute;right:3px;top:5px;display:none;color:#aaaaaa}
.lists .bqs{position:absolute;right:3px;top:5px;}
.lists:active{background-color:rgba(0,0,0,0.05)}

.lists .name{height:24px;line-height:24px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.huicont{height:24px;line-height:24px; overflow:hidden;word-wrap:break-word;word-break:break-all;white-space:normal;margin-right:5px}

.rock_tabbar{height:65px;overflow:hidden;display:flex;border-top:var(--border); background:#f7f7f7;align-items: center;}
.rock_tabbar__item{flex:1;text-align:center}
.rock_tabbar__icon>i{font-size:26px}
.rock_tabbar__label{font-size:14px}
.rock_bar__item_on{color:var(--main-color)}

.rock_badge{display:inline-block;padding:.15em .4em;min-width:8px;border-radius:18px;background-color:#fa5151;color:#fff;line-height:1.2;text-align:center;font-size:12px;vertical-align:middle}
.rock_badge:empty{display:none}

.rock_cells__title{color:#aaaaaa;font-size:14px;margin-top: 16px;margin-bottom: 3px;padding-left: 16px;padding-right: 16px;}
.rock_cells{}
.rock_cell{background-color:white;padding:16px;display: flex;-webkit-box-align: center;align-items: center; border-bottom:var(--border);user-select:none;}
.rock_cells .rock_cell:first-child{border-top:var(--border)}
.rock_cell__bd{flex:1}
.rock_cell__hd{}

.rock_btn_cell{padding:16px;text-align:center; background:white;display: block;border-top:var(--border);border-bottom:var(--border);color:#fa5151}

.rock_btn_cell:active,.rock_access:active,.rock_cell_access:active{background-color:rgba(0,0,0,0.05)}

.rock_search-bar {
    position: relative;
    display: flex;
    box-sizing: border-box;
    -webkit-text-size-adjust: 100%;
    -webkit-box-align: center;
    align-items: center;
	background:white;border-radius:5px;margin:10px;flex:1;height:40px;overflow:hidden
}

.rock_search-bar__input {
    width: 100%;
    border: 0;
    box-sizing: content-box;
    background: transparent;
}

.rock_input{background:none;line-height:34px;border:none}
.rock_label {
    display: block;
    width: 105px;
    word-wrap: break-word;
    word-break: break-all;
}

.rock_footer{text-align:center;font-size:14px;color:#bbbbbb}

.rock_grids{display: inline-block;width:100%;background-color:white;user-select:none;border-top:var(--border);border-bottom:var(--border);}
.rock_grid{text-align:center;float:left;}
.rock_grid__icon>img{width:30px;height:30px;border-radius:5px}
.rock_grid__label{font-size:16px;height:24px;line-height:24px;overflow:hidden}



.rock-loading {
  display: inline-block;
  vertical-align: middle;
  height:16px;
  width:16px;
  -webkit-mask: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23606060' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23606060' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E%0A") 0 0 no-repeat;
  mask-size: cover;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
  opacity:0.2;
}
