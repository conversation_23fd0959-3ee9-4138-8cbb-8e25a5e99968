<script>
$(document).ready(function(){
	
	var c = {
		init:function(){
			if(apixhbool){
				$('#versiondiv').html('V'+api.appVersion+'');
			}
			var str = apiurl+'';
			$('#nowurl').html(str.substr(0,10)+'***'+str.substr(-4));
			this.showfont();
		},
		showfont:function(){
			var str='正常';
			if(bodyzoom==1)str='中号';
			if(bodyzoom==2)str='大号';
			$('#fontsizezt').html(str);
		},
		tongzhi:function(){
			js.msgok('请自己到手机<br>的设置里设置');
		},
		fontsize:function(){
			if(apixhbool){
				api.addEventListener({name:'cog'}, function(ret){
					if(ret.stype=='fontsize'){
						bodyzoom = ret.zoom;
						c.showfont();
					}
				});
			}
			xcy.opennei({name:'字体大小',url:'themd',nlogin:true});
		},
		theme:function(){
			xcy.opennei({name:'主题颜色',url:'theme',nlogin:true});
		},
		about:function(){
			xcy.opennei({name:'关于我们',url:'about',nlogin:true});
		},
		clearall:function(){
			//plus.cache.clear();
			api.toast({msg:'已清空'})
		},
		gengxinx:function(){
			if(apixhbool && !isphone && apptypeleixing){
				api.rockFun('updateChange');
			}else{
				api.showMsg({
					msg:'已是最新版本',
					time:1
				});
			}
		}
	}
	js.initbtn(c);
	c.init();
});
</script>



<div class="rock_cells">
	
	<div class="rock_cell rock_cell_access" clickevt="tongzhi">
		<div class="rock_cell__bd fontsize">
			新消息通知
		</div>
		<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
	</div>
	<div class="rock_cell rock_cell_access" clickevt="theme">
		<div class="rock_cell__bd fontsize">
			切换主题颜色
		</div>
		<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
	</div>
	<div class="rock_cell rock_cell_access" clickevt="fontsize">
		<div class="rock_cell__bd fontsize">
			字体大小
		</div>
		<div style="color:#888888"><span id="fontsizezt">正常</span>&nbsp;&nbsp;<span style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></span></div>
	</div>
	
	
	
</div>

<div class="rock_cells">
	
	<div class="rock_cell rock_cell_access" clickevt="clearall">
		<div class="rock_cell__bd fontsize">
			清空浏览缓存
		</div>
		<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
	</div>
	
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			当前地址
		</div>
		<div style="color:#888888;" id="nowurl">V0.0.1</div>
	</div>
	
</div>

<div class="rock_cells">

	<div class="rock_cell rock_cell_access" clickevt="about">
		<div class="rock_cell__bd fontsize">
			关于我们
		</div>
		<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
	</div>
	<div class="rock_cell rock_cell_access" clickevt="gengxinx">
		<div class="rock_cell__bd fontsize">
			当前版本
		</div>
		<div style="color:#888888"><span id="versiondiv">V0.0.1</span>&nbsp;&nbsp;<span style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></span></div>
	</div>
</div>



<div style="height:15px"></div>
