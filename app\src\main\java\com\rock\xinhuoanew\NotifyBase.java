package com.rock.xinhuoanew;

import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.job.JobInfo;
import android.app.job.JobScheduler;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.baselib.AA;
import com.baselib.CLog;
import com.baselib.Rock;
import com.baselib.SqliteClass;

public class NotifyBase {

    public static void startServer(Context context)
    {
        Intent it = new Intent(context, XinhuAlarmService.class);
        context.startService(it);
    }

    public static void stopServer(Context context)
    {
        Intent it = new Intent(context, XinhuAlarmService.class);
        context.stopService(it);
    }

    /**
     * 定时唤醒
     * */
    public static void timerAlarm(Context context, String action, int time)
    {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        long timeToWakeUp = System.currentTimeMillis() + (time * 1000);
        Intent intent = new Intent(context, XinhuReceiver.class);
        intent.setAction(action);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, timeToWakeUp, pendingIntent);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, timeToWakeUp, pendingIntent);
        } else {
            alarmManager.set(AlarmManager.RTC_WAKEUP, timeToWakeUp, pendingIntent);
        }
    }

    /**
     * 判断服务是否运行
     * */
    public static boolean isServiceRunning(Context context, String serviceName) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        String sername = "";
        if (manager != null) {
            for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
                sername = service.service.getClassName();
                //CLog.debug("进程："+sername);
                if (serviceName.equals(sername)) {
                    return true;
                }
            }
        }
        return false;
    }

    //任务调度参考文档：https://wsa.jianshu.io/p/55e16941bfbd
    public static void startJobServer(Context context, int time)
    {
        //CLog.error(""+time+"秒后启动job");
        JobScheduler jobScheduler = (JobScheduler) context.getSystemService(Context.JOB_SCHEDULER_SERVICE);
        jobScheduler.cancel(1);
        JobInfo.Builder job = new JobInfo.Builder(1, new ComponentName(context.getPackageName(), XinhuJobService.class.getName()));
        job.setMinimumLatency(time * 1000);//至少几秒后启动
        job.setOverrideDeadline(time * 1000);
        job.setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY);
        job.setRequiresCharging(false); //不需要充电时
        job.setRequiresDeviceIdle(false); //不需要空闲
        //job.setPeriodic(3000); //重复执行的秒数

        JobInfo jobInfo = job.build();

        int result = jobScheduler.schedule(jobInfo);

    }

    public static Boolean isServiceRun(Context context)
    {
        String fwna  = XinhuAlarmService.class.getName();
        return isServiceRunning(context, fwna);
    }


    /**
     * 任务运行
     * */
    public static int taskServer(Context context, String from)
    {
        if (isServiceRun(context)) {
            CLog.debug("alter服务正在运行("+from+")...");
        } else {
            CLog.debug("alter服务没有运行("+from+")");
            startServer(context);
        }
        return 0;
    }

}
