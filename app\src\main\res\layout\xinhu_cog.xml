<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">


    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />

    <RelativeLayout
        android:id="@+id/btn01"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        android:background="@drawable/btn_list">

        <com.view.RockTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="新消息通知"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:src="@mipmap/right" />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />


    <RelativeLayout
        android:id="@+id/btn02"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        android:background="@drawable/btn_list">

        <com.view.RockTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="切换主题颜色"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:src="@mipmap/right"
             />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />

    <RelativeLayout
        android:id="@+id/btn03"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        android:background="@drawable/btn_list">

        <com.view.RockTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="字体大小"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <TextView
            android:id="@+id/fontsize"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="35dp"
            android:text="正常"
            android:textColor="@color/hui3"
            android:textSize="16dp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:src="@mipmap/right" />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />


    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:layout_marginTop="15dp"
        android:background="@color/line2" />

    <RelativeLayout
        android:id="@+id/btn05"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        android:background="@drawable/btn_list">

        <com.view.RockTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="通信服务端"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <TextView
            android:id="@+id/socketmsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="35dp"
            android:text="未开启"
            android:textColor="@color/hui3"
            android:textSize="16dp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:src="@mipmap/right"
            />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />

    <RelativeLayout
        android:id="@+id/btn04"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        android:background="@drawable/btn_list">

        <com.view.RockTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="清空浏览缓存"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:src="@mipmap/right"
            />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />


    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:layout_marginTop="15dp"
        android:background="@color/line2" />

    <RelativeLayout
        android:id="@+id/btn06"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        android:background="@drawable/btn_list">

        <com.view.RockTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="关于我们"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:src="@mipmap/right"
            />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />


    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />

    <RelativeLayout
        android:id="@+id/btn08"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        android:background="@drawable/btn_list">

        <com.view.RockTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="创建桌面快捷方式"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:src="@mipmap/right"
            />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />

    <RelativeLayout
        android:id="@+id/btn07"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        android:background="@drawable/btn_list">

        <com.view.RockTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="当前版本"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <TextView
            android:id="@+id/version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="35dp"
            android:text="V0.0.1"
            android:textColor="@color/hui3"
            android:textSize="17dp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:src="@mipmap/right"
             />
    </RelativeLayout>



    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />


</LinearLayout>