package com.rock.xinhuoanew;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;

import com.dialog.DialogAudio;
import com.baselib.AA;
import com.baselib.CLog;
import com.baselib.Json;
import com.baselib.QQLocation;
import com.baselib.Rock;
import com.baselib.CallBack;

import java.util.Map;


public class BaseActivity extends ARockActivity {

    protected final BaseActivity mActivity = this;
    protected String guanbo_Callstr = "";
    protected String guanbo_Action = "";
    protected BroadcastReceiver baseReceiver = null;
    protected Boolean isQianBool = true;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initCreate();
    }

    protected void initCreate() {
    }

    protected void showMoreMenu() {
    }

    protected void onKeyBack() {
        finish();
    }

    protected Boolean onRequestPermissions(int code, int grant) {
        return false;
    }

    protected String menustring = "";
    protected String menucallstr = "";

    protected void setMenustring(String str, String call) {
        menustring = str;
        menucallstr = call;
    }

    protected View.OnClickListener OnViewClickListener = new View.OnClickListener() {
        public void onClick(View v) {
            onViewClick(v);
        }
    };

    //初始一些信息
    protected void initParams() {
        guanbo_Callstr = "";
        guanbo_Action = "";
        menustring = "";
        menucallstr = "";
    }

    public void setTitles(String tit) {
        TextView view = (TextView) findViewById(R.id.title);
        view.setText(tit);
    }

    public void setTitlelabel(String tit, String bgcolor) {
        if (Rock.isEmpt(tit)) return;
        TextView view = (TextView) findViewById(R.id.title_label);
        if (!Rock.isEmpt(bgcolor)) {
            Rock.setCornerRadius(view, 5, bgcolor);
        }
        view.setText(tit);
        view.setVisibility(View.VISIBLE);
    }


    public Handler getHandlers() {
        return myhandler;
    }

    protected Handler myhandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            String retsult = "";
            Bundle mBundle = msg.getData();
            if (mBundle != null) retsult = mBundle.get("result").toString();
            onhandleCallback(msg.what, msg.arg1, retsult);
        }
    };

    /**
     * 重写的onhandlecallback
     */
    protected void onhandleCallback(int what, int arg1, String bstr) {
        if (what == AA.ASCIPT_HIDEKEY) {
            hideKeyboard();
        }
        if (what == AA.ASCIPT_TITLE) {
            Map<String, String> ret = Json.getJsonObject(bstr);
            setTitlelabel(ret.get("label"), ret.get("labelbgcolor"));
        }
    }

    /**
     * 重写的加载完成webview调用
     */
    protected void onWebviewFinished() {
        try {
            if (baseReceiver != null) unregisterReceiver(baseReceiver);
        } catch (Exception e) {
        }
        baseReceiver = null;
    }

    protected void handleSend(int what, String bstr) {
        Rock.sendHandler(myhandler, what, bstr, 0);
    }

    protected void handleCallSend(String call, String bstr) {
        if (Rock.isEmpt(call)) return;
        handleSend(AA.ASCIPT_CALL, "if(" + call + ")" + call + "(" + bstr + ")");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        initParams();
        if (baseReceiver != null) unregisterReceiver(baseReceiver);//注销广播
        baseReceiver = null;
    }

    @Override
    protected void onResume() {
        super.onResume();
        isQianBool = true;
        if (!Rock.isEmpt(guanbo_Callstr))
            Xinhu.sendBroadcastApi(this, guanbo_Action, "onresume");
    }

    @Override
    protected void onPause() {
        super.onPause();
        isQianBool = false;
        if (!Rock.isEmpt(guanbo_Callstr))
            Xinhu.sendBroadcastApi(this, guanbo_Action, "onstop");
    }

    /**
     * 注册广播
     *
     * @act 字符串方法
     */
    public void addEventListener(String act, String calls) {
        guanbo_Callstr = calls;
        guanbo_Action  = act;
        if (baseReceiver != null) return;
        IntentFilter filter = new IntentFilter();
        filter.addAction(act);
        baseReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                String content = intent.getExtras().getString("content");
                CLog.debug("广播(" + action + ")" + content + "");
                if (!Rock.isEmpt(guanbo_Callstr)) mActivity.handleCallSend(guanbo_Callstr, content);
                onBroadcastReceiver(content);
            }
        };
        registerReceiver(baseReceiver, filter);
    }
    /*
    * 广播接收到的
    * */
    protected void onBroadcastReceiver(String content) { }

    /**
     * 发送关播
     * */
    public void sendEvent(String act, String params)
    {
        Xinhu.sendBroadcast(this,act, params);
    }



    /**
     * 打开相机或者相册
     * */
    public void startCamera(int gcode, String call, String sourceType)
    {
        Intent intent = new Intent();
        intent.setClass(this, CameraActivity.class);
        intent.putExtra("callbackstr",call);
        intent.putExtra("sourceType",sourceType);
        startActivityForResult(intent, gcode);
    }

    /**
     * 打开扫码功能
     * */
    public void openScan(int gcode, String call, String msg)
    {
        Intent intent = new Intent();
        intent.setClass(this, ScanActivity.class);
        intent.putExtra("callbackstr",call);
        intent.putExtra("msg",msg);
        startActivityForResult(intent, gcode);
    }





    /**
     * 安卓6以上设置状态栏颜色,sdk21+
     * */
    public void setStatusColor(int color) {
        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(color);
        }
    }

    /**
     * 录音
     * */
    public void startRecord(CallBack call)
    {
        Boolean bo = Rock.checkPermission(this, Manifest.permission.RECORD_AUDIO);
        if(!bo) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.RECORD_AUDIO}, AA.PERMISSION_RECORD_AUDIO);
        }else{
            DialogAudio.show(this, call);
        }
    }
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults){
        super.onRequestPermissionsResult(requestCode,permissions,grantResults);
        int grant = grantResults[0];
        Boolean bo = onRequestPermissions(requestCode, grant);
        if(bo)return;
        if(requestCode==AA.PERMISSION_RECORD_AUDIO){
            if(grant == PackageManager.PERMISSION_DENIED){
                Rock.Toast(this, "拒绝了权限，无法使用录音功能");
            }
            if(grant == PackageManager.PERMISSION_GRANTED){
                Rock.Toast(this, "已授权权限，请重新点开");
            }
        }
        if(requestCode==AA.PERMISSION_LOCATION){
            if(grant == PackageManager.PERMISSION_DENIED){
                Rock.Toast(this, "拒绝了权限，无法使用定位功能");
            }
            if(grant == PackageManager.PERMISSION_GRANTED){
                startLocation(locationfun, 1);
            }
        }
    }

    /**
     * 定位
     * */
    private QQLocation qqLocation;
    private String locationfun;
    public void startLocation(String fun, int lx)
    {
        locationfun = fun;
        if(lx==1){
            qqLocation = new QQLocation(this, new CallBack(){
                public void backMap(Map<String,String> a){
                    String bstr = "{latitude:"+a.get("latitude")+",longitude:"+a.get("longitude")+",accuracy:"+a.get("accuracy")+",address:\""+a.get("address")+"\",provider:\""+a.get("provider")+"\"}";
                    mActivity.handleCallSend(locationfun, bstr);
                    qqLocation.stop();
                    qqLocation = null;
                }
            });
            qqLocation.start();
        }else {
            Boolean bo = Rock.checkPermission(this, Manifest.permission.ACCESS_FINE_LOCATION);
            if (!bo) {
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, AA.PERMISSION_LOCATION);
            } else {
                startLocation(fun, 1);
            }
        }
    }

    /**
     * 用默认浏览器打开地址
     * */
    public void openView(String url)
    {
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        intent.setData(Uri.parse(url));
        startActivity(intent);
    }

    /**
     * 打开基础页面
     * */
    public void openXinhu(String name, String type, String params, int gcode)
    {
        Intent intent = new Intent();
        intent.setClass(this, XinhuActivity.class);
        intent.putExtra("name",name);
        intent.putExtra("type",type);
        intent.putExtra("params",params);
        startActivityForResult(intent, gcode);
    }
    public void openXinhu(String name, String type){ openXinhu(name,type, "", 0); }



    /**
     * 退出返回
     * */
    public void exitBack(String result, String etype)
    {
        if(Rock.isEmpt(etype))etype = "";
        if(!Rock.isEmpt(result)){
            Intent intent = new Intent();
            intent.putExtra("result", result);
            setResult(RESULT_OK, intent);
        }
        finish();
        if(etype.equals("hide"))overridePendingTransition(R.anim.main_in,R.anim.main_out);
    }
    public void exitBack(){exitBack("","");}

    /**
     * 隐藏键盘
     * */
    public void hideKeyboard()
    {
        InputMethodManager imm = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(getWindow().getDecorView().getWindowToken(), 0);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        showMoreMenu();
        super.onCreateOptionsMenu(menu);
        return false;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onKeyBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    //点击
    protected void onViewClick(View v) {
        int id = v.getId();
        if(id == R.id.back){
            hideKeyboard();
            exitBack();
        }
        if(id == R.id.more){
            showMoreMenu();
        }
    }


}