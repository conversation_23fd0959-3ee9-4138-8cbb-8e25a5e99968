
<script>
$(document).ready(function(){
	{params}
	
	var c = {
		
		init:function(){
			this.messid = params.messid;
			this.loaddata();
		},
		loaddata:function(){
			js.loading('加载中...');
			js.ajax('reim|getxqkkd',{id:this.messid},function(ret){
				c.showstarshow(ret.data);
			});
		},
		showstarshow:function(ret){
			var a = ret.wdarr,d,s='',i;
			if(a.length>0){
				$('#wdlists').html('未读'+a.length+'人');
				for(i=0;i<a.length;i++){
					d = a[i];
					s+='<div class="rock_grid" style="width:20%;padding:10px 0px">';
					s+='<div class="rock_grid__icon">';
					s+='	<img src="'+d.face+'">';
					s+='</div>';
					s+='<p class="rock_grid__label">';
					s+=''+d.name+'';
					s+='</p>';
					s+='</div>';
				}
				$('#wdlist').html(s)
			}
			a = ret.ydarr;
			s='';
			if(a.length>0){
				$('#ydlists').html('已读'+a.length+'人');
				for(i=0;i<a.length;i++){
					d = a[i];
					s+='<div class="rock_grid" style="width:20%;padding:10px 0px">';
					s+='<div class="rock_grid__icon">';
					s+='	<img src="'+d.face+'">';
					s+='</div>';
					s+='<p class="rock_grid__label">';
					s+=''+d.name+'';
					s+='</p>';
					s+='</div>';
				}
				$('#ydlist').html(s)
			}
		}
	}
	c.init();
});
</script>


<div class="rock_cells__title" id="wdlists">未读0人</div>
<div id="wdlist" style="background-color:white" class="rock_grids"></div>


<div class="rock_cells__title" id="ydlists">已读0人</div>
<div id="ydlist" style="background-color:white" class="rock_grids"></div>