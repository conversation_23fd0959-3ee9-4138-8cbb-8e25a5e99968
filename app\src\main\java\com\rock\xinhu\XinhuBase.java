/**
 * 定义一下方法常量
 * from http://www.rockoa.com/
 * 来自信呼开发团队
 * 雨中磐石(rainrock)
 * */

package com.rock.xinhu;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.View;

import com.rock.xinhuoanew.XinhuActivity;

public class XinhuBase {

    protected Context mContext;
    protected View mView;
    protected XinhuActivity mObj;

    public XinhuBase(Context context, View view) {
        mContext = context;
        mView    = view;
    }

    public void setxinhuActivity(XinhuActivity obj)
    {
        mObj = obj;
    }

    public void initBase()
    {

    }

    public void onDestroy()
    {

    }

    public void onBack()
    {
        mObj.exitBack();
    }

    public String getMenuString()
    {
        return  "";
    }

    public void onMenuClick(int index, String name)
    {

    }

    public void onViewClick(int id)
    {

    }

    public void setOnClickListener(View.OnClickListener cick)
    {

    }

    /**
     * 页面返回交互
     * */
    public void onActivityResult(int requestCode, String result){

    }

    protected void onhandleCallback(int what,int arg1,String retsult){

    }

    protected Handler myhandler = new Handler(){
        @Override
        public void handleMessage(Message msg) {
            String retsult  = "";
            Bundle mBundle  = msg.getData();
            if(mBundle!=null)retsult   = mBundle.get("result").toString();
            onhandleCallback(msg.what, msg.arg1, retsult);
        }
    };
}