package com.baselib;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;

import com.rock.xinhuoanew.TonghuaActivity;
import com.rock.xinhuoanew.Xinhu;

import java.util.Map;

public class RCall {

    public static int ROOM_IN      = 1;
    public static int ROOM_OUT     = 0;


    public static int isOpen        = 0;

    public static void regCall(Context context)
    {
        isOpen      = 2;
    }

    public static void unregCall(Context context)
    {
        isOpen      = 0;
    }

    /**
     * 收到通话信息处理
     * */
    public static void message(Context context, String cont)
    {
        Map<String,String> ret = Json.getJsonObject(cont),da;
        String calltype     = Rock.getMapString(ret, "calltype");
        //if(mCallback != null)mCallback.backMap(ret);
        CLog.debug(cont, "tonghuaABC-"+isOpen+"");

        if(isOpen < 2 && Rock.equals(calltype, "call")){
            String name = ret.get("adminname");
            String type = ret.get("th_type");

            if(isOpen == 0) {
                isOpen = 1;
                if(!Rock.qianBool) {
                    Xinhu.NotificationcancelAll(context);
                    String types = type.equals("1") ? "视频" : "语音";
                    Xinhu.Notification(context, "邀请" + types + "通话", "" + name + "邀请与您" + types + "通话..", "音视频通话来电", 0, 60);
                }
                //playCallIn(context);//需要主UI才能播放
            }

            //如果后台没权限打开就无法打开
            if(Rock.qianBool) {
                da = Rock.getMap();
                da.put("id", ret.get("adminid"));
                da.put("name", name);
                da.put("ranking", "");
                da.put("myid", Rock.adminid);
                da.put("face", Rock.getFace(ret.get("adminface")));
                da.put("channel", ret.get("th_channel")); //有这个说明被叫端
                da.put("type", type); //0语音，1视频
                String params = Json.getJsonString(da);
                Xinhu.startActivity(context, TonghuaActivity.class, name, "", params);
            }
        }

        //取消了
        if(Rock.equals(calltype, "cancel")){
            RPlay.stop();
            isOpen = 0;//取消了
            Xinhu.NotificationcancelAll(context);
        }
    }

    /**
     * 播放
     * */
    public static void play(Context context, String type, Boolean loop)
    {
        String url = ""+Rock.APIURL+"web/res/sound/"+type+"";
        RPlay.playUrl(context,url,loop);
    }

    /**
     * 来电播放
     * */
    public static void playCallIn(Context context)
    {
        play(context, "mi.mp3", true);
    }
}
