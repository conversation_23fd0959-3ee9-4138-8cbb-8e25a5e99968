<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">

    <com.view.RockImageView
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginTop="20dp"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:id="@+id/face"
        android:src="@mipmap/logo"
        app:radius="180.0"
        />

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:layout_marginTop="30dp"
        android:background="@color/line2" />

    <LinearLayout
        android:id="@+id/div01"
        android:layout_width="match_parent"
        android:orientation="vertical"

        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:gravity="center"
            android:background="@color/white">

            <com.view.RockTextView
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:gravity="right"
                android:text="用户名"
                android:textColor="@color/black"
                android:textSize="@dimen/listdp" />

            <com.view.RockEditText
                android:id="@+id/user"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:textColor="#555555"
                android:textSize="@dimen/listdp"
                android:singleLine="true"
                android:hint="用户名/姓名/手机号"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="20dp"
                android:padding="5dp"
                android:background="@drawable/input_login1"
                android:text=""
                android:layout_width="match_parent"/>


        </LinearLayout>



        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="@color/line2" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:gravity="center"
            android:background="@color/white">

            <com.view.RockTextView
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:gravity="right"
                android:text="密码"
                android:textColor="@color/black"
                android:textSize="@dimen/listdp" />

            <com.view.RockEditText
                android:id="@+id/pass"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:textColor="#555555"
                android:textSize="@dimen/listdp"
                android:hint="请输入密码"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="20dp"
                android:inputType="textPassword"
                android:padding="5dp"
                android:background="@drawable/input_login1"
                android:text=""
                android:layout_width="match_parent"/>


        </LinearLayout>



    </LinearLayout>


    <LinearLayout
        android:id="@+id/div02"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:gravity="center"
            android:background="@color/white">

            <com.view.RockTextView
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:gravity="right"
                android:text="手机号"
                android:textColor="@color/black"
                android:textSize="@dimen/listdp" />

            <com.view.RockEditText
                android:id="@+id/mobile"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:textColor="#555555"
                android:textSize="@dimen/listdp"
                android:singleLine="true"
                android:hint="请输入手机号"
                android:inputType="number"
                android:maxLength="11"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="20dp"
                android:padding="5dp"
                android:background="@drawable/input_login1"
                android:text=""
                android:layout_width="match_parent"/>


        </LinearLayout>



        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:background="@color/line2" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:background="@color/white"
            >

            <com.view.RockTextView
                android:id="@+id/mobileyzm1"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:gravity="right"
                android:text="验证码"
                android:textColor="@color/black"
                android:textSize="@dimen/listdp" />

            <com.view.RockEditText
                android:id="@+id/mobileyzm"
                android:layout_width="match_parent"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:layout_toRightOf="@+id/mobileyzm1"
                android:layout_toLeftOf="@+id/mobileyzmhq"
                android:background="@drawable/input_login1"
                android:hint="手机验证码"
                android:inputType="number"
                android:maxLength="6"
                android:padding="5dp"
                android:text=""
                android:textColor="#555555"
                android:textSize="@dimen/listdp" />


            <TextView
                android:id="@+id/mobileyzmhq"
                android:layout_width="80dp"
                android:layout_height="36dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="1dp"
                android:layout_marginRight="15dp"
                android:background="@drawable/btn_zhu"
                android:gravity="center"
                android:text="获取"
                android:textColor="@color/white"
                android:textSize="16dp" />

        </RelativeLayout>



    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />



    <com.view.RockTextView
        android:id="@+id/button"
        android:layout_width="180dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginTop="30dp"
        android:background="@drawable/btn_zhu"
        android:gravity="center"
        android:text="登 录"
        android:textColor="@color/white"
        android:textSize="@dimen/listdp" />


    <TextView
        android:id="@+id/yzmstr"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="30dp"
        android:gravity="center"
        android:text="验证码登录"
        android:textColor="@color/mcolor"
        android:textSize="@dimen/listdp" />

</LinearLayout>