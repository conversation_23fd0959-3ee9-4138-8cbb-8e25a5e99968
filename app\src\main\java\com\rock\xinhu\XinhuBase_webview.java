
package com.rock.xinhu;


import android.content.Context;
import android.text.Html;
import android.view.View;
import android.widget.TextView;

import com.dialog.DialogMsg;
import com.baselib.AR;
import com.baselib.Rock;


public class XinhuBase_webview extends XinhuBase {

    private TextView btnObj;

    public XinhuBase_webview(Context context, View view) {
        super(context, view);
    }

    public void initBase()
    {
        btnObj   = mView.findViewById(AR.getID("button"));
    }

    public void setOnClickListener(View.OnClickListener cick)
    {
        btnObj.setOnClickListener(cick);
        Rock.setBackground(btnObj);
    }

    public void onViewClick(int id)
    {
        if(id==AR.getID("button")){
            DialogMsg.success(mContext, "兼容不好，最新弃用了");
        }
    }

}