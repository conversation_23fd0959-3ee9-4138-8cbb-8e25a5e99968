<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_height="wrap_content">


    <LinearLayout
        android:id="@+id/chatlist"
        android:layout_width="fill_parent"
        android:layout_height="@dimen/listheightbase"
        android:background="@drawable/btn_list"
        android:gravity="center"
        android:orientation="horizontal"
        >


        <com.view.RockImageView
            android:id="@+id/icons"
            android:layout_width="@dimen/listheightimg"
            android:layout_height="@dimen/listheightimg"
            android:layout_marginLeft="15dp"
            android:src="@mipmap/noimg"
            app:radius="20" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            >
                <com.view.RockTextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"

                    android:singleLine="true"
                    android:text="@string/app_name"
                    android:textColor="@color/black"
                    android:textSize="@dimen/listdp" />

                <TextView
                    android:id="@+id/titles"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:singleLine="true"
                    android:text="@string/app_name"
                    android:textColor="#888888"
                    android:textSize="14dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/check"
            android:layout_width="24dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="√"
            android:layout_marginRight="10dp"
            android:textColor="@color/hui"
            android:textSize="24dp"
            />


    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />

</LinearLayout>