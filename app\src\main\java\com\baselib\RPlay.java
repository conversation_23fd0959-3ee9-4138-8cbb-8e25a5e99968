package com.baselib;

import android.content.Context;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

import java.io.File;
import java.io.IOException;

public class RPlay {

    private static MediaPlayer mPlayer  = null;
    private static Boolean bLooping     = false;
    private static Boolean palying      = false;

    /**
     * 播放音乐
     * */
    public static void playUrl(Context context, String url)
    {
        playUrl(context, url, true);
    }

    /**
     * 播放音乐
     * */
    public static void playUrl(Context context, String url, Boolean loop)
    {
        String ext      = RockFile.getExt(url);
        String filename = Jiami.md5(url)+"."+ext+"";
        bLooping        = loop;

        filename = RockFile.getCachedir(context, "amr")+"/"+filename;
        if(new File(filename).exists()){
            play(filename);
        }else {
            RockHttp.down(url, filename, new Handler() {
                @Override
                public void handleMessage(Message msg) {
                    onhandleMessage(msg);
                }
            }, 1);
        }
    }

    private static void onhandleMessage(Message msg)
    {
        String retsult  = "";
        Bundle mBundle  = msg.getData();
        if(mBundle!=null)retsult   = mBundle.get("result").toString();
        if(msg.what==1) {
            if(msg.arg1 == AA.HTTPB_SUCCESS) {
                play(retsult);
            }else{

            }
        }
    }

    /**
     * 是否在播放
     * */
    public static Boolean isPlay()
    {
        return palying;
    }

    /**
     * 播放
     * */
    private static void play(String path)
    {
        stop();
        if(mPlayer == null){
            mPlayer = new MediaPlayer();
            mPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    playSuccess();
                }
            });
        }
        try {
            mPlayer.setDataSource(path);
            mPlayer.prepare();
            mPlayer.setLooping(bLooping);
            mPlayer.start();
            palying = true;
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void  stop()
    {
        if(mPlayer == null)return;
        try {
            mPlayer.stop();
            mPlayer.prepare();
        } catch (IOException e) {
            e.printStackTrace();
        }
        palying = false;
        mPlayer = null;
    }

    /**
     * 播放完成
     * */
    private static void  playSuccess()
    {
        palying = false;
    }

    /**
     * 获取总时间
     * */
    public static int getTime()
    {
        if(mPlayer == null)return 0;
        int miao  = (int)(mPlayer.getDuration() * 0.001);
        return miao;
    }
}
