
package com.rock.xinhu;


import android.Manifest;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.net.Uri;
import android.os.Parcelable;
import android.view.View;
import android.widget.TextView;


import androidx.core.content.pm.ShortcutInfoCompat;
import androidx.core.content.pm.ShortcutManagerCompat;
import androidx.core.graphics.drawable.IconCompat;

import com.dialog.Dialog;
import com.dialog.DialogLoad;
import com.dialog.DialogMsg;
import com.baselib.AA;
import com.baselib.AR;

import com.baselib.CDate;
import com.baselib.CLog;
import com.baselib.CallBack;
import com.baselib.Jiami;
import com.baselib.Json;
import com.baselib.Rock;
import com.baselib.RockFile;
import com.baselib.RockHttp;
import com.rock.xinhuoanew.MainActivity;
import com.rock.xinhuoanew.R;
import com.rock.xinhuoanew.XinhuReceiver;
import com.rock.xinhuoanew.XinhuService;

import java.io.File;
import java.util.Map;


public class XinhuBase_cog extends XinhuBase {


    public XinhuBase_cog(Context context, View view) {
        super(context, view);
    }

    public void initBase()
    {
        TextView tv = mView.findViewById(AR.getID("version"));
        tv.setText("V"+ Rock.VERSION+"");
        changeFontsize();
    }

    public void setOnClickListener(View.OnClickListener cick)
    {
        mView.findViewById(AR.getID("btn01")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn02")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn03")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn04")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn05")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn06")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn07")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn08")).setOnClickListener(cick);
    }

    public void onViewClick(int id)
    {
        if(id==AR.getID("btn01")){
            opentongzhi();
        }
        if(id==AR.getID("btn02")){
            mObj.openXinhu("切换主题","theme");
        }
        if(id==AR.getID("btn03")){
            mObj.openXinhu("字体大小","fontsize", "", AA.RESULTCODE_FONTSIZE);
        }
        if(id==AR.getID("btn04")){
            clearWebViewCache();
        }
        if(id==AR.getID("btn05")){
            changeFontsize();
            //mObj.openXinhu("浏览内核","webview");
            //mObj.openXinhu("长连接自启动","socket", "", AA.RESULTCODE_FONTSIZE);
        }
        if(id==AR.getID("btn06")){
            mObj.openXinhu( "关于我们","about");
        }
        if(id==AR.getID("btn07")){
            updateChange(true);
        }
        if(id==AR.getID("btn08")){
            createShortcut();
        }
    }

    public void onActivityResult(int requestCode, String result){
        if(requestCode==AA.RESULTCODE_FONTSIZE){
            changeFontsize();
        }
    }

    private void changeFontsize()
    {
        TextView tv = mView.findViewById(AR.getID("fontsize"));
        String str = "正常";
        if(Rock.fontsize==1)str="中号";
        if(Rock.fontsize==2)str="大号";
        tv.setText(str);

        tv = mView.findViewById(AR.getID("socketmsg"));
        int zt = XinhuService.getState();
        if(zt == 1){
            tv.setText("已连接");
            tv.setTextColor(Color.parseColor("#009966"));
        }else{
            tv.setText((zt == 0) ? "未登录" : "未连接");
            tv.setTextColor(Color.RED);
        }
    }

    private void opentongzhi(){
        Intent intent = new Intent();
        intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
        intent.putExtra("app_package", mContext.getPackageName());
        intent.putExtra("app_uid", mContext.getApplicationInfo().uid);
        intent.putExtra("android.provider.extra.APP_PACKAGE", mContext.getPackageName());
        mContext.startActivity(intent);
    }

    /**
     * 清除WebView缓存
     */
    public void clearWebViewCache(){
        try {
            mContext.deleteDatabase("webview.db");
            mContext.deleteDatabase("webviewCache.db");
        } catch (Exception e) {
            e.printStackTrace();
        }
        //String chedir = mContext.getFilesDir().getAbsolutePath();
        RockFile.deleteDirFile(RockFile.getCachedir(mContext));
       // RockFile.deleteDirFile(chedir+ AA.APPPAGE); //WebView 缓存文件
        //RockFile.deleteDirFile(chedir+"/webviewCache"); //删除webview 缓存目录

        Rock.Toast(mContext,"清理完成");
    }

    /**
     * 检查更新
     * */
    public void updateChange(Boolean bo)
    {
        String dt   = CDate.date();
        String ver  = Rock.VERSION;
        int getcode = 1;
        if(bo){
            if(AA.APPLOCAL) {
                DialogLoad.show(mContext, "检查中...");
            }else{
                DialogMsg.success(mContext, "已是最新版本了");
                return;
            }
        }else{
            if(!AA.APPLOCAL)return;
            if(Rock.Sqlite.getOption("vernowdt").equals(dt))return;
            getcode = 2;
        }
        Rock.Sqlite.setOption("vernowdt", dt);
        String url = "http://www.rockoa.com/api.php?a=appupdate&ver="+ver+"&btype=new&apptype="+ AA.APPTYPE+"";
        RockHttp.get(url, myhandler, getcode, "", null);
    }
    protected void onhandleCallback(int what,int code,String retsult){
        if(code==AA.HTTPB_SUCCESS) {
            Map<String,String> ret = Json.getJsonObject(retsult);
            if(ret.get("success").equals("false")){
                if(what==1)DialogMsg.success(mContext, "已是最新版本了");
            }else{
                DialogLoad.hide();
                String str = "发现新版本V"+ret.get("version")+"，大小"+ret.get("size")+"："+ret.get("explain")+"，是否马上升级？";
                String updateurl = ret.get("updatedizhi");
                Dialog.confirm(mContext, str, "", new CallBack(){
                    @Override
                    public void back() {
                        Rock.openView(mContext, updateurl);
                    }
                }, null);
            }
        }else{
            DialogMsg.error(mContext, retsult);
        }
    }

    //创建桌面快捷方式
    public void createShortcut()
    {
        if (ShortcutManagerCompat.isRequestPinShortcutSupported(mContext)) {
            shorttitle  = Rock.Sqlite.getOption("title");
            shortapi    = Rock.Sqlite.getOption("apiurl");
            if(Rock.isEmpt(shorttitle) || Rock.isEmpt(shortapi)){
                DialogMsg.error(mContext, "请先登录");
                return;
            }
            Dialog.confirm(mContext, "创建“"+shorttitle+"”的桌面快捷方式，图标如下：", "创建快捷方式", new CallBack(){
                @Override
                public void back() {
                    createShortcuts();
                }
            }, null);
            String imgurl = ""+shortapi+"images/logo.png";
            Dialog.setImage(imgurl);
            iconpath = Dialog.getImagesdPath();
        }else{
            DialogMsg.error(mContext, "不支持创建快捷方式");
        }
    }

    private String shorttitle = "",shortapi = "",iconpath = "";
    private void createShortcuts() {
        File file = new File(iconpath);
        if(!file.exists()){
            DialogMsg.error(mContext, "无法获取图标");
            return;
        }

        // 创建快捷方式的Intent
        Intent shortcutIntent = new Intent(mContext, MainActivity.class);
        shortcutIntent.setAction(Intent.ACTION_MAIN);
        String ids = Jiami.base64encode(shortapi);
        shortcutIntent.putExtra("apiurl", ids);

        // 创建快捷方式的图标
        Bitmap bitmap = BitmapFactory.decodeFile(file.getAbsolutePath());
        IconCompat shortcutIcon = IconCompat.createWithBitmap(bitmap);

        Intent it = new Intent(mContext, XinhuReceiver.class);
        it.setAction(AA.ACTION_SHORTCUT);
        PendingIntent shortcutCallback = PendingIntent.getBroadcast(mContext, 0, it, PendingIntent.FLAG_IMMUTABLE);
        // 创建快捷方式的信息
        ShortcutInfoCompat shortcutInfo = new ShortcutInfoCompat.Builder(mContext, ids)
                .setIntent(shortcutIntent)
                .setShortLabel(shorttitle)
                .setIcon(shortcutIcon)
                .build();
        ShortcutManagerCompat.requestPinShortcut(mContext, shortcutInfo, shortcutCallback.getIntentSender());
        DialogMsg.success(mContext, "已创建，如没有检查是否有权限");
    }

}