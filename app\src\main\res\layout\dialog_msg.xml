<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:gravity="center"
    android:orientation="vertical">


    <LinearLayout
        android:background="@drawable/shape_dialog_bg"
        android:layout_height="150dp"
        android:layout_width="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        >
        <LinearLayout
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:gravity="center">
        <ImageView
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:id="@+id/msg_icon"
            android:src="@mipmap/ok" />
        </LinearLayout>

        <TextView
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:padding="5dp"
            android:id="@+id/msg_cont"
            android:layout_width="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:textColor="@color/white"
            android:textSize="18dp"
            android:gravity="center"
            android:text="提示" />
    </LinearLayout>





</LinearLayout>