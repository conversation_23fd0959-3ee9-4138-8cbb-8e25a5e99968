<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:gravity="center"
    android:orientation="vertical">


    <LinearLayout
        android:background="@drawable/shape_dialog_bg"
        android:layout_height="150dp"
        android:layout_width="150dp"
        android:orientation="vertical"
        android:gravity="center"
        >

        <ProgressBar
            android:id="@+id/loadbar"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:indeterminateTint="#35ffffff"
            />
        <TextView
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:visibility="gone"
            android:padding="5dp"
            android:id="@+id/load_cont"
            android:layout_width="wrap_content"
            android:textColor="@color/white"
            android:textSize="16dp"
            android:gravity="center"
            android:text="加载中" />
    </LinearLayout>





</LinearLayout>