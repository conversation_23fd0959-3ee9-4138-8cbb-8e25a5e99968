/**
 * 定义一个回调函数
 * from http://xh829.com/
 * 来自信呼开发团队
 * */

package com.baselib;
import java.util.Map;

public class CallBack {

    /**
     * 回调处理方法
     * */
    public void back(){

    }

    /**
     * 回调处理方法
     * */
    public void back(int code, String bstr){

    }

    /**
     * 回调处理方法字符串
     * */
    public void backstr(String bstr){

    }


    /**
     * http请求的
     * */
    public void backhttp(int code,int arg1, String bstr){

    }

    /**
     * 回了数组
     * */
    public void backMap(Map<String,String> a){

    }
}