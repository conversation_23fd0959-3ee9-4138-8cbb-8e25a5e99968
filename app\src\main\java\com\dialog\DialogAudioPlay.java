/**
 * 录音
 * from http://www.rockoa.com/
 * 来自信呼开发团队
 * */

package com.dialog;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.baselib.AA;
import com.baselib.AR;
import com.baselib.CDate;
import com.baselib.CLog;
import com.baselib.CallBack;
import com.baselib.Jiami;
import com.baselib.Rock;
import com.baselib.RockFile;
import com.baselib.RockHttp;

import java.io.File;
import java.io.IOException;
import java.util.Timer;
import java.util.TimerTask;


public class DialogAudioPlay {

    private static View view = null;
    private static TextView mTextView,tTextView;
    private static AlertDialog dialog = null;
    private static ImageView mImageView = null;
    private static Timer sTimer = null;
    private static MediaPlayer mPlayer;
    private static Handler ssHandler;

    //play
    public static void show(Context context, String url) {
        if (dialog != null)hide();
        AlertDialog.Builder builder = new AlertDialog.Builder(context).setCancelable(true);
        view        = Rock.getView(context, AR.getlayoutID("dialog_audioplay"));
        mTextView   = (TextView) view.findViewById(AR.getID("timestr"));
        tTextView   = (TextView) view.findViewById(AR.getID("title"));
        mImageView   = (ImageView) view.findViewById(AR.getID("iv_recording_icon"));
        builder.setView(view);
        dialog = builder.show();

        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialogInterface) {
                stopShow();
            }
        });

        ispaybool = false;
        sTimer  = new Timer();
        mPlayer = new MediaPlayer();
        mPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                stop();
            }
        });

        ssHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                onhandleMessage(msg);
            }
        };

        String ext      = RockFile.getExt(url);
        String filename = Jiami.md5(url)+"."+ext+"";

        filename = RockFile.getCachedir(context, "amr")+"/"+filename;
        if(new File(filename).exists() && 1==1){
            play(filename);
        }else {
            tTextView.setText("下载中..");
            RockHttp.down(url, filename, ssHandler, 1);
        }
    }

    private static void onhandleMessage(Message msg)
    {
        String retsult  = "";
        Bundle mBundle  = msg.getData();
        if(mBundle!=null)retsult   = mBundle.get("result").toString();
        if(msg.what==1) {
            if(msg.arg1 == AA.HTTPB_SUCCESS) {
                play(retsult);
            }else{
                tTextView.setText("下载失败");
            }
        }
        if(msg.what==2) {
            int lev = (int)(1+Math.random()*5)+5;
            mImageView.getDrawable().setLevel(lev*1000);
            long time = Long.parseLong(CDate.gettime()) - Long.parseLong(retsult);
            mTextView.setText(CDate.long2String(time));
            runStimer(retsult);
        }
    }

    private static Boolean ispaybool = false;
    private static void play(String path){
        try {
            mPlayer.setDataSource(path);
            mPlayer.prepare();
            mPlayer.start();
            ispaybool = true;
            int miao  = (int)(mPlayer.getDuration() * 0.001);
            tTextView.setText("播放中共"+miao+"秒");
            runStimer(CDate.gettime());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void hide()
    {
        dialog.dismiss();
        stopShow();
    }

    //取消了
    private static void stopShow() {
        stop();
        mPlayer = null;
        dialog  = null;
        view    = null;
    }

    private static void stop() {
        sTimer.cancel();
        if(ispaybool) {
            try {
                mPlayer.stop();
                mPlayer.prepare();
            } catch (IOException e) {
                e.printStackTrace();
            }
            tTextView.setText("播放完毕");
        }
        ispaybool = false;
    }

    private static void runStimer(String stime)
    {
        sTimer.schedule(new TimerTask() {
            public void run() {
                Rock.sendHandler(ssHandler, 2, stime, 0);
            }
        }, 150);
    }
}