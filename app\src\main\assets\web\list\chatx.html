<link rel="stylesheet" href="res/css/chat.css"/>
<script src="js/strformat.js"></script>
<script src="res/plugin/jquery-rockupload.js"></script>
<script>
$(document).ready(function(){
	{params}
	
	var im = {
		init:function(){
			$('#footerdiv').remove();
			$('#backbtn').show();
			
			this.type = params.type;
			this.gid = params.id;
			this.myid = adminid;
			this.initload();
		},
		urlstr:function(st1){
			if(this.type=='gout'){
				var urla = st1.split('|');
				return xcy.outgroup.geturl(urla[1]);
			}
			return st1;
		},
		initload:function(){
			js.ajax(this.urlstr('reim|getgroupuser'),{type:this.type,gid:this.gid},function(ret){
				im.initloadshow(ret.data);
				xcy.reloadok();
			}, 'get', function(){
				xcy.showerror();
			});
		},
		initloadshow:function(ret){
			var uarr = ret.uarr;
			this.infor = ret.infor;
			this.inforuarr = uarr;
			$('#huihuaname').html(ret.infor.name);
			$('#myshow_name').html(ret.infor.name);
			if(!this.infor.createname)this.infor.createname='';
			if(!this.infor.createid)this.infor.createid='0';
			$('#createnameshow').html(this.infor.createname);
			$('#huihuanameshu').html(ret.infor.utotal+'人');
			xcy.setTitle(''+ret.infor.name+'('+ret.infor.utotal+')');
			var s1 = xcy.grouptype(ret.infor.deptid, this.type);
			if(!s1)s1='普通会话';
			get('chatface').src=xcy.getface(this.infor.face);
			$('#chattypeshow').html(s1);
			if(isempt(ret.infor.deptid)){
				$('#tuibtn_show').show();
				$('#yaoqing_show').show();
			}else{
				$('#tuibtn_show').hide();
				$('#yaoqing_show').hide();
			}
			if(this.type=='gout')$('#tuibtn_show').show();
			this.showuserl(0);
			if(ret.myfor && this.type=='gout'){
				$('#myoutinfi').show();
				this.myfor = ret.myfor;
				this.myid  = ret.myfor.id;
				$('#myname').html(this.myfor.name);
				$('#myunum').html(this.myfor.unum.substr(0,3)+'***');
				get('mytouxan').src=this.myfor.face;
				if(this.myfor.istx==0)get('switchCP').checked=true;
				xcy.outgroup.saveoutunum(this.myfor.unum);
				if(this.myid==1)$('#clearmessdiv').show();
			}
			js.initbtn(this);
		},
		showuserl:function(xu){
			var i,d,s='',len=this.inforuarr.length,maxh=60,ks=xu,zs=0;
			for(i=xu+0;i<len;i++){
				zs++;
				d = this.inforuarr[i];
				s+='<div clickevt="clickuser,'+i+'" class="rock_grid rock_access" style="width:20%;padding:10px 0px">';
				s+='<div class="rock_grid__icon">';
				s+='	<img src="'+d.face+'">';
				s+='</div>';
				s+='<p class="rock_grid__label">';
				if(d.id==this.infor.createid)s+='<i class="icon-user"></i>';
				s+=''+d.name+'';
				s+='</p>';
				s+='</div>';
				ks++;
				if(zs==maxh)break;
			}
			this.temoxues = ks;
			if(xu==0){
				$('#chatxlist').html(s);
			}else{
				$('#chatxlist').append(s);
			}
			if(len>ks){
				$('#morediv').show();
				$('#morediv').html('点击查看还有'+(len-ks)+'人');
			}else{
				$('#morediv').hide();
			}
		},
		clearmores:function(){
			this.showuserl(this.temoxues);
		},
		clickuser:function(o1,i){
			var d1 = this.inforuarr[i],isyichu=false;
			this.nowuser = d1;
			var da1 = [{name:'@TA',lx:1}],da2=['@TA'];
			if(this.myid==this.infor.createid && this.myid!=d1.id && (isempt(this.infor.deptid) || this.type=='gout')){
				isyichu = true;
				da2.push('移出会话');
				da1.push({name:da2[1],lx:1});
			}
			if(!apixhbool){
				var cans = {
					data:da1,
					onclick:function(d){
						if(d.lx==1)im.atta();
						if(d.lx==2)im.yichuss();
					}
				};
				setTimeout(function(){js.wx.actionsheet(cans);},10);
				return;
			}
			api.createMenu({
				menu:da2.join(',')
			},function(ret){
				if(ret.name=='@TA')im.atta();
				if(ret.name=='移出会话')im.yichuss();
			});
		},
		atta:function(){
			xcy.sendEvent('at', 'rockchat', {
				'id':this.nowuser.id,
				'namexm':this.nowuser.name,
				'type':this.type,
				'gid':this.gid,
			});
			xcy.back();
		},
		yichuss:function(){
			var d1 = this.nowuser;
			rockconfirm('确定要将“'+d1.name+'”移出会话里吗？', function(jg){
				if(jg=='yes')im.chatexits(d1.id,'移出');
			});
		},
		copyunum:function(){
			$('#myunum').html(this.myfor.unum);
		},
		chatexit:function(){
			if(this.myid==this.infor.createid){
				js.msgerror('创建者不可退出');
				return;
			}
			rockconfirm('确定要退出此会话吗？', function(jg){
				if(jg=='yes')im.chatexits(im.myid, '退出');
			});
		},
		chatexits:function(id1,smd){
			js.ajax(this.urlstr('reim|exitgroup'),{gid:this.gid,aid:id1},function(da){
				js.wx.msgok('已'+smd+'会话');
				im.initload();
			}, 'get',''+smd+'会话中...');
		},
		yaoqing:function(){
			rockprompt('','请输入邀请人员姓名/手机号', function(txt){
				if(txt)im.yaoqings(txt);
			});
		},
		yaoqings:function(txt){
			js.ajax(this.urlstr('reim|yaoqingname'),{gid:this.gid,val:txt},function(da){
				js.wx.msgok('邀请成功');
				im.initload();
			}, 'post','邀请中...');
		},
		editname:function(){
			if(this.type=='gout')return;
			rockprompt('','请输入新的会话名', function(txt){
				if(txt)im.editnames(txt);
			}, this.infor.name);
		},
		editnames:function(txt){
			txt = strreplace(txt);
			if(txt==this.infor.name)return;
			js.ajax(this.urlstr('reim|editname'),{gid:this.gid,val:txt},function(da){
				js.wx.msgok('修改成功');
				im.initload();
			}, 'post','修改中...');
		},
		editface:function(){
			if(!apixhbool || this.type=='gout')return;
			
			api.createMenu({
				menu:'修改头像|'+maincolor+',拍照,相册中选,用默认'
			},function(ret){
				var index = ret.menuIndex;
				if(index==1)im.paizhao('camera');
				if(index==2)im.paizhao('album');
				if(index==3)im.sendfileok({thumbpath:'images/group.png',id:'0'});
			});
		},
		paizhao:function(lx){
			api.getPicture({
				sourceType:lx
			},function(ret){
				im.sendapifile(ret.filepath, ret);
			});
		},
		changeappok:function(ret){
			if(ret.data){
				var ext = ret.data.substr(-3).toLowerCase();
				if(!js.isimg(ext)){
					js.wx.msgerror('请选图片类型');
				}else{
					im.sendapifile(ret.data);
				}
			}
		},
		sendapifile:function(path){
			api.showProgress({msg:'上传中...'});
			var url = js.apiurl('upload','upfile',{noasyn:'yes',thumbnail:'150x150'});
			api.upload({
				filepath:path,
				url:url
			},function(ret,err){
				if(ret){
					if(ret.valid==0){
					}else{
						im.sendfileok(ret);
					}
				}else{
					api.showMsg({
						type:'error',
						msg:err.responseText
					});
				}
			});
		},
		sendfileok:function(ret){
			js.ajax('reim|editface',{gid:this.gid,fileid:ret.id},function(da){
				api.showMsg({msg:'修改成功'})
				get('chatface').src=xcy.getface(ret.thumbpath);
			}, 'get');
		},
		editmyface:function(){
			rockconfirm('是否同步我的头像', function(jg){
				if(jg=='yes'){
					
					im.savemyface();
				}
			});
		},
		clearqingk:function(){
			rockconfirm('确定要清空记录吗？', function(jg){
				if(jg=='yes')im.clearqingks();
			});
		},
		clearqingks:function(){
			js.ajax(this.urlstr('reim|clearmess'),{type:this.type,gid:this.gid},function(da){
				js.wx.msgok('清空成功');
			}, 'get','清空中...');
		},
		savemyface:function(){
			if(adminface.indexOf('noface.png')>0){
				js.msgerror('你当前没有设置头像');
				return;
			}
			js.ajax('login|downimgnew',{path:encodeURI(adminface)},function(ret){
				var s = ret.data.result;
				s = s.replace(/\+/g, '!');	
				s = s.replace(/\//g, '.');	
				s = s.replace(/\=/g, ':');
				js.ajax(im.urlstr('reim|editmyface'),{gid:im.gid,data:s},function(ret){
					js.wx.msgok('修改成功');
					get('mytouxan').src = ret.data.face;
				}, 'post','修改中...');
			});
		},
		editmyname:function(){
			if(this.type!='gout')return;
			rockprompt('','请输入新的昵称', function(txt){
				if(txt)im.editmynames(txt);
			}, this.myfor.name);
		},
		editmynames:function(na){
			js.ajax(this.urlstr('reim|editmyname'),{gid:this.gid,name:na},function(da){
				js.wx.msgok('修改成功');
				$('#myname').html(na);
			}, 'post','修改中...');
		},
		getBase64Image:function(img) {
            var canvas = document.createElement("canvas");
            canvas.width = img.width;
            canvas.height = img.height;
            var ctx = canvas.getContext("2d");
            ctx.drawImage(img, 0, 0, img.width, img.height);
            var dataURL = canvas.toDataURL("image/png");
             return dataURL;
            //return dataURL.replace("data:image/png;base64,", ""); 
        },
		qiehuanke:function(){
			setTimeout(function(){im.qiehuankes()},500);
		},
		qiehuankes:function(){
			var istx = 1;
			if(get('switchCP').checked)istx=0;
			js.ajax(this.urlstr('reim|editmyistx'),{gid:this.gid,istx:istx},function(da){
			});
		}
	}
	im.init();
	xcy.backcall.chatx=function(){
		js.back();
	}
	xcy.touchload.chatx=function(){
		im.initload();
	}
	xcy.leftright=function(lx){
		if(lx>100)js.back();
	}
	im{rand}=im;
});
</script>

<div class="rock_cells" style="margin-top:15px">
	<div class="rock_cell rock_cell_access" clickevt="editface" >
		<div class="rock_cell__hd"style="position: relative;margin-right: 10px;">
			<img src="images/noface.png" class="r-face" id="chatface"  style="width:40px;display: block;height:40px"/>
		</div>
		<div class="rock_cell__bd">
			<p id="myshow_name" class="fontsize"></p>
		</div>
		<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
	</div>
</div>

<div id="myoutinfi" style="display:none">
	<div class="rock_cells__title">我的信息</div>
	<div class="rock_cells">
		<div class="rock_cell rock_cell_access"  clickevt="editmyname">
			<div class="rock_cell__bd fontsize">
				我的昵称
			</div>
			<div style="color:#888888"><span id="myname"></span>&nbsp;&nbsp;<span style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></span></div>
		</div>
		<div class="rock_cell rock_cell_access"  clickevt="copyunum">
			<div class="rock_cell__bd fontsize">
				我的唯一编号
			</div>
			<div style="color:#888888"><span id="myunum"></span>&nbsp;&nbsp;<span style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></span></div>
		</div>
		<div class="rock_cell rock_cell_access"  clickevt="editmyface">
			<div class="rock_cell__bd fontsize">
				我的头像
			</div>
			<div style="color:#888888"><img id="mytouxan" align="absmiddle" src="images/noface.png" width="20px" height="20px">&nbsp;&nbsp;<span style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></span></div>
		</div>
		<div class="rock_cell rock_cell_access" >
			<div class="rock_cell__bd fontsize">
				消息免打扰
			</div>
			<div style="color:#888888">
				<label for="switchCP"  clickevt="qiehuanke" class="rock_switch-cp">
					<input id="switchCP" class="rock_switch-cp__input" type="checkbox">
					<div class="rock_switch-cp__box"></div>
				</span>
		
			</div>
		</div>
	</div>
</div>

<div class="rock_cells__title">设置</div>
<div class="rock_cells">
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			会话类型
		</div>
		<div id="chattypeshow" style="color:#888888">普通会话</div>
	</div>
	<div class="rock_cell rock_cell_access"  clickevt="editname">
		<div class="rock_cell__bd fontsize">
			会话名
		</div>
		<div style="color:#888888"><span id="huihuaname"></span>&nbsp;&nbsp;<span style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></span></div>
	</div>
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			会话上人员数
		</div>
		<div id="huihuanameshu" style="color:#888888"></div>
	</div>
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			创建者
		</div>
		<div id="createnameshow" style="color:#888888"></div>
	</div>
	<div style="display:none" id="yaoqing_show" clickevt="yaoqing" class="rock_cell rock_cell_access">
		<div class="rock_cell__bd fontsize">
			邀请
		</div>
		<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
	</div>
	<div class="rock_cell rock_cell_access" style="display:none" id="clearmessdiv" clickevt="clearqingk">
		<div class="rock_cell__bd fontsize">
			清空聊天记录
		</div>
		<div style="color:#888888"><span style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></span></div>
	</div>
</div>

<div class="rock_cells__title">会话上人员</div>
<div id="chatxlist" style="background-color:white" class="rock_grids"></div>
<div id="morediv" clickevt="clearmores" style="padding:10px;color:#bbbbbb;font-size:14px;text-align:center;display:none">更多人员...</div>




<div style="display:none" id="tuibtn_show">
<div style="height:15px"></div>
<a class="rock_btn_cell rock_btn_cell-warn" type="button" clickevt="chatexit">退出会话</a>
<div style="height:15px"></div>
</div>