<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="yes" />
<title>..</title>

<style>
</style>
<script>


if(window.plus){plusReady();}else{document.addEventListener("plusready", plusReady, false);}
function plusReady(){ 
	isapp = true;
	nowwin = plus.webview.currentWebview();
	plus.storage.removeItem('scanresult');
	barcode = plus.barcode.create('barcode', [plus.barcode.QR], {
		top:'0px',
		left:'0px',
		width: '100%',
		height: '100%',
		position: 'static'
	});
	barcode.onmarked = function(type,result){
		plus.storage.setItem('scanresult', result);
		barcode.close();
		plus.webview.close(nowwin);	
	};
	nowwin.append(barcode);
	barcode.start();
}

</script>
</head>
<body>


</body>
</html>
