<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusableInTouchMode="true"
    >


    <LinearLayout
        android:id="@+id/headertopzt"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:background="@color/mcolor"
        android:orientation="vertical"></LinearLayout>

    <RelativeLayout
        android:id="@+id/headertop"
        android:layout_below="@+id/headertopzt"
        android:layout_width="fill_parent"
        android:background="@color/mcolor"
        android:layout_height="@dimen/headheight">

        <LinearLayout
            android:id="@+id/header"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/headheight"
            android:layout_marginLeft="@dimen/headheight"
            android:layout_marginRight="@dimen/headheight"
            android:gravity="center"
           >
            <com.view.RockTextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/headheight"
                android:singleLine="true"
                android:gravity="center"
                android:text="@string/app_name"
                android:textSize="@dimen/headfontsize"
                android:textColor="@color/white" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/back"
            android:layout_width="@dimen/headheight"
            android:layout_height="@dimen/headheight"
            android:background="@drawable/btn_tm"
            android:layout_alignParentLeft="true"
            android:gravity="center"
            android:orientation="vertical">
            <com.view.RockTextViewIcon
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_back"
                android:textSize="20dp"
                android:textColor="@color/white" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/more"
            android:layout_width="@dimen/headheight"
            android:layout_height="@dimen/headheight"
            android:visibility="gone"
            android:background="@drawable/btn_tm"
            android:layout_alignParentRight="true"
            android:orientation="vertical">
            <ImageView
                android:id="@+id/moreimg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#00000000"
                android:layout_margin="16dp"
                android:src="@mipmap/mores" />
        </LinearLayout>

    </RelativeLayout>


    <ScrollView
        android:id="@+id/mainbody"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/headertop"
        android:layout_alignParentBottom="true"
        android:overScrollMode="never"
        android:background="#f1f1f1">
    </ScrollView>


</RelativeLayout>