/**
 * 定义一下方法
 * from http://xh829.com/
 * 来自信呼开发团队
 * */

package com.dialog;

import android.app.AlertDialog;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.baselib.AR;
import com.baselib.Rock;


public class DialogLoad {

    private static View view = null;
    private static AlertDialog dialog = null;

    /**
     * 显示
     * */
    public static void show(Context context, String msg)
    {
        hide();
        view = Rock.getView(context,AR.getlayoutID("dialog_load"));
        if(!Rock.isEmpt(msg)){
            TextView tv = (TextView)view.findViewById(AR.getID("load_cont"));
            tv.setVisibility(View.VISIBLE);
            tv.setText(msg);
        }

        //ProgressBar loadbar = view.findViewById(AR.getID("loadbar"));
        //loadbar.setIndeterminateTintList(ColorStateList.valueOf(Color.parseColor(Rock.defTheme)));

        AlertDialog.Builder builder = new AlertDialog.Builder(context);

        builder.setView(view);
        dialog = builder.show();

        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.getWindow().setDimAmount(0f);
    }

    /**
     * 显示
     * */
    public static void show(Context context)
    {
        show(context, null);
    }

    /**
     * 显示
     * */
    public static void hide()
    {
        if(dialog!=null) {
            dialog.dismiss();
            dialog = null;
            view = null;
        }
    }

}