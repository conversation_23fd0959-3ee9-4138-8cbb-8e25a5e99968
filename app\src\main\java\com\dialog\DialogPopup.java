/**
 * 定义一下方法
 * from http://xh829.com/
 * 来自信呼开发团队
 * */

package com.dialog;


import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;

import com.baselib.AR;
import com.baselib.CallBack;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.baselib.Rock;


public class DialogPopup {

    private static View view = null;
    private static PopupWindow dialog = null;

    /**
     * 自定义菜单显示
     * */
    public static void show(Context context, View sview, String cont, final CallBack call) {
        hide();
        dialog = new PopupWindow(sview, ViewGroup.LayoutParams.WRAP_CONTENT,ViewGroup.LayoutParams.WRAP_CONTENT,true);
        view = Rock.getView(context, AR.getlayoutID("dialog_popup"));
        LinearLayout layout = (LinearLayout) view.findViewById(AR.getID("dialog_popup_main"));
        String menua[] = cont.split(",");

        View tv,hv;
        TextView av;
        int len = menua.length;
        for (int i = 0; i < len; i++){
            final int oi = i;
            String arr[] = menua[i].split("\\|");
            final String str = arr[0];
            tv = Rock.getView(context, AR.getlayoutID("dialog_popuplist"));
            av = (TextView) tv.findViewById(AR.getID("title"));
            av.setText(str);
            if(arr.length>1)av.setTextColor(Color.parseColor(arr[1]));
            av.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    if(call!=null)call.back(oi,str);
                    hide();
                }
            });
            if(i==len-1){
                hv = (View) tv.findViewById(AR.getID("hang"));
                hv.setVisibility(View.GONE);
            }
            layout.addView(tv);
        }

        dialog.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.setContentView(view);
        dialog.showAsDropDown(sview,0,0);
    }

    public static void hide()
    {
        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
            view = null;
        }
    }
}