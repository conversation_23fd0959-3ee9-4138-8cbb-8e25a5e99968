<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="maximum-scale=2.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="yes" />
<title>..</title>
<script src="js/jquery.js"></script>
<script src="js/base64-min.js"></script>
<script src="js/js.js?2"></script>
<script src="js/jswx.js?abc123"></script>
<script src="js/config.js"></script>
<link rel="stylesheet" href="res/css/rui.css?5"/>
<link rel="stylesheet" href="res/iconfont/iconfont.css"/>

<style>
body,html{overflow:hidden;}
body{--font-size:18px;}
</style>
<script>
var bodyzoom = 0,nowurl='home',isappbo=false,nowtabs={},loadci=0,nowchat='',apixhbool=false,initAppbool=false,isphone=false,viewheight=350,sizearr=[18,20,22],windowfocus=true,deletecolor='#D9534F',xcytouch= {},agentarr={},ishttps=true,bordercolor='rgba(0,0,0,0.1)',yingshu=4,maincolor='#1389D3',initbodybool=false,touchobja=false,nowwin={},mainobj={},isysapp=false,title=systemtitle,adminname='',date='',adminface='';

function globalbody(){
	var llq = navigator.userAgent;
	if(llq.indexOf('XINHUOAAPP')>0)isysapp = true;
}

apiready=function(){
	if(apixhbool)return;
	apixhbool = true;
	device    = api.deviceId;
	js.setoption('deviceid', device);
	CFROM='npp'+api.systemType+'';
	if(api.systemType=='ios')isphone = true;
	if(!isphone)sizearr=[18,18,18];
	if(isphone)xcy.phoneaction()
	js.setoption('nowtheme', api.nowtheme);
	xcy.showtheme(api.nowtheme);
    if(api.apiUrl)apiurl = api.apiUrl;
	initbody();
	xcy.init();
	xcy.initApp();
}


function initbody(){
	if(initbodybool)return;
	if(isysapp && !apixhbool)return; //等待app运行
	initbodybool = true;
	date 	  = js.now();
	title 	  = js.getoption('title', systemtitle);
	adminname = js.getoption('adminname');
	adminface = js.getoption('adminface');
	apiurl 	  = js.getoption('apiurl', apiurl);
	if(!apixhbool){
		js.showplugin('modal');
		$('#header').show();
	}
	var canurl= js.request('apiurl');
	if(canurl){
		canurl= jm.base64decode(canurl);
		if(canurl != apiurl){
		}
	}
	
	xcy.showtheme(js.getoption('nowtheme'));
	
	js.ajaxerror=function(msg, code){
		if(code==199){
			js.msg();
			xcy.loginExit();
			js.msg('msg','登录失效：'+msg+'');
			js.reload();
		}
		xcy.showerror(msg);
	}
	var lweb = navigator.userAgent;
	if(lweb.indexOf('XINHUOAAPP')<0 && lweb.indexOf('Html5Plus')<0){
		xcy.init();
	}
}

var xcy = {
	initApp:function(){},
	resieze:function(){},
	touchload:{},
	unloadarr:{},
	backcall:{},
	otherheight:0,
	init:function(){
		if(xcy.touchobj)return;
		this.resiezess();
		$(window).resize(xcy.resiezess);
		xcy.touchobj = $('#mainbody').rockdoupull({
			downbool:true,
			scrollbool:false,
			ondownsuccess:function(){
				if(xcy.touchload[nowurl])xcy.touchload[nowurl]();
			},
			ondownbefore:function(){
				if(xcy.touchload[nowurl]){
					return true;
				}else{
					return false;
				}
			},
			ondrayrlend:function(lx){
				
			}
		});
		
		var str,da={nlogin:false};
		var bstr= js.request('bstr');
		if(bstr){
			da = JSON.parse(jm.base64decode(bstr));
			$('#backbtn').show();
			this.pageParams = da;
		}
		adminid = js.getoption('adminid');
		TOKEN 	= js.getoption(TOKENKEY);
		if((!adminid || TOKEN=='') && !da.nlogin){
			this.addtabs('登录','login');
			return;
		}
		
		if(bstr){
			this.addtabs(da.name,da.url, da);
			if(isapp)this.addback();
			return;
		}
		
		this.addtabs('','home');
	},
	resiezess:function(){
		var hei = xcy.getheight();
		$('#mainbody').css('height',''+hei+'px');
		viewheight = hei;
		if(xcy.touchobj)xcy.touchobj.resize();
		xcy.resieze();
	},
	getheight:function(ss){
		if(!ss)ss=0;
		var hei = this.otherheight;
		if(get('header') && get('header').style.display!='none')hei+=50;
		var dx = winHb()-hei+ss;
		return dx;
	},
	setTitle:function(na){
		$('#header_title').html(na);
		document.title = na;
		if(apixhbool)api.rockFun('setBiaoti',{title:na});
	},
	addtabs:function(na,url,cans){
		if(!cans)cans={};
		if(!na)na= title;
		this.setTitle(na);
		var rand = js.getrand();
		var urlpms = JSON.stringify(cans);
		cans.name = na;
		cans.url = url;
		nowurl = url;
		
		if(this.istabs(url) && this.tabscont[url]){
			this.addtabsok(this.tabscont[url], rand, urlpms);
			return;
		}
		
		if(loadci>0){
			js.loading();
			//if(apixhbool){api.showProgress();}else{js.loading();}
		}
		loadci++;
		var cans = {
			'type':'get',
			'url':'list/'+url+'.html?'+rand+'',
			success:function(da){
				if(isphone && NOWURL.substr(0,4)=='file' && !da)showAlert('IOS的app入口地址必须使用http开头');
				if(!da)da='无法加载：'+cans.url+'';
				xcy.addtabsok(da,rand, urlpms);
				if(apixhbool)api.hideProgress();
			},
			error:function(){
				js.unloading();
				js.msg('msg','加载出错');
				if(apixhbool)api.hideProgress();
			}
		}
		$.ajax(cans);
	},
	addtabsok:function(da, rand, urlpms){
		if(this.istabs(nowurl))this.tabscont[nowurl]=da;
		js.unloading();
		var s = da;
			s = s.replace(/\{rand\}/gi, rand);
			s = s.replace(/\{params\}/gi, "var params="+urlpms+";");
		$('#mainbody').html(s);
		if(apixhbool && !initAppbool){
			xcy.initApp();
		}
		this.resiezess();
	},
	grouptype:function(did, lx, blx){
		var s = '',col='',s1='',s2=' style="font-weight:lighter"';
		if(lx=='gout')did='-1';
		if(isempt(did) || (lx && lx=='user'))return s;
		if(did=='1'){s1='全员';col='#93cdf2';s=' <b'+s2+' class="reimlabel">'+s1+'</b>';}
		if(did=='-1'){s1='外部';col='#99CCCC';s=' <b'+s2+' class="reimlabel2">'+s1+'</b>';}
		if(did>1){s1='部门';col='#f9af7e';s=' <b'+s2+' class="reimlabel1">'+s1+'</b>';}
		if(blx)return [s1,col];
		return s;
	},
	getface:function(face,o1){
		if(!face)return '';
		if(face && face.substr(0,4)!='http')face=''+apiurl+''+face+'';
		return face;
	},
	showerror:function(cw){
		if(this.touchobj)this.touchobj.ondownerror(cw);
	},
	reloadok:function(){
		if(this.touchobj)this.touchobj.ondownok();
	},
	tabscont:{},
	istabs:function(url){
		var str = ',home,ying,lianxi,user,';
		return str.indexOf(','+url+',')>-1;
	},
	index:0,
	historyarr:[],
	agentarr:[],
	dakaiurl:function(name,url){
		if(apixhbool){
			api.openWin({url:url,progress:'true'});
		}else{
			js.location(url);
		}
	},
	opennei:function(ustr,glx, cans){
		var bstr=jm.base64encode(JSON.stringify(ustr));
		var url = 'index.html?bstr='+bstr+'&nei=true&rand='+js.getrand()+'';
		if(glx)return url;
		this.openurl(url, cans);
	},
	openurl:function(dz, cans){;
		if(apixhbool){
			var url = ''+NOWURL+''+dz+'';
			if(!cans)cans={};
			cans.url = url;
			cans.isnei = true;
			cans.progress = 'true';
			api.openWin(cans);
		}else{
			js.location(dz);
		}
	},
	openurls:function(dz,nae){
		if(dz.substr(0,4)!='http'){
			dz = ''+apiurl+''+dz+'&adminid='+adminid+'&token='+TOKEN+'';
		}
		if(apixhbool){
			var opentype='nei',nurl=location.href;
			opentype=jm.base64encode(nurl);
			dz+='&hideheader=true&opentype='+opentype+'';
			if(!nae)nae='.';
			api.openWin({url:dz,title:nae,progress:'true'});
		}else{
			js.location(dz);
		}
	},
	opennewurl:function(url, na){
		var ustr={name:na,url:'opennewurl',dizhi:url};
		this.opennei(ustr);
	},
	openchat:function(na,type,id){
		var ustr={name:na,type:type,id:id,url:'chat'};
		this.opennei(ustr, false, {menu:'all',winid:'rockchat'});
	},
	back:function(){
		if(apixhbool){
			api.closeWin();	
		}else{
			js.back();
		}
	},
	more:function(){
		js.showmenu({
			data:[{name:'刷新'}],
			onclick:function(d){
				js.loading();
				js.reload();
			}
		})
	},
	sendEvent:function(lx,na,da){
		if(!apixhbool)return;
		if(!da)da={};
		da.stype = lx;
		if(!na)na='rockhome';
		da.name = na;
		api.sendEvent(da);
	},
	showtheme:function(lx){
		$('#autostyle').remove();
		maincolor = lx;
		if(!maincolor)maincolor='#1389D3';
		lx = maincolor;
		var str = 'body{--main-color:'+lx+';}';
		$('body').append('<style id="autostyle">'+str+'</style>');
	},
	fontsize:function(){
		return sizearr[bodyzoom];
	},
	stopPush:function(){
		if(!apixhbool)return;
		api.rockFun('stopService',{});
		api.rockFun('unregPush',{});
	},
	loginExit:function(){
		js.setoption(TOKENKEY,'');
		js.setoption('adminid','');
		js.setoption('showmyinfo','');
		js.setoption('agentjson','');
		js.setoption('historyjson','');
		js.setoption('silderarr','');
		js.setoption('myhomenum','');
		js.setoption('deptjson', '');
		js.setoption('userjson', '');
		js.setoption('groupjson', '');
		TOKEN  = '';
		adminid= '0';
		js.setoption('maxupgloble', '');
		clearTimeout(xcy.homeretime);
		xcy.outgroup.clearchache()
		this.stopPush();
	},
	longmenu:function(){
		setTimeout('touchobja=false',200);
		if(!touchobja)return;
		touchobja.islongbool = true;
		touchobja.onlongclick();
	},
	addback:function(){
		
	},
	phoneaction:function(){
		api.startWebsocket = function(conf){
			if(this.ws)this.ws.close();
			this.ws 	= new WebSocket(jm.base64decode(conf.wsurl));
			this.ws.onopen = function(){
				this.send('{"from":"'+conf.recid+'_app","adminid":"'+conf.adminid+'","atype":"connect","sendname":"'+conf.adminname+'"}');
				xcy.broadWebsocket('open');
			}
			this.ws.onclose = function(e){
				xcy.broadWebsocket('close');
			};
			this.ws.onmessage = function(evt){
				var ds = JSON.parse(evt.data);
				xcy.broadWebsocket('message', ds);
			};
		}
	},
	broadWebsocket:function(stype, data){
		if(!data)data='';
		var da = {name:'rockhome',stype:'websocket',sockettype:stype,socketdata:data};
		homeim.websocketData(da);
	}
}

//长按处理
function touchclass(cans){
	var me = this;
	this.onlongclick = function(){}
	this.onclick	 = function(){}
	this.initbool 	 = false;
	this.islongbool	 = false;
	for(var i in cans)this[i]=cans[i];
	this.touchstart=function(o1,evt){
		touchobja 		= this;
		this.islongbool = false;
		if(!this.initbool){
			o1.addEventListener('click', function(){
				me._onclick(this, event);
			}, false);
		}
		this.obj = o1;
		this.initbool	= true;
		clearTimeout(this.touchtime);
		this.touchtime  = setTimeout('touchobja=false',1000);
		return true;
	}
	this._onclick=function(o1, evt){
		if(!this.islongbool)this.onclick(o1, evt);
	}
}
</script>
</head>
<body>
<div id="header" style="display:none">
	<div class="r-header">
		<div class="r-header-text" id="header_title"></div>
		<div onclick="xcy.more()" class="r-header-btn r-position-right">···</div>
		<div onclick="xcy.back()" style="display:none" id="backbtn" class="r-header-btn r-position-left">く</div>
	</div>
	<div style="height:50px;overflow:hidden"></div>
</div>

<div id="mainbody" class="r-touch" style="height:350px;position:relative">
	<div align="center" onclick="js.reload()" style="margin-top:50px"><i style="height:40px;width:40px" class="rock-loading"></i></div>
</div>

<link rel="stylesheet" href="res/fontawesome/css/font-awesome.min.css">
<script src="res/plugin/jquery-rocksilder.js"></script>
<script src="res/plugin/jquery-rockdoupull.js?abc"></script>
<script src="js/outgroup.js"></script> 
</body>
</html>
