package com.baselib;


import android.util.Log;


public final class CLog{

	private static int cishu = 0;

	public static void debug(String msg)
	{
		debug(msg, "");
	}

	public static void debug(String msg, String type)
	{
		if(AA.DEBUG){
			if(msg==null)msg = "NULL";
			cishu ++;
			if(type.length() >0)msg = "["+type+"]"+msg;
			Log.v(AA.APPPAGE, ""+cishu+"."+msg);
		}
	}

	public static void error(String msg)
	{
		error(msg, "");
	}

	public static void error(String msg, String type)
	{
		if(AA.DEBUG){
			if(msg==null)msg = "NULL";
			cishu ++;
			if(type.length() >0)msg = "["+type+"]"+msg;
			Log.e(AA.APPPAGE, ""+cishu+"."+msg);
		}
	}
}