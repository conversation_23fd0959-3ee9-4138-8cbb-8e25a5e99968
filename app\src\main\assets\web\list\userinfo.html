<script>
$(document).ready(function(){
	{params}
	
	var c = {
		init:function(){
			var nowuserstr = js.getoption('nowuser');
			this.laiyuan = params.laiyuan;
			
			params = JSON.parse(nowuserstr);
			if(this.laiyuan=='chat'){
				var nstr = js.getoption('userjson');
				if(nstr){
					var uarr = js.decode(nstr),i,len=uarr.length;
					for(i=0;i<len;i++){
						if(uarr[i].id==params.id){
							params = uarr[i];
							break;
						}
					}
				}
			}
			
			$('#userinfo_name').html(params.name);
			$('#userinfo_deptname').html(params.deptallname);
			
			get('userinfo_myface').src=xcy.getface(params.face, get('userinfo_myface'));
			$('#userinfo_ranking').html(params.ranking);
			var sj = params.mobile
			if(sj)sj=''+sj.substr(0,3)+'****'+sj.substr(-4)+'';
			$('#userinfo_mobile').html(sj);
			$('#userinfo_email').html(params.email);
			$('#userinfo_tel').html(params.tel);
			$('#userinfo_id').html(params.id);
			if(adminid!=1)$('#userinfo_id').parent().remove();
			if(js.getoption('tonghuabo')=='1')$('#tonghuabtn').show();
		},
		faxiaoxi:function(){
			if(this.laiyuan=='chat'){
				api.closeWin();
			}else{
				xcy.openchat(params.name,'user', params.id);
			}
		},
		callphone:function(){
			var sj = params.mobile;
			if(!sj)return;
			if(apixhbool){
				api.rockFun('callPhone',{phone:sj});
			}
		},
		calltel:function(){
			var sj = params.tel;
			if(!sj)return;
			if(apixhbool){
				api.rockFun('callPhone',{phone:sj});
			}
		},
		yuyintongh:function(){
			api.rockFun('tonghua',{
				id:params.id,
				name:params.name,
				ranking:params.ranking,
				face:params.face,
				myid:adminid
			});
		},
		viewimg:function(){
			var src = get('userinfo_myface').src;
			if(apixhbool){
				api.imageView({
					url:src
				});
			}else{
				js.showplugin('imgview',{
					url:src,ismobile:true
				});
			}
		}
	}
	c.init();
	js.initbtn(c);
	
	//$('#backbtn').show();
	//xcy.backcall.userinfo=function(){
	//	xcy.addtabs('联系人','lianxi');
	//}
});
</script>

<div class="rock_cells" style="margin-top:15px">
	<div class="rock_cell">
		<div class="rock_cell__hd" style="position: relative;margin-right: 10px;">
			<img src="images/noface.png"  clickevt="viewimg" onclick="" class="r-face" id="userinfo_myface"  style="width: 60px;display: block;height:60px"/>
		</div>
		<div class="rock_cell__bd">
			<p id="userinfo_name" class="fontsize">您好</p>
			<p id="userinfo_deptname" style="color: #aaaaaa;margin-top:5px;font-size:14px;">部门</p>
		</div>
	</div>
</div>	



<div class="rock_cells__title">联系方式</div>
<div class="rock_cells">
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			职位
		</div>
		<div style="color: #888888;" id="userinfo_ranking"></div>
	</div>
	<div class="rock_cell rock_cell_access" clickevt="callphone">
		<div class="rock_cell__bd fontsize">
			手机号
		</div>
		<div class="rock_cell__ft" style="color: #888888;" id="userinfo_mobile"></div>
	</div>
	<div class="rock_cell rock_cell_access">
		<div class="rock_cell__bd fontsize">
			邮箱
		</div>
		<div style="color: #888888;"  id="userinfo_email"></div>
	</div>
	<div class="rock_cell rock_cell_access" clickevt="calltel">
		<div class="rock_cell__bd fontsize">
			电话
		</div>
		<div class="rock_cell__ft" style="color: #888888;" id="userinfo_tel"></div>
	</div>
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			用户ID
		</div>
		<div style="color: #888888;" id="userinfo_id"></div>
	</div>
</div>

<div style="margin-top:15px;color:var(--main-color)" class="rock_btn_cell fontsize" clickevt="faxiaoxi"><i class="icon-comment-alt"></i> 发消息</div>

<div style="color:var(--main-color);border-top:0;display: none;" id="tonghuabtn" class="rock_btn_cell fontsize" clickevt="yuyintongh"><i class="icon-facetime-video"></i> 音视频通话</button></div>

<div style="height:15px"></div>

