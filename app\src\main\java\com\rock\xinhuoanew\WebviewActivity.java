package com.rock.xinhuoanew;



import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.net.Uri;
import android.os.Bundle;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;



import android.webkit.JsResult;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.LinearLayout;


import com.dialog.DialogLoad;
import com.dialog.DialogMenu;
import com.dialog.DialogPopup;
import com.baselib.CLog;
import com.baselib.CallBack;

import com.baselib.AA;
import com.baselib.Json;
import com.baselib.Rock;
import com.baselib.RockFile;



public class WebviewActivity extends BaseActivity {

    protected int openCishu = 0;
    private AndroidJs Androidjs;
    protected WebView webview;
    protected String nowUrl = "";

    public void gotoUrl(String url)
    {
        webview = new WebView(this, null);
        LinearLayout layout = findViewById(R.id.forum_contextshow);
        layout.addView(webview, -1, -1);
        initweb();
        webview.setOverScrollMode(View.OVER_SCROLL_NEVER);
        webview.loadUrl(url);
    }


    protected void onhandleCallback(int what, int arg1, String bstr){
        super.onhandleCallback(what, arg1, bstr);
        if(what==AA.ASCIPT_CALL){
            webview.loadUrl("javascript:"+bstr);
        }
    }

    protected void setOnLongClick(View.OnLongClickListener onvse)
    {
        if(webview==null)return;
        webview.setOnLongClickListener(onvse);
    }

    //重新定义
    protected String getUserAgent(){return  "";}

    @SuppressLint({ "SetJavaScriptEnabled", "JavascriptInterface" })
    @JavascriptInterface
    private void initweb()
    {
        //避免输入法界面弹出后遮挡输入光标的问题
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE | WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);

        webview.setBackgroundColor(Color.argb(0, 0, 0, 0));//设置透明背景
        webview.setDrawingCacheEnabled(true);
        webview.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        WebSettings webSettings = webview.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAppCacheEnabled(true); //开启缓存
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        webSettings.setSupportMultipleWindows(true);
        webSettings.setAllowFileAccess(true);	//可以访问文件权限
        //webSettings.setAppCachePath(RockFile.getCachedir(this, "webviewCache"));
        webSettings.setUserAgentString(webSettings.getUserAgentString()+";XINHUOAAPP ANDROID V"+ BuildConfig.VERSION_NAME+"");
        webSettings.setAllowUniversalAccessFromFileURLs(true); //ajax访问用的
        webSettings.setAllowFileAccessFromFileURLs(true);
        webSettings.setGeolocationEnabled(true);

        //webSettings.setCacheMode(WebSettings.LOAD_DEFAULT); //缓存模式
        //webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);


        //字体大小100,1倍数
        if(Rock.fontsize==1)webSettings.setTextZoom(110);
        if(Rock.fontsize==2)webSettings.setTextZoom(120);


        //注册js
        Androidjs   = new AndroidJs(this, mActivity);
        webview.addJavascriptInterface(Androidjs, "appxinhu");

        webview.setWebViewClient(new WebViewClient(){
            public void onPageStarted(WebView view, String url, Bitmap favicon){
                nowUrl = url;
            }

            public void onPageFinished(WebView view, String url){
                openCishu++;
                DialogLoad.hide();
                initParams();
                String str = Androidjs.getScriptstr("0");
                view.loadUrl("javascript:" + str + ";if(window.apiready)apiready();");
                onWebviewFinished();
                handleSend(AA.WEBVIEW_OPEN, "open_"+openCishu+"");
            }


            //跳转的时候处理
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                String qianz = url.substring(0,4);
               // CLog.debug(url);
                if(qianz.equals("tel:")) {
                    String tel = url.substring(4);
                    DialogMenu.show(mActivity, "复制号码,拨打"+tel+"电话", new CallBack(){
                        public void backhttp(int code, int arg1, String bstr) {
                            if(code==0){
                                Rock.copy(mActivity, tel);
                                Rock.Toast(mActivity, "已复制");
                            }
                            if(code==1){
                                Xinhu.callPhone(mActivity, tel);
                            }
                        }
                    });
                    return true;
                }
                return false;
            }
        });

        webview.setWebChromeClient(new WebChromeClient(){
            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
                setTitles(title);//标题改变时
            }


            /**
             * 覆盖默认的window.alert展示界面，避免title里显示为“：来自file:////”
             */
            public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
                return super.onJsAlert(view, url, message, result);
            }
            public boolean onJsBeforeUnload(WebView view, String url,String message, JsResult result) {
                return super.onJsBeforeUnload(view, url, message, result);
            }

            @Override
            public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
                mUploadMessage6 = filePathCallback;
                Intent i = new Intent(Intent.ACTION_GET_CONTENT);
                i.addCategory(Intent.CATEGORY_OPENABLE);
                i.setType("*/*");
                startActivityForResult(Intent.createChooser(i, "文件选择"), AA.RESULTCODE_CHANGEFILE);
                return true;
            }
        });
    }

    //选择文件的
    private ValueCallback<Uri[]> mUploadMessage6;
    protected void onActivityResult(int requestCode, int resultCode, Intent data){
        super.onActivityResult(requestCode, resultCode, data);
        if(requestCode==AA.RESULTCODE_CHANGEFILE) {
            Uri result = data == null || resultCode != RESULT_OK ? null : data.getData();
            if (mUploadMessage6 != null) {
                if (result != null) {
                    mUploadMessage6.onReceiveValue(new Uri[]{result});
                }else{
                    mUploadMessage6.onReceiveValue(new Uri[]{});
                }
                mUploadMessage6 = null;
            }
        }
        if(requestCode==AA.RESULTCODE_CAMERAPI && resultCode==RESULT_OK) {
            Bundle bundle   = data.getExtras();
            String filepath = bundle.getString("filepath");//拍照好的地址
            String filepaths = bundle.getString("filepaths");
            String callstr  = bundle.getString("callbackstr");
            String filesize  = bundle.getString("filesize");
            if(!Rock.isEmpt(callstr)){
                String base64Data = RockFile.fileTobase64(filepaths);
                handleCallSend(callstr, "{filepath:'"+filepath+"',filesize:"+filesize+",base64Data:'"+base64Data+"'}");
            }
        }
        if(requestCode==AA.RESULTCODE_SCANAPI && resultCode==RESULT_OK) {
            Bundle bundle   = data.getExtras();
            String result   = bundle.getString("result");//扫码结果
            String callstr  = bundle.getString("callbackstr");
            if(!Rock.isEmpt(callstr)){
                handleCallSend(callstr, "{result:'"+result+"'}");
            }
        }
    }

    /**
     * 显示菜单
     * */
    protected void showMoreMenu()
    {
        if(menustring.equals("all")){
            handleCallSend(menucallstr, "{menuIndex:1,name:'"+menustring+"'}");
            return;
        }
        View v 		= (View)findViewById(R.id.more);
        String cont = "刷新";
        if(!Rock.isEmpt(menustring))cont+=","+menustring+"";
        DialogPopup.show(this, v, cont, new CallBack(){
            public void back(int code, String str){
                if(code==0){
                    DialogLoad.show(mActivity);
                    webview.reload();
                }else{
                    handleCallSend(menucallstr, "{menuIndex:"+code+",name:'"+str+"'}");
                }
            }
        });
    }

}