<script>
$(document).ready(function(){
	
	var c = {
		init:function(){
			get('myface').src=xcy.getface(apiurl+'images/logo.png', get('myface'));
			$('#appname').html(title);
			$('#versionv').html('Version '+api.appVersion+'');
		},
		opensss1:function(){
			xcy.opennei({name:'用户协议',url:'xyuser',nlogin:true});
		},
		opensss2:function(){
			xcy.opennei({name:'隐私政策',url:'xyyinsi',nlogin:true});
		},
		editpass:function(){
			if(adminid==''){
				js.msg('msg','请先登录');
			}else{
				xcy.opennei({name:'修改密码','url':'editpass'});
			}
		},
		jianyi:function(){
			rockprompt('','请输入您想说的话', function(txt){
				if(txt){
					api.showMsg({msg:'感谢您的支持'});
				}
			});
		},
		jieshao:function(){
			rockalert('感谢您的使用，我们会越做越好的。');
		}
	}
	js.initbtn(c);
	c.init();
});
</script>




<div align="center" style="padding:30px">
	<div><img id="myface" style="height:100px;width:100px;border-radius:10px" onclick="location.reload()" src="images/noface.png"></div>
	<div id="appname" style="font-size:20px;margin-top:10px" class="zhu">信呼OA</div>
	<div id="versionv" style="margin-top:10px">Version 1.00</div>
</div>

<div style="margin:0px 30px;">
	<div class="weui-cells">
		<div class="weui-cell weui-cell_access" clickevt="jieshao">
			<div class="weui-cell__bd fontsize">
				软件介绍
			</div>
			<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
		</div>
		<div class="weui-cell weui-cell_access" clickevt="jianyi">
			<div class="weui-cell__bd fontsize">
				建议投诉
			</div>
			<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
		</div>
	</div>
</div>

<div class="weui-footer" style="margin-top:50px">
	<p class="weui-footer__links">
		<a href="javascript:;" clickevt="opensss1" class="weui-footer__link">用户协议</a>
		<a href="javascript:;"  clickevt="opensss2" class="weui-footer__link">隐私政策</a>
	</p>
</div>