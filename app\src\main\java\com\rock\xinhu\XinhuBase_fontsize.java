
package com.rock.xinhu;


import android.content.Context;
import android.view.View;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import com.dialog.DialogMsg;
import com.baselib.AR;
import com.baselib.Rock;


public class XinhuBase_fontsize extends XinhuBase {


    public XinhuBase_fontsize(Context context, View view) {
        super(context, view);
    }

    public void initBase()
    {
        RadioButton radioBtn = (RadioButton) mView.findViewById(AR.getID("radioGroupButton"+Rock.fontsize+""));;
        radioBtn.setChecked(true);
    }

    public void setOnClickListener(View.OnClickListener cick)
    {
        View tv = mView.findViewById(AR.getID("button"));
        tv.setOnClickListener(cick);
        Rock.setBackground(tv);
    }

    public void onViewClick(int id)
    {
        if(id==AR.getID("button")){
            queding();
        }
    }

    public void onBack()
    {
        mObj.exitBack("fontsize", "");
    }

    private void queding(){
        RadioGroup  radiogroup = (RadioGroup) mView.findViewById(AR.getID("radioGroup1"));
        int selid = radiogroup.getCheckedRadioButtonId();
        RadioButton radioBtn2 = (RadioButton) radiogroup.findViewById(selid);

        String str = radioBtn2.getText().toString();
        int val = 0;
        if(str.equals("中号"))val=1;
        if(str.equals("大号"))val=2;
        Rock.fontsize = val;
        Rock.Sqlite.setOption("fontsize", ""+val+"");
        DialogMsg.success(mContext, "保存成功，需重启生效");
    }

}