package com.baselib;

import android.content.Context;



public final class SqliteClass extends SqliteHelper{
	


	//文件表
	public static final String tn_file					= "filerecord";
	public static final String[] tn_filefields			=  {"id","fileext","filepath","filename","thumbpath","filesizecn","filetype","optid","optname","adddt","filesize","downpath","downurl"};

	
	public SqliteClass(Context context) {
		super(context, AA.APPPAGE, AA.DB_VERSION);
	}
	

	//初始化时创建
	protected void initcreate(){

		//文件表
		createtable(tn_file,"fileext varchar(20),filepath varchar(50),downpath varchar(100),downurl varchar(300),filename varchar(50),filetype varchar(50),thumbpath varchar(50)" +
				",filesizecn varchar(10),optid INTEGER DEFAULT 0,optname varchar(20),filesize INTEGER DEFAULT 0,adddt datetime");


	}

	/**
	 * 清除说有表记录
	 * */
	public void clearalltable()
	{
		delete(tn_file, "1=1");
	}
	
}