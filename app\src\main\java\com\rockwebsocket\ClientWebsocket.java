package com.rockwebsocket;

public class ClientWebsocket {

    public static ClientWebsocket create(String url)
    {
        ClientWebsocket obj = new ClientWebsocket_orgjava(url);
        return obj;
    }

    protected ClientWebsocketCallBack websocketCallBack = null;
    protected String recid = "",adminid = "";

    public ClientWebsocket(String url) {
        websocketCallBack = new ClientWebsocketCallBack();
    }

    public void setClientWebsocketCallBack(ClientWebsocketCallBack call)
    {
        websocketCallBack = call;
    }

    /**
     * 链接
     * */
    public void connect()
    {

    }

    /**
     * 关闭
     * */
    public void close()
    {

    }

    /**
     * 发送信息
     * */
    public Boolean send(String str)
    {
        return false;
    }

    /**
     * 是否打开了
     * */
    public Boolean isOpen()
    {
        return false;
    }

    /**
     * 发送心跳包
     * */
    public Boolean sendBeat()
    {
        String scont = "{\"from\":\""+recid+"\"";
        scont += ",\"type\":\"rocksystembeat\"";
        scont += ",\"atype\":\"sends\", \"receid\":\""+adminid+"\"}";
        return this.send(scont);
    }

    /**
     * 设置信息
     * */
    public void setRecidAndmyid(String recid, String myid)
    {
        this.recid      = recid;
        this.adminid    = myid;
    }
}
