<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"

    android:orientation="vertical">

  <com.view.RockTextView
      android:id="@+id/title"
      android:layout_width="match_parent"
      android:layout_height="@dimen/menulist"
      android:background="@drawable/btn_tm"
      android:gravity="center"
      android:text="系统提示"
      android:textColor="@color/white"
      android:textSize="@dimen/listdp" />

  <View
      android:id="@+id/hang"
      android:layout_width="fill_parent"
      android:layout_height="1px"
      android:layout_marginLeft="10dp"
      android:layout_marginRight="10dp"
      android:background="@color/line2" />

</LinearLayout>