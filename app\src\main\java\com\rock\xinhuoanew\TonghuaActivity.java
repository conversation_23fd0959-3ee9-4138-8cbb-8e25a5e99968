package com.rock.xinhuoanew;




import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;


import androidx.core.app.ActivityCompat;

import com.baselib.AA;
import com.baselib.AR;
import com.baselib.CDate;
import com.baselib.CLog;
import com.baselib.CallBack;
import com.baselib.Json;
import com.baselib.RCall;
import com.baselib.RPlay;
import com.baselib.RTouchListener;
import com.baselib.Rock;
import com.baselib.RockHttp;
import com.dialog.Dialog;
import com.dialog.DialogAudio;
import com.rock.xinhuRtc.TencentRTC;
import com.tencent.rtmp.ui.TXCloudVideoView;
import com.tencent.trtc.TRTCCloudDef;
import com.view.RockImageView;

import org.w3c.dom.Text;

import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;


public class TonghuaActivity extends BaseActivity {

    protected BroadcastReceiver tonghuaReceiver;

    /**
     * 拨号类型0语音，1视频
     */
    private int calltype = 0;

    /**
     * 事件秒
     */
    private int calltime = 0;

    private int isHUtype = 0;

    private Timer sTimer = null;

    private TextView titletv, titletvs;


    private Boolean callOk = false, isCall = true, isJIETONG = false, authBool = false;

    /**
     * 是否点击接听拒绝和取消
     * */
    private Boolean allQuxiao = false;

    /**
     * 给对于人id
     */
    private String toid = "";

    /**
     * 对方名字
     */
    private String toname = "";

    /**
     * 我自己id
     */
    private String myid = "";

    /**
     * 房间号
     */
    private String channel = "";

    /**
     * 进入的密钥
     */
    private String channeltoken = "";


    private String sdkappid = "";

    private int what_init = 1, what_time = 2, what_jujue = 3, what_jieting = 4, what_close = 5, what_cancel = 6, what_guan = 7;


    protected void initCreate() {

        setContentView(R.layout.activity_tonghua);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setNavigationBarColor(Color.parseColor("#555555"));
        }

        Bundle bundle = this.getIntent().getExtras();
        String name = bundle.getString("name");
        String paramsstr = bundle.getString("paramsstr");
        //CLog.debug(paramsstr, "-tonghua");
        Map<String, String> ret = Json.getJsonObject(paramsstr);
        String face = Rock.getMapString(ret, "face");
        if (!Rock.isEmpt(face)) {
            RockImageView img = (RockImageView) findViewById(R.id.face);
            img.setPath(face);
            img = (RockImageView) findViewById(R.id.faces);
            img.setPath(face);
        }
        this.toid = Rock.getMapString(ret, "id");
        this.myid = Rock.getMapString(ret, "myid");
        this.toname = Rock.getMapString(ret, "name");
        titletv = (TextView) findViewById(R.id.title);
        titletvs = (TextView) findViewById(R.id.titles);

        String channel = Rock.getMapString(ret, "channel");
        String type = Rock.getMapString(ret, "type");
        if (!Rock.isEmpt(channel)) {
            isCall = false;
            this.channel = channel;
            this.calltype = Integer.parseInt(type);
        }

        //可能对应已经取消了
        if (!isCall) {
            if (RCall.isOpen == 0) {
                cancel();
                return;
            }
        } else {
            if (RCall.isOpen == 2) {
                cancel();
                return;
            }
        }
        RCall.regCall(this);

        TextView tv = (TextView) findViewById(R.id.name);
        tv.setText(this.toname);
        tv = (TextView) findViewById(R.id.names);
        tv.setText(this.toname);

        findViewById(R.id.btn_yuyin).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_shipin).setOnClickListener(OnViewClickListener);
        findViewById(R.id.back).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_cancel).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_maike).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_yangsq).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_vadio).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_zhuan).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_quxiao).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_tongyi).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_jujue).setOnClickListener(OnViewClickListener);
        findViewById(R.id.zongmain).setOnClickListener(OnViewClickListener);
        findViewById(R.id.btn_callcancal).setOnClickListener(OnViewClickListener);

        Rock.setBackground(findViewById(R.id.btn_yuyin), 180, "#ff6600");
        Rock.setBackground(findViewById(R.id.btn_shipin), 180, "#339933");
        Rock.setBackground(findViewById(R.id.btn_tongyi), 180, "#339933");
        Rock.setBackground(findViewById(R.id.btn_cancel), 180, "#D9534F");
        Rock.setBackground(findViewById(R.id.btn_quxiao), 180, "#D9534F");
        Rock.setBackground(findViewById(R.id.btn_jujue), 180, "#D9534F");
        Rock.setBackground(findViewById(R.id.btn_callcancal), 180, "#D9534F");
        Rock.setBackground(findViewById(R.id.btn_maike), 180, "#666666");
        Rock.setBackground(findViewById(R.id.btn_yangsq), 180, "#666666");
        Rock.setBackground(findViewById(R.id.btn_vadio), 180, "#666666");

        checkPermission();//权限判断

        Xinhu.NotificationcancelAll(this);

        //被叫端，需要接听
        if (isCall == false) beiCall();

        addEventListener(AA.TONGHUA_ACTION, ""); //注册一个广播
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        stopStimer();
        RCall.unregCall(this);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        CLog.debug("打开？");
    }

    @Override
    protected void onPause() {
        super.onPause();//回到后台
        if (isJIETONG) {
            Xinhu.Notification(this, "正在" + this.getTypes() + "通话", "正在与" + this.toname + "" + this.getTypes() + "通话...", "",-1,0);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Xinhu.NotificationcancelAll(this);
    }

    @Override
    protected void onhandleCallback(int what, int arg1, String bstr) {
        super.onhandleCallback(what, arg1, bstr);
        if (what == what_time) {
            startStimers(arg1);
            return;
        }
        if (what == what_init) {
            Json.strParse(arg1, bstr, new CallBack() {
                public void backMap(Map<String, String> a) {
                    initOkback(a);
                }
            }, new CallBack() {
                public void backstr(String bstr) {
                    initError(bstr);
                }
            });
        }
        if (what == what_jujue) beiJujueb(bstr, arg1);
        if (what == what_close) cancel();
        if (what == what_cancel) startCallcancels();
        if (what == what_guan) startCallguans();
        if (what == what_jieting) beiTongyis(bstr, arg1);
    }

    private void initError(String msg) {
        setTishi(msg);
        RPlay.stop();
        isHUtype = 2;//呼叫错误
    }


    private void stopStimer() {
        if (sTimer != null) sTimer.cancel();
        sTimer = null;
    }

    private String getTypes() {
        String types = (calltype == 0) ? "语音" : "视频";
        return types;
    }

    private void startStimer(int lx) {
        if (sTimer == null) sTimer = new Timer();
        calltime = -1;
        sTimer.schedule(new TimerTask() {
            public void run() {
                Rock.sendHandler(myhandler, what_time, "", lx);
            }
        }, 0, 1000);
    }

    private void startStimers(int lx) {
        calltime++;
        if (lx == 0) {
            String msg = "" + this.getTypes() + "拨号中...已等待" + calltime + "秒";
            setTishi(msg);
            if (calltime > 30) startCallcancel(false);
        }
        //接通数秒
        if (lx == 1) {
            int h = (int) (calltime / 60), i = calltime - (60 * h);
            String hs = (h < 10) ? "0" + h + "" : "" + h + "";
            String is = (i < 10) ? "0" + i + "" : "" + i + "";
            String msg = "" + this.getTypes() + "通话中(" + hs + ":" + is + ")";
            setTishi(msg);
        }
    }

    private void setTishi(String msg) {
        titletv.setText(msg);
        titletvs.setText(msg);
    }

    private void cancelCall() {
        String msg = "对方无应答，已取消";
        setTishi(msg);
        Rock.Toast(this, msg);
        cancel();
    }

    protected void onViewClick(View v) {
        int id = v.getId();
        if (id == R.id.back) {
            finish();
        }
        if (id == R.id.btn_quxiao || id == R.id.btn_cancel || id == R.id.btn_callcancal) {
            startCallcancel(true);
        }
        if (id == R.id.btn_yuyin) startCall(0);
        if (id == R.id.btn_shipin) startCall(1);
        if (id == R.id.btn_maike) changemaike();
        if (id == R.id.btn_yangsq) changeyangsq();
        if (id == R.id.btn_vadio) changevadio();
        if (id == R.id.btn_jujue) beiJujue();
        if (id == R.id.btn_tongyi) beiTongyi();
        if (id == R.id.zongmain) hideBtn();//隐藏接通按钮
        if (id == R.id.btn_zhuan) zhuanCarme();
        if (id == R.id.txcvv_main_localv) zhuanlocalRomet();
    }

    /**
     * 全部停止关闭
     * */
    private void allStop()
    {
        stopStimer();
        RPlay.stop();
        stopServer();
    }

    /**
     * 取消了关闭
     */
    private void cancel()
    {
        allStop();
        if (isJIETONG) TencentRTC.stop();
        isJIETONG = false;
        finish();
    }

    private void stopServer() {
        Xinhu.startService(this, Xinhu.SERVICETYPE_TONGHUAE, "");
    }

    //点击呼叫
    private void startCall(int type) {
        if (!authBool) {
            Dialog.alert(this, "未开启相应权限，无法使用");
            return;
        }
        calltype = type;
        isHUtype = 1;
        String types = (calltype == 0) ? "语音" : "视频";
        String msg = "" + types + "拨号初始化中...";
        setTishi(msg);
        RockHttp.get(Rock.getApiUrls("tonghua", "thinit"), myhandler, what_init, "id=" + this.toid + "&type=" + type + "&rtctype=1", null);

        findViewById(R.id.back).setVisibility(View.GONE);
        findViewById(R.id.btn_callstart).setVisibility(View.GONE);
        findViewById(R.id.btn_callcancals).setVisibility(View.VISIBLE);
    }

    /**
     * 接通后显示按钮
     */
    private void btnShow() {
        findViewById(R.id.btn_callcancals).setVisibility(View.GONE);
        findViewById(R.id.btn_callone).setVisibility(View.VISIBLE);
        if (calltype == 0) {
            findViewById(R.id.btn_vadiom).setVisibility(View.GONE);
            findViewById(R.id.btn_quxiaom).setVisibility(View.VISIBLE);
        } else {
            findViewById(R.id.btn_shipinm).setVisibility(View.VISIBLE);
            findViewById(R.id.namediv).setVisibility(View.GONE);
            findViewById(R.id.namedivs).setVisibility(View.VISIBLE);
        }
    }

    private TXCloudVideoView localView, remoteView;

    /**
     * 创建接通的组件
     */
    private void createRTC() {
        TencentRTC.create(this, new CallBack() {
            @Override
            public void back(int code, String bstr) {
                onCallBack(code, bstr);
            }
        });
        TencentRTC.startlocalAudio();
        if (calltype == 1) {
            localView = new TXCloudVideoView(this);
            localView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            LinearLayout localv = (LinearLayout) findViewById(R.id.txcvv_main_localv);
            localv.addView(localView);
            TencentRTC.startlocalVideo(localView, true);

            remoteView = new TXCloudVideoView(this);
            remoteView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            LinearLayout localr = (LinearLayout) findViewById(R.id.zongmain);
            localr.addView(remoteView);
            TencentRTC.startRemoteVideo(remoteView, "rock" + this.toid);

            findViewById(R.id.txcvv_main_localv).setOnClickListener(OnViewClickListener);
        }
        TencentRTC.joinChannel(this.sdkappid, this.myid, this.channel, this.channeltoken);
        callOk = true;
    }

    //切换镜头
    private void zhuanlocalRomet()
    {
        if(vadioBool)TencentRTC.startlocalVideo(remoteView, TencentRTC.isqianVideo);
        TencentRTC.startRemoteVideo(localView, "rock" + this.toid);
        TXCloudVideoView lov = localView;
        TXCloudVideoView rov = remoteView;
        localView  = rov; remoteView = lov;
    }


    /**
     * 完成开始呼叫了
     * */
    private void initOkback(Map<String, String> a)
    {
        this.channel        = a.get("channel");
        this.channeltoken   = a.get("token");
        this.sdkappid       = a.get("appid");
        startStimer(0);
        RCall.play(this, "call.mp3", true);
        startServer();
    }

    @Override
    protected void onBroadcastReceiver(String content) {
        CLog.error("收到电话："+content);
        callMessage(Json.getJsonObject(content));
    }

    /**
     * 收到消息判断状态
     * */
    private void callMessage(Map<String, String> a)
    {
        String callt = Rock.getMapString(a, "calltype");
        if(Rock.equals(callt, "cancel")){
            setTishi("对方已取消");
            allStop();
            findViewById(R.id.btn_calljie).setVisibility(View.GONE);
            allQuxiao = true;
            cancelYan();
        }
        if(this.isCall) {
            if (Rock.equals(callt, "tongyi")) {
                startCallOk();//同意
            }
            if (Rock.equals(callt, "jujue")) {
                startCalljujue();//拒绝
            }
        }else{
            if(!allQuxiao && Rock.equals(callt, "tongyi")) {
                allStop();
                cancelYan();
                findViewById(R.id.btn_calljie).setVisibility(View.GONE);
                setTishi("已在另端接听");
            }
            if(!allQuxiao && Rock.equals(callt, "jujue")) {
                allStop();
                cancelYan();
                findViewById(R.id.btn_calljie).setVisibility(View.GONE);
                setTishi("已在另端拒绝");
            }
        }
        if (Rock.equals(callt, "jiesu")) {
            findViewById(R.id.btn_callone).setVisibility(View.GONE);
            startCalljiesu(false);//结束
        }
        //来电话，你又打开这个页面又没有呼叫
        if (Rock.equals(callt, "call") && isCall && isHUtype == 0) {
            cancel();
        }
    }

    /**
     * 呼叫取消 / 挂电话
     * bo true主动取消，false超时取消
     * */
    private void startCallcancel(Boolean bo)
    {
        if(isHUtype ==2){
            cancel();
            return;
        }
        stopStimer();
        if(!this.isJIETONG) {
            String lx = bo ? "" : "无人接听";
            setTishi("" + lx + "取消呼叫中...");
            RPlay.stop();
            String state = "3";//5时超时取消
            RockHttp.get(Rock.getApiUrls("tonghua", "cancel"), myhandler, what_cancel, "channel=" + this.channel + "&state=" + state + "", null);
        }else{
            startCallguan();
        }
    }
    private void startCallcancels()
    {
        setTishi("已取消呼叫");
        cancelYan();
    }


    //回调的处理
    private void onCallBack(int code, String bstr)
    {
        //进来房间就是接通了
        if(code == RCall.ROOM_IN){
            startCallOk();
        }

        //离开房间就是结束
        if(code == RCall.ROOM_OUT){
            startCalljiesu(false);
        }
    }


    private void hideBtn()
    {
        if(isJIETONG){
            View view = findViewById(R.id.btn_callone);
            if(view.getVisibility()==View.VISIBLE){
                view.setVisibility(View.GONE);
            }else{
                view.setVisibility(View.VISIBLE);
            }
        }
    }


    /**
     * 对方接通了主角被叫
     * */
    private void startCallOk()
    {
        if(isJIETONG)return;//已经接通就不用重复运行
        btnShow();
        createRTC();
        setTishi("已接通，通话中...");
        stopStimer();
        RPlay.stop();
        startStimer(1);
        isJIETONG  = true;
        startServer();
    }
    private void startServer()
    {
        String url = Rock.getApiUrls("tonghua", "state")+"&channel="+this.channel+"";
        Xinhu.startService(this, Xinhu.SERVICETYPE_TONGHUA, url);
    }

    /**
     * 通话结束(对方挂掉)
     * bool false 对方挂断，true，我自己挂掉
     */
    private void startCalljiesu(Boolean bool)
    {
        if(!isJIETONG)return;
        setTishi("通话已结束");
        isJIETONG = false;
        stopStimer();
        if(bool == false){
            cancelYan();
        }
    }

    /**
     * 对方拒绝
     * */
    private void startCalljujue()
    {
        if(isJujue)return;
        setTishi("对方拒绝通话");
        isJujue = true;
        stopStimer();
        cancelYan();
    }
    private Boolean isJujue = false;


    private void changemaike(){
        if(!callOk)return;
        TextView tv = (TextView)findViewById(R.id.btn_maikemsg);
        TextView tvi = (TextView)findViewById(R.id.btn_maikeicon);
        if(Rock.contain(tv.getText().toString(), "已开")){
            tv.setText("麦克风已关");
            tvi.setText(AR.getString("icon_maikfx"));
            TencentRTC.mTRTCCloud.muteLocalAudio(true);
        }else{
            tv.setText("麦克风已开");
            tvi.setText(AR.getString("icon_maikf"));
            TencentRTC.mTRTCCloud.muteLocalAudio(false);
        }
    }

    private void changeyangsq(){
        if(!callOk)return;
        TextView tv = (TextView)findViewById(R.id.btn_yangsqmsg);
        TextView tvi = (TextView)findViewById(R.id.btn_yangsqicon);
        if(Rock.contain(tv.getText().toString(), "已开")){
            tv.setText("扬声器已关");
            tvi.setText(AR.getString("icon_shengx"));
            TencentRTC.setAudioRoute(TRTCCloudDef.TRTC_AUDIO_ROUTE_EARPIECE);
        }else{
            tv.setText("扬声器已开");
            tvi.setText(AR.getString("icon_sheng"));
            TencentRTC.setAudioRoute(TRTCCloudDef.TRTC_AUDIO_ROUTE_SPEAKER);
        }
    }

    private Boolean vadioBool = true;
    private void changevadio(){
        if(!callOk)return;
        TextView tv = (TextView)findViewById(R.id.btn_vadiomsg);
        TextView tvi = (TextView)findViewById(R.id.btn_vadioicon);
        if(Rock.contain(tv.getText().toString(), "已开")){
            tv.setText("摄像头已关");
            tvi.setText(AR.getString("icon_vadiox"));
            vadioBool = false;
            TencentRTC.stoplocalVideo();
        }else{
            tv.setText("摄像头已开");
            tvi.setText(AR.getString("icon_vadio"));
            vadioBool = true;
            TencentRTC.startlocalVideo(localView, TencentRTC.isqianVideo);
        }
    }

    private void zhuanCarme(){
        if(!callOk)return;
        TextView tv = (TextView)findViewById(R.id.btn_vadiomsg);
        if(Rock.contain(tv.getText().toString(), "已开")){
            if(TencentRTC.isqianVideo){
                TencentRTC.startlocalVideo(localView, false);
            }else{
                TencentRTC.startlocalVideo(localView, true);
            }
        }
    }

    protected void onKeyBack(){
        if((isHUtype==0 || isHUtype==2) && isCall)cancel();
    }


    //延迟关闭
    private void cancelYan()
    {
        stopServer();
        new Timer().schedule(new TimerTask() {
            public void run() {
                Rock.sendHandler(myhandler, what_close, "", 0);
            }
        }, 1000);
    }


    /**
     * 被叫端开始了
     * */
    private void beiCall()
    {
        String msg = "邀请与您"+this.getTypes()+"通话";
        setTishi(msg);
        findViewById(R.id.btn_callstart).setVisibility(View.GONE);
        findViewById(R.id.btn_calljie).setVisibility(View.VISIBLE);
        findViewById(R.id.back).setVisibility(View.GONE);
        startServer();
        if(!RPlay.isPlay())RCall.playCallIn(this);
    }

    /**
     * 拒绝
     * */
    private void beiJujue()
    {
        allQuxiao = true;
        findViewById(R.id.btn_calljie).setVisibility(View.GONE);
        setTishi("拒绝中...");
        RPlay.stop();
        RockHttp.get(Rock.getApiUrls("tonghua", "jie"), myhandler, what_jujue, "channel="+this.channel+"&state=2", null);
    }

    private void beiJujueb(String bstr, int arg1)
    {
        Json.strParse(arg1, bstr, new CallBack(){
            public void backMap(Map<String, String> a) {
                setTishi("已拒绝通话");
                cancelYan();
            }
        }, new CallBack(){
            public void backstr(String bstr) {
                setTishi(bstr);
                cancelYan();
            }
        });
    }

    /*
    * 同意接通
    * */
    private void beiTongyi()
    {
        if(!authBool){
            Dialog.alert(this, "未开启权限，无法通话，请拒绝");
            return;
        }
        allQuxiao = true;
        findViewById(R.id.btn_calljie).setVisibility(View.GONE);
        setTishi("接通中...");
        RPlay.stop();
        RockHttp.get(Rock.getApiUrls("tonghua", "jietong"), myhandler, what_jieting, "channel="+this.channel+"&state=1", null);
    }
    private void beiTongyis(String bstr, int arg1)
    {
        Json.strParse(arg1, bstr, new CallBack(){
            public void backMap(Map<String, String> a) {
                beiTongyiss(a);
            }
        }, new CallBack(){
            public void backstr(String bstr) {
                setTishi(bstr);
                cancelYan();
            }
        });
    }
    private void beiTongyiss(Map<String, String> ret)
    {
        this.channeltoken   = Rock.getMapString(ret, "token");
        this.sdkappid       = Rock.getMapString(ret, "appid");
        startCallOk();
    }

    /**
     * 挂电话
     * */
    private void startCallguan()
    {
        findViewById(R.id.btn_callone).setVisibility(View.GONE);
        setTishi("挂断中...");
        isJIETONG = false;
        RCall.play(this, "gua.mp3", false);
        RockHttp.get(Rock.getApiUrls("tonghua", "jiesu"), myhandler, what_guan, "channel="+this.channel+"&toid="+this.toid+"", null);
    }
    private void startCallguans()
    {
        setTishi("通话已结束");
        TencentRTC.stop();
        cancelYan();
    }



    //权限判断
    private void checkPermission()
    {
        Boolean bo = Rock.checkPermission(this, Manifest.permission.RECORD_AUDIO);
        if(!bo) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.RECORD_AUDIO}, AA.PERMISSION_RECORD_AUDIO);
        }else{
            checkPermissions();
        }
    }
    private void checkPermissions(){
        Boolean bo = Rock.checkPermission(this, Manifest.permission.CAMERA);
        if(!bo) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, AA.PERMISSION_CAMERA);
        }else{
            authBool = true;
        }
    }
    protected Boolean onRequestPermissions(int code, int grant) {
        if(code == AA.PERMISSION_RECORD_AUDIO){
            if(grant == PackageManager.PERMISSION_DENIED){
                Dialog.alert(this, "拒绝了权限，无法使用麦克风功能");
            }
            if(grant == PackageManager.PERMISSION_GRANTED){
                checkPermissions();
            }
        }
        if(code == AA.PERMISSION_CAMERA){
            if(grant == PackageManager.PERMISSION_DENIED){
                Dialog.alert(this, "拒绝了权限，无法使用摄像头功能");
            }
            if(grant == PackageManager.PERMISSION_GRANTED){
                authBool = true;
            }
        }
        return true;
    }
}
