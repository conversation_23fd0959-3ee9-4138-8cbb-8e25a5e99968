/**
 * 定义一下方法
 * from http://xh829.com/
 * 来自信呼开发团队
 * */

package com.dialog;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.baselib.AR;
import com.baselib.CLog;
import com.baselib.Rock;
import com.baselib.CallBack;


public class DialogMenu {

    private static View view = null;
    private static AlertDialog dialog = null;

    //自定义alert
    public static void show(Context context, String cont, final CallBack call) {
        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
            view = null;
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        view = Rock.getView(context, AR.getlayoutID("dialog_menu"));
        LinearLayout layout = (LinearLayout) view.findViewById(AR.getID("dialog_menu_main"));

        String menua[] = cont.split(",");

        View tv,hv;
        TextView av;
        int len = menua.length;
        for (int i = 0; i < len; i++){
            final int oi = i;
            String arr[] = menua[i].split("\\|");
            final String str = arr[0];
            tv = Rock.getView(context, AR.getlayoutID("dialog_menulist"));
            av = (TextView) tv.findViewById(AR.getID("title"));
            av.setText(str);
            if(arr.length>1)av.setTextColor(Color.parseColor(arr[1]));
            av.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    if(call!=null)call.backhttp(oi,0, str);
                    hide();
                }
            });
            if(i==len-1){
                hv = (View) tv.findViewById(AR.getID("hang"));
                hv.setVisibility(View.GONE);
            }
            layout.addView(tv);
        }


        builder.setView(view);
        dialog = builder.show();
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
    }

    public static void hide()
    {
        dialog.dismiss();
        dialog  = null;
        view    = null;
    }
}