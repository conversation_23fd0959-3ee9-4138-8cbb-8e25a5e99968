/**
 * 定义一下方法
 * from http://xh829.com/
 * 来自信呼开发团队
 * */

package com.dialog;

import android.app.AlertDialog;

import android.content.Context;

import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.widget.TextView;


import com.baselib.AR;
import com.baselib.CLog;
import com.baselib.Rock;
import com.baselib.CallBack;
import com.view.RockImageView;


public class Dialog {

    private static View view = null;
    private static AlertDialog dialog = null;
    private static int dialoglx = 0;

    //自定义alert
    public static void mainalert(Context context, String cont, String tit, String btnqz, String btnqx, CallBack queclick, CallBack qxclick, CallBack discall)
    {
        hide();
        AlertDialog.Builder builder = new AlertDialog.Builder(context);

        view = Rock.getView(context, AR.getlayoutID("dialog_view0"));
        setTitle(tit);
        setContent(cont);
        setOkbtn(btnqz, queclick);
        setCancelbtn(btnqx, qxclick);

        builder.setView(view);
        dialog = builder.show();
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        if(discall!=null) {
            dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialogInterface) {
                    discall.back();
                }
            });
        }
    }

    /**
     * 是否点击取消
     * */
    public static void setCancelable(Boolean bo)
    {
        dialog.setCancelable(bo);
    }

    public static void alert(Context context, String cont)
    {
        alert(context, cont, "", null, null);
    }

    /**
     * alert
     * */
    public static void alert(Context context, String cont, String tit, String btnstr, CallBack queclick)
    {
        dialoglx = 0;
        mainalert(context, cont, tit, null, null,  null, null, null);
        TextView tv = (TextView)view.findViewById(AR.getID("btn_queding"));
        tv.setVisibility(View.VISIBLE);
        if(btnstr != null)tv.setText(btnstr);
        if(!Rock.isEmpt(Rock.nowtheme))tv.setTextColor(Color.parseColor(Rock.nowtheme));
        tv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (queclick != null) queclick.back();
            }
        });
    }

    /**
     * 隐藏
     * */
    public static void hide()
    {
        if(dialog!=null){
            dialog.dismiss();
            dialog  = null;
            view    = null;
        }
    }

    /**
     * confirm
     * */
    public static void confirm(Context context, String cont, String tit, CallBack queclick,  CallBack qxclick)
    {
        dialoglx = 1;
        mainalert(context, cont, tit, "确定", "取消",  queclick, qxclick, null);
    }

    /**
     * 设置显示图片
     * */
    public static void setImage(String path)
    {
        RockImageView imgobj= (RockImageView)view.findViewById(AR.getID("images"));
        imgobj.setPath(path);
        imgobj.setVisibility(View.VISIBLE);
    }

    /**
     * 获取对应图片sd路径
     * */
    public static String getImagesdPath()
    {
        RockImageView imgobj= (RockImageView)view.findViewById(AR.getID("images"));
        return imgobj.getsdPath();
    }

    /**
     * prompt
     * */
    public static void prompt(Context context, String cont, String tit, String text, CallBack queclick, CallBack discall)
    {
        dialoglx = 2;
        mainalert(context, cont, tit, "确定", "取消",  queclick, null, discall);
        TextView tv = (TextView)view.findViewById(AR.getID("contentedit"));
        tv.setVisibility(View.VISIBLE);
        tv.setText(text);
    }

    public static String getText()
    {
        TextView tv = (TextView)view.findViewById(AR.getID("contentedit"));
        return tv.getText().toString();
    }

    //设置确定按钮文字
    public static void setOkbtn(String str, CallBack queclick)
    {
        TextView tv = (TextView)view.findViewById(AR.getID("btn_comfirm"));
        if (str==null){
            tv.setVisibility(View.GONE);
        }else {
            tv.setText(str);
            if(!Rock.isEmpt(Rock.nowtheme))tv.setTextColor(Color.parseColor(Rock.nowtheme));
            tv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                    if (queclick != null) queclick.back();
                }
            });
        }
    }


    /**
     * 设置确定取消文字
     * */
    public static void setCancelbtn(String str, CallBack qxclick)
    {

        TextView tv = (TextView)view.findViewById(AR.getID("btn_cancel"));
        if (str==null){
            tv.setVisibility(View.GONE);
        }else {
            tv.setVisibility(View.VISIBLE);
            tv.setText(str);
            tv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                    if (qxclick != null) qxclick.back();
                }
            });
        }
    }

    /**
     * 设置中间按钮
     * */
    public static void setCenterbtn(String str, CallBack qxclick)
    {
        if(dialog==null)return;
        TextView tv = (TextView)view.findViewById(AR.getID("btn_center"));
        if (str==null){
            tv.setVisibility(View.GONE);
        }else {
            tv.setVisibility(View.VISIBLE);
            tv.setText(str);
            tv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //dialog.dismiss();
                    if (qxclick != null) qxclick.back();
                }
            });
        }
    }

    //设置标题
    public static void setTitle(String str)
    {
        TextView tv = (TextView)view.findViewById(AR.getID("title"));
        if(Rock.isEmpt(str)){
            tv.setVisibility(View.GONE);
        }else{
            tv.setVisibility(View.VISIBLE);
            tv.setText(str);
            if(!Rock.isEmpt(Rock.nowtheme))tv.setTextColor(Color.parseColor(Rock.nowtheme));
        }
    }

    //设置内容
    public static void setContent(String str)
    {
        TextView tv = (TextView)view.findViewById(AR.getID("content"));
        tv.setText(str);
    }
}