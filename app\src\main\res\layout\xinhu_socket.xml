<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">


    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />
    <RelativeLayout
        android:id="@+id/mya_versionbtn"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        >

        <com.view.RockTextView

            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="长连接自启动"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <TextView
            android:id="@+id/neimsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:text="已开启"
            android:textColor="@color/hui2" />
    </RelativeLayout>
    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />

    <RelativeLayout
        android:id="@+id/qxbtn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/listheight"
        android:background="@drawable/btn_list">

        <com.view.RockTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="应用权限"
            android:textColor="@color/black"
            android:textSize="@dimen/listdp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:src="@mipmap/right"
            />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />

    <com.view.RockTextView
        android:id="@+id/button"
        android:layout_width="180dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:background="@drawable/btn_zhu"
        android:gravity="center"
        android:text="开启"
        android:textColor="@color/white"
        android:textSize="@dimen/listdp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:text="开启长连接自启动，需要在APP的权限管理下去开启“自启动”权限，开启后可确保有消息时能及时收到提醒。"
        android:textSize="14dp"
        android:textColor="@color/hui"
        />




</LinearLayout>