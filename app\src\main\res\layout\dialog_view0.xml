<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:orientation="vertical">

  <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:background="@drawable/dialog_bg0"
      android:layout_marginLeft="20dp"
      android:layout_marginRight="20dp"
      android:orientation="vertical">

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/mcolor"
        android:gravity="left"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:text="系统提示"
        android:textSize="20dp" />

    <com.view.RockTextView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="20dp"
        android:textColor="#666666"
        android:text="内容"
        android:textSize="@dimen/listdp" />

      <com.view.RockImageView
          android:id="@+id/images"
          android:visibility="gone"
          android:layout_width="150dp"
          android:layout_height="150dp"
          android:layout_marginLeft="10dp"
          android:layout_marginRight="10dp"
          android:layout_marginTop="10dp"
          android:layout_marginBottom="10dp"
          android:layout_gravity="center"
          android:src="@mipmap/noimg"
           />

      <com.view.RockEditText
          android:id="@+id/contentedit"
          android:layout_width="match_parent"
          android:visibility="gone"
          android:layout_height="wrap_content"
          android:padding="10dp"
          android:layout_marginLeft="20dp"
          android:layout_marginRight="20dp"
          android:layout_marginBottom="10dp"
          android:background="@drawable/input_login"
          android:textColor="@color/black"
          android:text=""
          android:textSize="@dimen/listdp" />

      <View
          android:id="@+id/hang"
          android:layout_marginTop="15dp"
          android:layout_width="fill_parent"
          android:layout_height="1px"
          android:background="@color/line2" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_btn1"
        >

        <com.view.RockTextView
            android:id="@+id/btn_cancel"
            android:layout_width="fill_parent"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:background="@drawable/dialog_btn0"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/hui2"
            android:textSize="@dimen/listdp" />
        <com.view.RockTextView
            android:id="@+id/btn_center"
            android:layout_width="fill_parent"
            android:layout_height="60dp"
            android:layout_marginLeft="1px"
            android:layout_weight="1"
            android:visibility="gone"
            android:gravity="center"
            android:textSize="@dimen/listdp"
            android:textColor="@color/black"
            android:background="@drawable/dialog_btn4"
            android:text="确定" />
        <com.view.RockTextView
            android:id="@+id/btn_queding"
            android:layout_width="fill_parent"
            android:layout_height="60dp"
            android:layout_marginLeft="1px"
            android:layout_weight="1"
            android:visibility="gone"
            android:gravity="center"
            android:textSize="@dimen/listdp"
            android:textColor="@color/mcolor"
            android:layout_alignParentRight="true"
            android:background="@drawable/dialog_btn3"
            android:text="确定" />
        <com.view.RockTextView
            android:id="@+id/btn_comfirm"
            android:layout_width="fill_parent"
            android:layout_height="60dp"
            android:layout_marginLeft="1px"
            android:layout_weight="1"
            android:gravity="center"
            android:textSize="@dimen/listdp"
            android:textColor="@color/mcolor"
            android:layout_alignParentRight="true"
            android:background="@drawable/dialog_btn2"
            android:text="确定" />
    </LinearLayout>

  </LinearLayout>
</LinearLayout>