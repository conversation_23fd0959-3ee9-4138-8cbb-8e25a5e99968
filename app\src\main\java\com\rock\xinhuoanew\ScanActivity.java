package com.rock.xinhuoanew;



import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Vibrator;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;


import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import com.baselib.AA;

import com.baselib.CLog;
import com.baselib.Rock;


import cn.bingoogolapple.qrcode.core.QRCodeView;
import cn.bingoogolapple.qrcode.zbar.ZBarView;


public class ScanActivity extends ARockActivity {

    private ZBarView qrCodeView;
    protected  String  callbackstr;
    private final ScanActivity mActivity = this;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_scan);
        findViewById(R.id.back).setOnClickListener(OnViewClickListener);
       // findViewById(R.id.sendbtn).setOnClickListener(OnViewClickListener);

        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }

        Bundle bundle = this.getIntent().getExtras();
        callbackstr   = bundle.getString("callbackstr");
        String msg = bundle.getString("msg");
        if(!Rock.isEmpt(msg)){
            TextView tv = (TextView)findViewById(R.id.scan_msg);
            tv.setText(msg);
        }

        qrCodeView = (ZBarView) findViewById(R.id.zbarview);
        qrCodeView.setDelegate(new QRCodeView.Delegate() {
            @Override
            public void onScanQRCodeSuccess(String result) {
                vibrate();

                stopCamera(result);
            }

            @Override
            public void onCameraAmbientBrightnessChanged(boolean isDark) {
                // 这里是通过修改提示文案来展示环境是否过暗的状态，接入方也可以根据 isDark 的值来实现其他交互效果
                String tipText = qrCodeView.getScanBoxView().getTipText();
                String ambientBrightnessTip = "\n环境过暗，请打开闪光灯";
                if (isDark) {
                    if (!tipText.contains(ambientBrightnessTip)) {
                        qrCodeView.getScanBoxView().setTipText(tipText + ambientBrightnessTip);
                    }
                } else {
                    if (tipText.contains(ambientBrightnessTip)) {
                        tipText = tipText.substring(0, tipText.indexOf(ambientBrightnessTip));
                        qrCodeView.getScanBoxView().setTipText(tipText);
                    }
                }
            }
            @Override
            public void onScanQRCodeOpenCameraError() {
                Rock.Toast(mActivity, "扫描错误");
            }
        });

        Boolean bo = Rock.checkPermission(this, Manifest.permission.CAMERA);
        if(bo){
            startCamera();
        }else {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA,}, AA.PERMISSION_CAMERA);
        }
    }

    @Override
    public void onDestroy() {
        qrCodeView.stopSpot();
        qrCodeView.stopCamera();
        super.onDestroy();
    }

    private void startCamera()
    {
        qrCodeView.startCamera();//打开后置摄像头开始预览，但是并未开始识别
        qrCodeView.startSpotAndShowRect();
    }

    private void backFinish()
    {
        finish();
    }

    protected View.OnClickListener OnViewClickListener = new View.OnClickListener() {
        public void onClick(View v) {
            onViewClick(v);
        }
    };

    protected void onViewClick(View v) {
        int id = v.getId();
        if(id == R.id.back){
            backFinish();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            backFinish();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void vibrate() {
        Vibrator vibrator = (Vibrator) getSystemService(VIBRATOR_SERVICE);
        vibrator.vibrate(200);
    }

    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults){
        super.onRequestPermissionsResult(requestCode,permissions,grantResults);
        if(requestCode==AA.PERMISSION_CAMERA){
            if(grantResults[0]== PackageManager.PERMISSION_DENIED){
                Rock.Toast(this, "相机拒绝了权限，无法使用扫码功能");
            }
            if(grantResults[0]== PackageManager.PERMISSION_GRANTED){
                startCamera();
            }
        }
    }

    /**
     * 发送图片
     * */
    private void stopCamera(String str)
    {
        if(Rock.isEmpt(callbackstr))Rock.Toast(this, str);
        Intent result = new Intent();
        result.putExtra("callbackstr", callbackstr);
        result.putExtra("result", str);
        setResult(RESULT_OK, result);
        backFinish();
    }
}
