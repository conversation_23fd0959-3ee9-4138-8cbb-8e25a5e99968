//模式提示
$.rockmodelmsg  = function(lx, txt, sj,fun){
	clearTimeout($.rockmodelmsgtime);
	$('#rockmodelmsg').remove();
	js.msg('none');
	if(!fun)fun=function(){};
	if(lx=='none')return;
	var s = '<div id="rockmodelmsg" onclick="$(this).remove()" align="center" style="position:fixed;left:45%;top:33%;z-index:9999;border-radius:10px; background:rgba(0,0,0,0.6);color:white;font-size:18px;min-width:100px;min-height:100px"><div style="padding:30px;">';
	if(lx=='wait'){
		//if(!txt)txt='处理中...';
		s+='<div style="height:45px"><i style="height:40px;width:40px" class="rock-loading"></i></div>';
		//s+='<div style="font-size:16px">'+txt+'</div>';
		if(txt)s+='<div style="padding-top:5px;font-size:16px">'+txt+'</div>';
		if(!sj)sj= 60;
	}
	if(lx=='ok'){
		if(!txt)txt='处理成功';
		s+='<div style="font-size:40px;"><i class="icon-ok"></i></div>';
		s+='<div style="font-size:16px">'+txt+'</div>';
	}
	if(lx=='yuyin'){
		if(!txt)txt='录音中(0.00)...';
		s+='<div style="font-size:40px;"><i class="icon-volume-up"></i></div>';
		s+='<div style="font-size:16px">'+txt+'</div>';
	}
	if(lx=='msg' || !lx){
		if(!txt)txt='提示';
		s+='<div style="font-size:40px;color:red"><i class="icon-exclamation-sign"></i></div>';
		s+='<div style="color:red;font-size:16px">'+txt+'</div>';
	}
	s+='</div></div>';
	$('body').append(s);
	if(!sj)sj = 3;
	var le = (winWb()-$('#rockmodelmsg').width())*0.5;
	var te = (winHb()-$('#rockmodelmsg').height())*0.5-10;
	$('#rockmodelmsg').css({'left':''+le+'px','top':''+te+'px'});
	$.rockmodelmsgtime = setTimeout(function(){
		$('#rockmodelmsg').remove();
		fun();
	}, sj*1000);
}

js.wx={};
js.wx.alert=function(msg,fun){
	js.alert(msg, fun);
}
js.wx.confirm=function(msg,fun,tit){
	js.confirm(msg,fun)
}
js.wx.prompt=function(tit,msg,fun,nr){
	js.prompt(tit,fun, msg, nr)
}
js.wx.load=js.loading=function(msg,sj){
	$.rockmodelmsg('wait', msg,sj);
};
js.wx.unload=js.unloading=function(){
	$.rockmodelmsg('none');
}
js.wx.msgok=js.msgok=function(msg,fun,sj){
	$.rockmodelmsg('ok', msg,sj, fun);
};
js.wx.msgerror=js.msgerror=function(msg,fun,sj){
	$.rockmodelmsg('msg', msg,sj, fun);
};
js.yuyinload=function(msg,fun,sj){
	$.rockmodelmsg('yuyin', msg,sj, fun);
};
js.showmenu=function(d){
	$('#menulistshow').remove();
	var d=js.apply({width:200,top:'50%',renderer:function(){},align:'center',onclick:function(){},oncancel:function(){}},d);
	var a=d.data;
	if(!a)return;
	var h1=$(window).height(),h2=document.body.scrollHeight,s1;
	if(h2>h1)h1=h2;
	var col='';
	var s='<div onclick="$(this).remove();" align="center" id="menulistshow" style="background:rgba(0,0,0,0.5);height:'+h1+'px;width:100%;position:absolute;left:0px;top:0px;z-index:198" >';
	s+='<div id="menulistshow_s" style="width:'+d.width+'px;margin-top:'+d.top+';position:fixed;-webkit-overflow-scrolling:touch;border-radius:5px" class="menulist">';
	for(var i=0;i<a.length;i++){
		s+='<div oi="'+i+'" style="text-align:'+d.align+';" >';
		s1=d.renderer(a[i]);
		if(s1){s+=s1}else{s+=''+a[i].name+'';}
		s+='</div>';
	}
	s+='</div>';
	s+='</div>';
	$('body').append(s);
	var mh = $(window).height();
	var l=($(window).width()-d.width)*0.5,o1 = $('#menulistshow_s'),t = (mh-o1.height()-10)*0.5;
	if(t<10){
		t = 10;
		o1.css({height:''+(mh-20)+'px','overflow':'auto'});
	}
	o1.css({'left':''+l+'px','margin-top':''+t+'px'});
	$('#menulistshow div[oi]').click(function(){
		var oi=parseFloat($(this).attr('oi'));
		d.onclick(a[oi],oi);
	});
	$('#menulistshow').click(function(){
		$(this).remove();
		try{d.oncancel();}catch(e){}
	});
};

js.isqywx=false;
js.iswxbo=function(){
	var bo = true;
	var ua = navigator.userAgent.toLowerCase(); 
	if(ua.indexOf('micromessenger')<0)bo=false;
	if(bo && ua.indexOf('wxwork')>0)js.isqywx=true;
	return bo;
}


rockconfirm=function(msg, fun, tit){
	if(!tit)tit='';
	if(isapp){
		plus.nativeUI.confirm(msg, function(e){
			var jg = 'no';
			if(e.index==0)jg='yes';
			fun(jg);
		});
	}else if(apixhbool){
		api.confirm({
			title: tit,
			msg: msg,
			buttons: ['确定', '取消']
		}, function(ret, err) {
			var jg = 'no';
			if(ret.buttonIndex==1)jg='yes';
			fun(jg);
		});
	}else{
		js.wx.confirm(msg, fun, tit);
	}
}
rockprompt=function(tit,msg,fun,nr){
	if(!nr)nr='';if(!tit)tit='';
	if(apixhbool){
		api.prompt({
			title:tit,
			msg:msg,
			text:nr,
			buttons: ['确定','取消']
		}, function(ret, err) {
			if(ret.buttonIndex==1)fun(ret.text);
		});
	}else{
		js.wx.prompt(tit,msg,fun,nr);
	}
}
rockalert=function(msg,fun){
	if(!fun)fun=function(){}
	if(apixhbool){
		api.alert({
			title: '',
			msg: msg,
		}, function(ret, err) {
			if(ret.buttonIndex==1)fun();
		});
	}else{
		js.wx.alert(msg, fun);
	}
}

function showAlert(strv,col){
	var obj = $('div[temp="div"]'),hei=50;
	for(var i=0;i<obj.length;i++)hei+=$(obj[i]).height()+11;
	if(!col)col='red';
	if(typeof(strv)!='string')strv = JSON.stringify(strv);
	var str = '<div temp="div" onclick="$(\'div[temp=div]\').remove()" style="background:rgba(0,0,0,0.8);font-size:12px;position:fixed;right:0px;top:'+hei+'px;padding:5px;z-index:99;word-wrap:break-word;word-break:break-all;white-space:normal;color:'+col+'">['+js.now('now')+']'+strv+'</div>';
	$('body').append(str);
}

function showSuccess(str){
	showAlert(str,'white');
}