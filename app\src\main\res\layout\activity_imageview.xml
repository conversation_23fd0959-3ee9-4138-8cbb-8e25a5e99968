<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
android:id="@+id/rl"
android:layout_width="fill_parent"
android:background="@color/black"
android:layout_height="fill_parent" >

    <ImageView
        android:id="@+id/ImageControl"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_centerInParent="true"
        android:scaleType="matrix"
        android:src="@mipmap/white" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:indeterminateTint="#50ffffff"
        android:max="100"
        android:progress="60"
        />

    <LinearLayout
        android:id="@+id/back"
        android:layout_width="@dimen/headheight"
        android:layout_height="@dimen/headheight"
        android:background="@drawable/btn_tm"
        android:layout_alignParentLeft="true"
        android:layout_marginTop="28dp"
        android:gravity="center"
        android:orientation="vertical">
        <com.view.RockTextViewIcon
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_back"
            android:textSize="20dp"
            android:textColor="@color/white" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/down"
        android:layout_width="@dimen/headheight"
        android:layout_height="@dimen/headheight"
        android:background="@drawable/btn_tm"
        android:layout_marginTop="28dp"
        android:layout_alignParentRight="true"
        android:orientation="vertical">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="#00000000"
            android:layout_margin="16dp"

            android:src="@mipmap/down" />
    </LinearLayout>






<TextView
    android:id="@+id/downbtn"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:textColor="@color/white"
    android:gravity="center"
    android:padding="10dp"
    android:text="下载查看原图"
    android:layout_alignParentBottom="true"
    android:layout_centerHorizontal="true"
    android:layout_marginBottom="20dp"
/>

</RelativeLayout>