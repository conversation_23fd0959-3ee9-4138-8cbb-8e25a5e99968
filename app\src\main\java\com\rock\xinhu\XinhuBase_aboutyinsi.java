
package com.rock.xinhu;


import android.content.Context;
import android.text.Html;
import android.view.View;
import android.widget.TextView;

import com.baselib.AA;
import com.baselib.AR;
import com.baselib.RockFile;


public class XinhuBase_aboutyinsi extends XinhuBase {



    public XinhuBase_aboutyinsi(Context context, View view) {
        super(context, view);
    }

    public void initBase()
    {
        TextView tv = mView.findViewById(AR.getID("title"));
        String str = "";
        if(AA.APPLOCAL) {
            str = RockFile.getFromAssets(mContext, "web/list/xyyinsi.html");
            if (AA.YINGTYPE == 1) str = str.replace("信呼开发团队", AR.getString("companyname"));
        }else{
            str= "暂无内容";
        }
        tv.setText(Html.fromHtml(str));
    }


}