<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:gravity="center"
    android:orientation="vertical">


    <LinearLayout
        android:background="@drawable/shape_dialog_bg"
        android:layout_height="160dp"
        android:layout_width="160dp"
        android:orientation="vertical"
        android:gravity="center"
        >

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:id="@+id/iv_recording_icon"
            android:alpha="0.5"
            android:src="@drawable/record_microphone" />

        <TextView
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:id="@+id/timestr"
            android:layout_width="wrap_content"
            android:textColor="#30FFFFFF"
            android:gravity="center"
            android:textSize="20dp"
            android:text="" />
    </LinearLayout>



    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="30dp"
        android:text="按住屏幕底部开始说话"
        android:textColor="@color/black"
        android:textSize="20dp"
        />

    <TextView
        android:id="@+id/bottomlinear"
        android:layout_width="match_parent"
        android:text=""
        android:background="@android:color/transparent"
        android:layout_height="250dp"
        />


</LinearLayout>