<script>
$(document).ready(function(){
	{params}
	var c = {
		isout:false,
		info:{},
		init:function(){
			//js.getarr(params);
			this.fileid = params.fileid;
			this.type   = params.type;
			this.downkey= 'downfile'+this.type+'_'+this.fileid+'';
			if(isNaN(this.fileid))this.isout = true;
			$('#file_fileext').html(params.fileext.toUpperCase());
			this.panduan();
			this.key = md5('fileinfo'+apiurl+''+this.fileid+'');
			var str = js.getoption(this.key);
			if(str)this.loadshow(js.decode(str), true);
			this.loadss();
		},
		loadss:function(){
			var tdz = 'file|getfilenew';
			if(this.isout)tdz = xcy.outgroup.geturl('getfilenew');
			js.ajax(tdz,{id:this.fileid}, function(ret){
				c.loadshow(ret.data, false);
			},'get','加载中...');
		},
		loadshow:function(da, bo){
			this.info = da;
			if(!bo)js.setoption(this.key, JSON.stringify(da));
			//js.getarr(da);
			this.filename = ''+this.fileid+'_'+da.filename;
			$('#file_filename').html(da.filename);
			$('#file_fileext').html(da.fileext.toUpperCase());
			$('#file_fileinfo').html('发送者：'+da.optname+'<br>大小：'+da.filesizecn+'<br>'+da.adddt+'');
			if(this.isyuyin(da.fileext)){
				$('#viewbtn').html('播放语音');
				$('#downbtn').hide();
				if(bo)this.playamr();
			}
		},
		playamr:function(){
			var url = ''+apiurl+''+this.info.filepath+'';
			api.rockFun('AudioPlay',{url:url});
		},
		openfile:function(o1,lx){
			if(this.isyuyin(this.info.fileext)){
				this.playamr();
				return;
			}
			if(this.existbool && lx==1){
				this.openbdfile();
				return;
			}
			var tdz = 'upload|fileinfo';
			if(this.isout)tdz = xcy.outgroup.geturl('fileinfo');
			js.ajax(tdz,{type:lx,lx:lx,id:this.fileid,ismobile:1,abclx:'newapp'}, function(ret){
				c.openfileshow(ret.data);
			},'get','加载中...');
		},
		isyuyin:function(ext){
			var lxing= 'mp3';
			if(api.systemType == 'ios'){
				lxing += ',caf';
			}else{
				lxing += ',amr';
			}
			return (lxing.indexOf(ext)>-1);
		},
		openfileshow:function(da){
			var url = da.url;
			if(url.substr(0,4)!='http')url=''+apiurl+''+url+'&token='+TOKEN+'&adminid='+adminid+'&openfrom='+api.systemType+'app';
			if(da.type==0){
				if(this.isyuyin(da.fileext)){
					api.rockFun('AudioPlay',{
						url:url
					});
					return;
				}
				if(da.isview){
					var bo = this.openpdf();
					if(bo)return;
				}
				xcy.dakaiurl('预览',url);
			}
			if(da.type==1){
				if(apixhbool){	
					this.downurl = url;
					api.rockFun('download',{
						url:url,
						filename:da.filename
					},function(ret,err){
						if(ret){
							c.downback(ret);
						}else{
							js.msg('msg', err.responseText);
						}
					});
				}else{
					js.location(url);
				}
			}
		},
		downback:function(ret){
			if(ret.status==1){
				js.setoption(this.downkey, ret.downid);
				this.panduan();
			}else{
				$('#downbtn').html('下载中...');
			}
		},
		panduan:function(){
			var fv = js.getoption(this.downkey);
			if(fv){
				$('#downbtn').html('<font color="green">已下载，点我打开</font>');
				this.existbool=true;
			}else{
				$('#downbtn').html('点我下载');
				this.existbool=false;
			}
		},
		openbdfile:function(){
			api.createMenu({
				menu:'打开文件,删除|'+deletecolor+''
			},function(ret){
				if(ret.menuIndex==0){
					api.rockFun('downloadView',{path:js.getoption(c.downkey)});
				}
				if(ret.menuIndex==1){
					js.setoption(c.downkey, '');
					c.panduan();
				}
			});
		},
		openpdf:function(){
			var d 	 = this.info;
			var path = d.pdfpath;
			if(this.isout && path){
				this.openstart(path, d);
				return true;
			}
			
			var x5ver = api.x5Ver;
			if(x5ver && x5ver>0){
			}else{
				return false;
			}
			
			if(d.fileext=='pdf'){
				path = d.filepath;
				if(d.filepathout)path = d.filepathout;
			}
			if(path){
				path = xcy.getface(path);
				this.openstart(path, d);
				return true;
			}
			
			return false;
		},
		openstart:function(path,d){
			api.rockFun('openPdf', {
				fileurl:path,
				filename:d.filename,
				filesizecn:d.filesizecn,
				animtype:'show',
				opentyle:'x5'
			});
		}
	}

	c.init();
	js.initbtn(c);
});
</script>

<div style="margin-top:50px" align="center">
	<div id="file_fileext" style="width:60px;height:60px;background:#53B7ED;background:var(--main-color);color:white;
	border-radius:5px;line-height:60px;text-align:center;overflow:hidden;">TXT</div>
	<div class="rock_msg__desc" id="file_filename" style="margin-top:20px;padding:0px 20px">文件名</div>
	<div style="margin-top:15px;font-size:14px;color:#888888"  id="file_fileinfo">发送者：管理员<br>大小：10KB<br>时间</div>
	<div style="margin-top:15px;"><a clickevt="openfile,1" style="padding:8px 0px;color:#fa5151" id="downbtn" type="button">点我下载</a></div>
	
	<div style="margin-top:20px;"><button clickevt="openfile,0" style="width:150px" id="viewbtn" class="webbtn webbtn_big" type="button">在线预览</button></div>
	
	
	
</div>

<div style="position:fixed;width:100%;bottom:50px;">
	<div class="rock_footer">
		提示：下载会占用手机存储空间</p>
	</div>
</div>