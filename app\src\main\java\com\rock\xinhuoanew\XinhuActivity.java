package com.rock.xinhuoanew;


import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ScrollView;

import com.dialog.DialogPopup;
import com.baselib.AR;
import com.baselib.CallBack;
import com.baselib.Json;
import com.baselib.Rock;
import com.rock.xinhu.XinhuBase;
import com.rock.xinhu.XinhuBase_about;
import com.rock.xinhu.XinhuBase_aboutxieyi;
import com.rock.xinhu.XinhuBase_aboutyinsi;
import com.rock.xinhu.XinhuBase_cog;
import com.rock.xinhu.XinhuBase_fontsize;
import com.rock.xinhu.XinhuBase_login;
import com.rock.xinhu.XinhuBase_loginserver;
import com.rock.xinhu.XinhuBase_socket;
import com.rock.xinhu.XinhuBase_theme;
import com.rock.xinhu.XinhuBase_webview;

import java.util.HashMap;
import java.util.Map;

public class XinhuActivity extends BaseActivity {

    private XinhuBase mxinhuBase;
    private View mView;
    private Map<String, String> paramsMap = new HashMap<String, String>();

    protected void initCreate() {
        setContentView(R.layout.activity_xinhu);

        Bundle bundle = this.getIntent().getExtras();
        String name   = bundle.getString("name");
        String type   = bundle.getString("type");
        String params = bundle.getString("params");
        if(!Rock.isEmpt(params)){
            paramsMap = Json.getJsonObject(params);
            String animtype = paramsMap.get("animtype");//打开类型
            if(Rock.contain(animtype, "show"))overridePendingTransition(R.anim.main_zoom,R.anim.main_out);
        }
        setTitles(name);

        findViewById(R.id.back).setOnClickListener(OnViewClickListener);
        findViewById(R.id.more).setOnClickListener(OnViewClickListener);
        if(!Rock.isEmpt(Rock.nowtheme)) {
            View headerv = (View) findViewById(R.id.headertop);
            headerv.setBackgroundColor(Color.parseColor(Rock.nowtheme));
            setStatusColor(Color.parseColor(Rock.nowtheme));
        }else{
            setStatusColor(getResources().getColor(R.color.mcolor));
        }

        ScrollView sView = (ScrollView) findViewById(R.id.mainbody);
        int layid = AR.getlayoutID("xinhu_"+type+"");
        if(layid>0) {
            mView = Rock.getView(this, layid);
            sView.addView(mView);
        }

        mxinhuBase = new XinhuBase(this,mView);

        if(type.equals("webview")) {
            mxinhuBase = new XinhuBase_webview(this, mView);
        }
        if(type.equals("cog")) {
            mxinhuBase = new XinhuBase_cog(this, mView);
        }
        if(type.equals("theme")) {
            mxinhuBase = new XinhuBase_theme(this, mView);
        }
        if(type.equals("fontsize")) {
            mxinhuBase = new XinhuBase_fontsize(this, mView);
        }
        if(type.equals("about")) {
            mxinhuBase = new XinhuBase_about(this, mView);
        }
        if(type.equals("aboutxieyi")) {
            mxinhuBase = new XinhuBase_aboutxieyi(this, mView);
        }
        if(type.equals("aboutyinsi")) {
            mxinhuBase = new XinhuBase_aboutyinsi(this, mView);
        }
        if(type.equals("login")) {
            mxinhuBase = new XinhuBase_login(this, mView);
        }
        if(type.equals("loginserver")) {
            mxinhuBase = new XinhuBase_loginserver(this, mView);
        }
        if(type.equals("readerfile")) {
            //mxinhuBase = new XinhuBase_readerfile(this, mView);
        }
        if(type.equals("socket")) {
            mxinhuBase = new XinhuBase_socket(this, mView);
        }
        mxinhuBase.setxinhuActivity(this);
        mxinhuBase.initBase();
        mxinhuBase.setOnClickListener(OnViewClickListener);
    }

    @Override
    protected void onDestroy(){
        super.onDestroy();
        mxinhuBase.onDestroy();
    }

    protected void onViewClick(View v) {
        int id = v.getId();
        if(id == R.id.back){
            mxinhuBase.onBack();
        }
        if(id == R.id.more){
            showMoreMenu();
        }
        mxinhuBase.onViewClick(id);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            mxinhuBase.onBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 隐藏返回
     * */
    public void hideBack()
    {
        View tv = (View)findViewById(R.id.back);
        tv.setVisibility(View.GONE);
    }

    /**
     * 显示更多操作
     * */
    public void showMore()
    {
        View tv = (View)findViewById(R.id.more);
        tv.setVisibility(View.VISIBLE);
    }

    /**
     * 显示菜单
     * */
    protected void showMoreMenu()
    {
        View v 		= (View)findViewById(R.id.more);
        String cont = mxinhuBase.getMenuString();
        if(Rock.isEmpt(cont)){
            mxinhuBase.onMenuClick(-1, "");
        }else {
            DialogPopup.show(this, v, cont, new CallBack() {
                public void back(int code, String str) {
                    mxinhuBase.onMenuClick(code, str);
                }
            });
        }
    }

    /**
     * 获取参数
     * */
    public String getParams(String key)
    {
        return paramsMap.get(key);
    }

    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(resultCode==RESULT_OK){
            Bundle bundle   = data.getExtras();
            String result = bundle.getString("result");
            mxinhuBase.onActivityResult(requestCode, result);
        }
    }
}
