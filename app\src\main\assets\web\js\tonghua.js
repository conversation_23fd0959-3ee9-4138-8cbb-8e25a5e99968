/**
*	音视频通话使用的文件
*/
xcy.tonghuamessdata = function(){}

//注册广播
xcy.tonghuareg=function(){
	api.addEventListener({
		name: 'tonghua'
	}, function(ret, err) {
		xcy.tonghuamessdata(ret.value);
	});
}

//发送广播
xcy.tonghusendEvent=function(da){
	this.sendEvent('tonghua', 'tonghua', da);
}


//收到消息
xcy.tonghuamess=function(){
	var da = this.received_msg;
	var calltype=da.calltype;
	if(calltype=='call'){
		this.tonghuashow(da);
	}else{
		this.tonghusendEvent(da);
	}
}

//显示试图接听和拒绝的视图
xcy.tonghuashow=function(da){
	this.tonghuadata = da;
	var typea= ['语音','视频'];
	var type = da.th_type,channel = da.th_channel,s12='tonghuachannel';
	if(js.getoption(s12)==channel)return;
	
	js.setoption(s12, channel);
	js.setoption('tonghuaface', this.tonghuadata.adminface);
	xcy.opennei({
		name:this.tonghuadata.adminname,
		id:this.tonghuadata.adminid,
		iscall:false,
		channel:this.tonghuadata.th_channel,
		appid:this.tonghuadata.th_appid,
		sytime:this.tonghuadata.th_time,
		'url':'tonghua',
		type:this.tonghuadata.th_type
	});
}