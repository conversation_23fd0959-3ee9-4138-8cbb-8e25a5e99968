package com.rockwebsocket;

import com.baselib.CLog;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;

public class ClientWebsocket_orgjava extends ClientWebsocket {

    private WebSocketClient webSockets;

    public ClientWebsocket_orgjava(String url) {
        super(url);
        URI uri = URI.create(url);
        webSockets = new WebSocketClient(uri) {
            @Override
            public void onOpen(ServerHandshake handshakedata) {
                websocketCallBack.onOpen();
            }

            @Override
            public void onMessage(String message) {
                try{
                    websocketCallBack.onMessage(message);
                }catch (Exception e){
                    CLog.debug("sendeorror："+e.getMessage());
                }
            }

            @Override
            public void onClose(int code, String reason, boolean remote) {
                websocketCallBack.onClose(code, reason);
            }

            @Override
            public void onError(Exception ex) {
                websocketCallBack.onError(500,ex.getMessage());
            }
        };
        webSockets.setConnectionLostTimeout(60); //0说明不要定时检测
    }

    @Override
    public void connect() {
        webSockets.connect();
    }

    @Override
    public void close() {
        webSockets.close();
        webSockets = null;
    }

    @Override
    public Boolean send(String str) {
        try {
            webSockets.send(str);
            return true;
        }catch (Exception e){
            websocketCallBack.onError(499,"senderror:"+e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean isOpen() {
        if(webSockets == null)return false;
        return webSockets.isOpen();
    }
}
