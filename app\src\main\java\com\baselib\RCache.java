package com.baselib;

import android.content.Context;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Scanner;

public class RCache {

    private static String getFile(Context context, String key)
    {
        return RockFile.getCachedir(context, "cache")+"/"+key+".txt";
    }

    /**
     * 保存文件缓存
     * */
    public static void save(Context context, String key, String cont)
    {
        String path = getFile(context, key);
        File file	  = new File(path);
        if(Rock.isEmpt(cont)){
            if(file.exists())file.delete();
        }else{
            try {
                if(!file.exists())file.createNewFile();
                FileOutputStream fos = new FileOutputStream(file);
                fos.write(cont.getBytes());
                fos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 读取内容
     * */
    public static String get(Context context, String key)
    {
        String path = getFile(context, key);
        String str  = "";
        File file	  = new File(path);
        if(file.exists()){
            try (Scanner scanner = new Scanner(file)){
                if(scanner.hasNextLine())str = scanner.nextLine();
            } catch (FileNotFoundException e) {

            }
        }
        return str;
    }

    /**
     * 删除缓存内容
     * */
    public static void del(Context context, String key)
    {
        String path = getFile(context, key);
        File file	  = new File(path);
        if(file.exists())file.delete();
    }
}
