/**
 * 录音
 * from http://www.rockoa.com/
 * 来自信呼开发团队
 * */

package com.dialog;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.baselib.AA;
import com.baselib.AR;
import com.baselib.CDate;
import com.baselib.CLog;
import com.baselib.Jiami;
import com.baselib.Rock;
import com.baselib.CallBack;
import com.baselib.RockFile;
import com.baselib.RockHttp;


public class DialogAudio {

    private static View view = null;
    private static TextView viewl = null, mTextView, tTextView;
    private static AlertDialog dialog = null;
    private static ImageView mImageView = null;
    private static CallBack callback = null;

    private static AudioRecoderUtils mAudioRecoderUtils;
    private static Handler ssHandler;
    private static String lastPath = "", lastPath3 = "";
    private static int lastTime = 0;

    /**
     * 录音开始了
     * */
    public static void show(Context context, CallBack call) {
        if (dialog != null) hide();
        touchBool = false;
        callback = call;
        AlertDialog.Builder builder = new AlertDialog.Builder(context).setCancelable(true);
        view = Rock.getView(context, AR.getlayoutID("dialog_audio"));
        viewl = (TextView) view.findViewById(AR.getID("bottomlinear"));
        mTextView = (TextView) view.findViewById(AR.getID("timestr"));
        tTextView = (TextView) view.findViewById(AR.getID("title"));
        mImageView = (ImageView) view.findViewById(AR.getID("iv_recording_icon"));
        builder.setView(view);
        dialog = builder.show();

        Window dialogWindow = dialog.getWindow();
        dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.gravity = Gravity.BOTTOM;
        dialogWindow.setAttributes(lp);
        dialogWindow.setDimAmount(0f);

        viewl.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                return yybtnonTouch(motionEvent);
            }
        });

        mAudioRecoderUtils = new AudioRecoderUtils(context);
        mAudioRecoderUtils.setOnAudioStatusUpdateListener(new AudioRecoderUtils.OnAudioStatusUpdateListener() {
            @Override
            public void onUpdate(double db, long time) {
                //根据分贝值来设置录音时话筒图标的上下波动
                int lev = (int) (3000 + 6000 * db / 100);
                mImageView.getDrawable().setLevel(lev);
                String str = CDate.long2String(time);
                mTextView.setText(str);
                //if(callback!=null)callback.back(3, str);
            }

            @Override
            public void onStop(String filePath, long audioTime) {
                stopBack(filePath, (int) audioTime);
            }
        });

        ssHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                onhandleMessage(msg);
            }
        };
    }

    public static void hide() {
        dialog.dismiss();
        dialog = null;
        view = null;
        viewl = null;
        mTextView = null;
        tTextView = null;
        mAudioRecoderUtils = null;
        callback = null;

    }

    private static void onhandleMessage(Message msg)
    {
        String retsult = "";
        Bundle mBundle = msg.getData();
        if (mBundle != null) retsult = mBundle.get("result").toString();
        //CLog.debug(retsult);
        if (msg.what == 1 && msg.arg2 == 1) {
            if (Rock.contain(retsult, "http") && msg.arg1 == AA.HTTPB_SUCCESS) {
                downMp3(retsult);
            } else {
                tTextView.setText("转换失败!");
                stopBackOK();
            }
        }
        if (msg.what == 2) {
            if (msg.arg1 == AA.HTTPB_SUCCESS) {
                downMp3OK();
            } else {
                tTextView.setText("保存失败!");
                stopBackOK();
            }
        }
    }

    /**
     * 回调处理
     * */
    private static void stopBack(String filePath, int audioTime) {
        lastPath = filePath;
        lastTime = audioTime;
        if (callback == null) { hide();return;}
        //不转就直接运行这个
        //stopBackOK();

        tTextView.setText("转mp3中..");
        String upurl = Jiami.base64decode("aHR0cDovL2FwaS5yb2Nrb2EuY29tLz9tPXRvbXAz");
        if (AA.DEBUG) {
            //upurl = Jiami.base64decode("aHR0cDovLzE5Mi4xNjguMC4xMDgvYXBwL3JvY2thcGkvP209dG9tcDM:");
        }
        RockHttp.upload(upurl, filePath, ssHandler, 1, null);
    }

    /**
     * 完成了
     * */
    private static void stopBackOK() {
        if (callback != null) callback.back(lastTime, lastPath);
        hide();
    }

    private static void downMp3(String url) {
        tTextView.setText("转成功保存中..");
        lastPath3 = lastPath.replace(".amr", ".mp3");
        RockHttp.down(url, lastPath3, ssHandler, 2);
    }

    private static void downMp3OK()
    {
        tTextView.setText("转成功");
        lastPath = lastPath3;
        stopBackOK();
    }

    private static float startY = 50;
    private static Boolean touchBool = false;
    private static Boolean yybtnonTouch(MotionEvent event)
    {
        if(touchBool)return false;

        int evlx = event.getAction();
        float yy;
        if(evlx==MotionEvent.ACTION_DOWN){
            startY      = event.getY();
            tTextView.setText("录音开始上滑取消");
            mAudioRecoderUtils.startRecord();
            if(callback!=null)callback.back(0,"");
            return true;
        }
        if(evlx==MotionEvent.ACTION_UP){
            yy = startY - event.getY();
            if(yy>100){
                mAudioRecoderUtils.cancelRecord();
                tTextView.setText("录音已取消");
                if(callback!=null)callback.back(2,"录音已取消");
                hide();
            }else {
                tTextView.setText("录音已完成");
                long time = mAudioRecoderUtils.stopRecord();
                if(time<1000){
                    tTextView.setText("录音时间太短");
                    if(callback!=null)callback.back(2,"录音时间太短");
                    hide();
                }
            }
            touchBool = true;
        }
        if(evlx==MotionEvent.ACTION_MOVE){
            yy = startY - event.getY();
            if(yy>100){
                mAudioRecoderUtils.cancelRecord();
                tTextView.setText("录音已取消了");
            }
        }
        return false;
    }
}