package com.rock.xinhuoanew;

import android.app.job.JobParameters;
import android.app.job.JobService;
import android.content.Context;

import com.baselib.CLog;

public class XinhuJobService extends JobService {


    @Override
    public boolean onStartJob(JobParameters jobParameters) {
        Boolean bo = JPushReceiver.regRJPush(this);
        if(bo)NotifyBase.startJobServer(this, 2*60);//60秒检测job服务一次
        return false;
    }

    @Override
    public boolean onStopJob(JobParameters jobParameters) {
        return false;
    }
}
