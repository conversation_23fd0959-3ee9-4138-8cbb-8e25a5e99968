/**
 * 说明：http请求
 * 创建：雨中磐石  from www.rili123.cn
 * 时间：2014-11-28
 * 邮箱：<EMAIL>
 * QQ：290802026/1073744729
 * */

package com.baselib;


import static android.content.Context.DOWNLOAD_SERVICE;

import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;


import org.apache.http.conn.ConnectTimeoutException;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.UUID;

public final class RockHttp {


	/**
	 * 取消http
	 */
	private static HttpURLConnection httpConn = null;

	public static void httpcancel() {
		if (httpConn != null) httpConn.disconnect();
		httpConn = null;
	}

	public static void sendurl(String url, int gcode, Handler shandler, String type, String params, CallBack call, int timeout) {

		String ResultString = "";
		HttpURLConnection httpURLConnection = null;
		int httpcode = AA.HTTPB_ERROR;
		try {
			if (type.equals("GET")) {
				if (!Rock.isEmpt(params)) {
					String jg = Rock.contain(url, "?") ? "&" : "?";
					url += jg + params;
				}
			}
			URL requestUrl = new URL(url);
			//CLog.error(url,"-API");
			httpURLConnection = (HttpURLConnection) requestUrl.openConnection();//打开连接
			httpURLConnection.setRequestMethod(type);//两种方法GET/POST
			httpURLConnection.setUseCaches(false); //不缓存
			if (type.equals("POST")) {
				byte[] data = params.getBytes();
				httpURLConnection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
				httpURLConnection.setRequestProperty("Content-Length", data.length + "");
				OutputStream out = httpURLConnection.getOutputStream();
				out.write(data);
				out.flush();
				out.close();
			}
			httpURLConnection.setConnectTimeout(5000);
			httpURLConnection.setReadTimeout(timeout);
			httpURLConnection.connect();
			httpConn = httpURLConnection;

			int statusCode = httpURLConnection.getResponseCode();
			InputStream inputstream = httpURLConnection.getInputStream();
			String line = "";
			if (statusCode == 200) {
				httpcode = AA.HTTPB_SUCCESS;
			} else {
				if (statusCode == 301) ResultString = "地址被强制跳转";
			}
			BufferedReader reader = new BufferedReader(new InputStreamReader(inputstream));
			int ci = 0;
			while ((line = reader.readLine()) != null) {
				if (ci > 0) ResultString += "\n";
				ResultString += line;
				ci++;
			}
			reader.close();
		} catch (MalformedURLException e) {

		} catch (ProtocolException e) {

		} catch (IOException e) {
			ResultString = "无法访问网络";
			httpcode = AA.HTTPB_NOTWEN;
		} finally {
			if (httpURLConnection != null) httpURLConnection.disconnect();// 关闭连接
			httpConn = null;
		}
		if (shandler != null) {
			Rock.sendHandler(shandler, gcode, ResultString, httpcode);
		}
		if (call != null) call.backhttp(httpcode, gcode, ResultString);
	}

	/**
	 * get请求
	 */
	public static void get(String url, Handler ssHandler, int getcode, String params, CallBack call, int timeout) {
		new Thread(new Runnable() {
			@Override
			public void run() {
				sendurl(url, getcode, ssHandler, "GET", params, call, timeout);
			}
		}).start();
	}
	public static void get(String url, Handler ssHandler, int getcode, String params, CallBack call){
		get(url,ssHandler,getcode, params, call, AA.HTTPG_CONNTIMEOUT);
	}
	public static void get(String url, Handler ssHandler, int getcode, int timeout) { get(url,ssHandler,getcode, "", null, timeout); }
	public static void get(String url, Handler ssHandler, int getcode) { get(url,ssHandler,getcode, "", null, AA.HTTPG_CONNTIMEOUT); }

	/**
	 * post请求
	 * */
	public static void post(String url, Handler ssHandler, int getcode, String params, CallBack call)
	{
		new Thread(new Runnable() {
			@Override
			public void run() {
				sendurl(url, getcode, ssHandler,"POST", params,call, AA.HTTPG_CONNTIMEOUT);
			}
		}).start();
	}


	/**
	 * 上传文件到服务器根据路径
	 * */
	public static void upload(String url, String fpath, Handler ssHandler, int getcode, CallBack call)
	{
		new Thread(new Runnable() {
			@Override
			public void run() {
				uploadFile(fpath,url, getcode, ssHandler, call);
			}
		}).start();
	}

	/**
	 * 选择的文件上传
	 * */
	public static void uploadChange(String url, InputStream is, String filename, Handler ssHandler, int getcode)
	{
		new Thread(new Runnable() {
			@Override
			public void run() {
				uploadFileStream(is, filename, url, getcode, ssHandler, null);
			}
		}).start();
	}

	/**
	 * 取消上传
	 * */
	private static HttpURLConnection uploadConn = null;
	public static void uploadcancel()
	{
		if(uploadConn!=null)uploadConn.disconnect();
		uploadConn = null;
	}


	private static void uploadFile(String fpath, String RequestURL, int gcode, Handler shandler,CallBack call){
		String ResultString	= "";
		File file 		= new File(fpath);
		if(!file.exists()){
			ResultString = "["+fpath+"]文件不存在";
			sendHandler(shandler, gcode, 0, 1, ResultString);
			return;
		}
		String filename = file.getName();
		try {
			InputStream is 	= new FileInputStream(file);
			uploadFileStream(is, filename, RequestURL, gcode, shandler, call);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			sendHandler(shandler, gcode, 0, 1, "FileNotFoundException");
		}
	}

	/**
	 * 回传
	 * @param what 来源标识
	 * @param arg1 是否成功如果http=200
	 * @param arg2 0进度条,1完成了
	 * */
	private static void sendHandler(Handler shandler, int what, int arg1,int arg2,String result){
		Message message = new Message();
		message.what = what;
		Bundle mBundle = new Bundle();
		mBundle.putString("result", result);
		message.setData(mBundle);
		message.arg1 = arg1;
		message.arg2 = arg2;
		shandler.sendMessage(message);
	}

	private static void uploadFileStream(InputStream is, String filename, String RequestURL, int gcode, Handler shandler,CallBack call){
		long totalSize = 0,writesize=0;
		String BOUNDARY	=  UUID.randomUUID().toString();  //边界标识   随机生成
		String PREFIX 	= "--" , LINE_END = "\r\n";
		String CONTENT_TYPE = "multipart/form-data";   //内容类型
		String CHARSET 	= "utf-8";
		int backcode 	= 0;
		HttpURLConnection conn = null;
		String ResultString = "";
		try {
			URL url = new URL(RequestURL);
			conn = (HttpURLConnection) url.openConnection();
			conn.setReadTimeout(AA.HTTPG_SOTIMEOUT);
			conn.setConnectTimeout(AA.HTTPG_UPLOADTIMEOUT);
			conn.setDoInput(true);  //允许输入流
			conn.setDoOutput(true); //允许输出流
			conn.setUseCaches(false);  //不允许使用缓存
			conn.setRequestMethod("POST");  //请求方式
			conn.setRequestProperty("Charset", CHARSET);  //设置编码
			conn.setRequestProperty("connection", "keep-alive");
			conn.setRequestProperty("Content-Type", CONTENT_TYPE + ";boundary=" + BOUNDARY);
			conn.connect();
			uploadConn = conn;
			int bufferSize		= 4*1024; //每次写入

			//当文件不为空，把文件包装并且上传
			DataOutputStream dos = new DataOutputStream( conn.getOutputStream());
			StringBuffer sb = new StringBuffer();
			sb.append(PREFIX);
			sb.append(BOUNDARY);
			sb.append(LINE_END);

			//设置上传name，和文件名
			sb.append("Content-Disposition: form-data; name=\"file\"; filename=\""+filename+"\""+LINE_END);
			sb.append("Content-Type: application/octet-stream; charset="+CHARSET+LINE_END);
			sb.append(LINE_END);
			dos.write(sb.toString().getBytes());
			totalSize	= is.available();//总大小

			byte[] bytes 	= new byte[bufferSize];
			int len = 0,bili;
			String bilis;

			//写入进度条
			while((len=is.read(bytes))!=-1){
				if(uploadConn==null)break;
				dos.write(bytes, 0, len);
				writesize+=bufferSize;
				if(writesize>totalSize)writesize=totalSize;

				bili 	= Integer.parseInt((writesize*100/totalSize)+"");
				//bilis = new DecimalFormat("#.00").format((writesize*100/totalSize));
				//发送进度条状态
				String str = "{total:"+totalSize+",valid:0,write:"+writesize+",progress:"+bili+"}";
				if(shandler != null)sendHandler(shandler, gcode, AA.HTTPB_SUCCESS, 0, str);
				if(call!=null)call.backhttp(0, AA.HTTPB_SUCCESS, str);
				Thread.sleep(10);
			}
			is.close();
			dos.write(LINE_END.getBytes());
			byte[] end_data = (PREFIX+BOUNDARY+PREFIX+LINE_END).getBytes();
			dos.write(end_data);
			dos.flush();

			int statusCode = conn.getResponseCode();
			InputStream inputstream = conn.getInputStream();
			if(statusCode==200){
				backcode 	= AA.HTTPB_SUCCESS;
			}else{
				backcode 	= AA.HTTPB_ERROR;
			}

			BufferedReader reader = new BufferedReader(new InputStreamReader(inputstream));
			String line = "";
			int ci = 0;
			while((line=reader.readLine())!=null){
				if(ci>0)ResultString+="\n";
				ResultString+=line;
				ci++;
			}
			reader.close();
		}catch (ConnectTimeoutException e) {
			ResultString = "请求超时";
			backcode = AA.HTTPB_TIMEOUT;
		}catch(Exception e){
			ResultString = "错误:"+e.getMessage()+"";
			e.printStackTrace();
			backcode = AA.HTTPB_NOTWEN;
		} finally {
			if (conn != null) conn.disconnect();// 关闭连接
			uploadConn = null;
		}
		if(shandler != null)sendHandler(shandler, gcode, backcode, 1, ResultString);
		if(call!=null)call.backhttp(1, backcode, ResultString);
	}

	/**
	 * 下载文件
	 * */
	public static void download(Context content, String url, String filename, CallBack call)
	{
		DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));
		request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, filename);//指定下载路径和下载文件
		request.setVisibleInDownloadsUi(true);// 设置为可见和可管理
		DownloadManager downloadManager= (DownloadManager) content.getSystemService(DOWNLOAD_SERVICE);//获取下载管理器
		final long refernece = downloadManager.enqueue(request);
		Rock.Toast(content,""+filename+"下载中...");
		if(call!=null)call.backhttp(0,Integer.parseInt(refernece+""),"start");

		// 注册广播接收器，当下载完成时自动安装
		IntentFilter filter = new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE);
		BroadcastReceiver receiver = new BroadcastReceiver() {
			public void onReceive(Context context, Intent intent) {
				long myDwonloadID = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);
				if (refernece == myDwonloadID) {
					context.unregisterReceiver(this);
					Rock.Toast(context,""+filename+"下载完成");
					if(call!=null)call.backhttp(1,Integer.parseInt(myDwonloadID+""),""+myDwonloadID);
				}
			}
		};
		content.registerReceiver(receiver, filter);
	}


	/**
	 * 下载
	 * */
	public static void down(String url, String filename, Handler shandler, int gcode)
	{
		new Thread(new Runnable() {
			@Override
			public void run() {
				HttpURLConnection httpURLConnection = null;
				int httpcode = AA.HTTPB_ERROR;
				try{
					URL requestUrl = new URL(url);
					httpURLConnection = (HttpURLConnection)requestUrl.openConnection();//打开连接
					httpURLConnection.setRequestMethod("GET");//两种方法GET
					httpURLConnection.setUseCaches(false); //不缓存
					httpURLConnection.setConnectTimeout(5000);
					httpURLConnection.connect();
					int statusCode = httpURLConnection.getResponseCode();
					if(statusCode==200){
						httpcode = AA.HTTPB_SUCCESS;
						InputStream inputstream = null;
						inputstream = httpURLConnection.getInputStream();
						if(!Rock.isEmpt(filename)) {
							FileOutputStream fosto = new FileOutputStream(new File(filename));
							byte[] bt = new byte[1024];
							int c;
							while ((c = inputstream.read(bt)) > 0) {
								fosto.write(bt, 0, c);
							}
							fosto.close();
							inputstream.close();
						}
					}
				}catch(MalformedURLException e){

				} catch (ProtocolException e) {
				} catch (IOException e) {
				} finally {
					if (httpURLConnection != null) httpURLConnection.disconnect();// 关闭连接
				}
				if(shandler != null){
					Rock.sendHandler(shandler, gcode, filename, httpcode);
				}
			}
		}).start();
	}
}