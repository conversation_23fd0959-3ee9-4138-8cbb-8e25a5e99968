<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:focusableInTouchMode="true">

    <LinearLayout
        android:id="@+id/headertopzt"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:background="@color/mcolor"
        android:orientation="vertical"></LinearLayout>

    <RelativeLayout
        android:id="@+id/headertop"
        android:layout_below="@+id/headertopzt"
        android:layout_width="fill_parent"
        android:background="@color/mcolor"
        android:layout_height="@dimen/headheight">

        <LinearLayout
            android:id="@+id/header"
            android:layout_width="fill_parent"
            android:layout_height="@dimen/headheight"
            android:layout_marginLeft="@dimen/headheight"
            android:layout_marginRight="@dimen/headheight"
            android:gravity="center"
            >
            <com.view.RockTextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/headheight"
                android:singleLine="true"
                android:gravity="center"
                android:text="@string/app_name"
                android:textSize="@dimen/headfontsize"
                android:textColor="@color/white" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/more"
            android:layout_width="@dimen/headheight"
            android:layout_height="@dimen/headheight"
            android:layout_alignParentRight="true"
            android:background="@drawable/btn_tm"
            android:gravity="center">

            <com.view.RockTextViewIcon
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_add"
                android:textSize="24dp"
                android:textColor="@color/white" />
        </LinearLayout>

    </RelativeLayout>

   <LinearLayout
       android:id="@+id/forum_contextshow"
       android:layout_width="match_parent"
       android:layout_height="match_parent"
       android:layout_below="@+id/headertop"
       android:layout_alignParentLeft="true"
       android:layout_alignParentBottom="true"
       android:orientation="vertical">
   </LinearLayout>


    <LinearLayout
        android:id="@+id/mainbody"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="horizontal">

        <ProgressBar
            android:id="@+id/progressBar2"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:indeterminateTint="#20000000"
         />
    </LinearLayout>

</RelativeLayout>