/**
*	外部群使用
*/

xcy.outgroup={
	openout:true,
	objarr:[],
	myid:0,
	apiurl:'aHR0cDovL2FwaS5yb2Nrb2EuY29tLw::', //如果不想使用清空这个地址就可以
	//apiurl:'aHR0cDovLzE5Mi4xNjguMS4yL2FwcC9yb2NrYXBpLw::',
	gidarr:[],
	geturl:function(fx){
		var url = jm.base64decode(this.apiurl)+'?m=outgroup&a='+fx+'&device='+device+'';
		return url;
	},
	isopen:function(){
		if(js.getoption('outgroupopen')=='open')return true;
		return false;
	},
	clearchache:function(){
		js.setoption('outgrouplist0', '');
		js.setoption('outgrouplist1', '');
		js.setoption('outunum', '');
	},
	loaddata:function(obj,lx){
		if(!this.isopen() || !this.apiurl){
			this.clearchache();
			return;
		}
		this.objarr[lx] = obj;
		var me = this;
		js.ajax(this.geturl('getlist'),{lx:lx},function(ret){
			me.showdata(ret.data,lx);
		});
	},
	saveoutunum:function(bh){
		var sede = js.getoption('outunum');
		if(sede != bh){
			js.setoption('outunum', bh);
			js.ajax('reim|saveoutunum',{unum:bh},false,'get',function(){js.msg('none');});
		}
	},
	showdata:function(da,lx){
		var hlist = da.hlist;
		js.setoption('outgrouplist'+lx+'', JSON.stringify(hlist));
		js.setoption('outgrouplist1', JSON.stringify(da.alist));
		this.gidarr = da.alist;
		var myinfo  = da.myinfo;
		if(lx==0){
			this.objarr[lx].showhistory(xcy.historyarr);
		}else{
			this.objarr[lx].showgroupall();
		}
		if(!this.socketob && da.wsconfig)this.linkwebsocket(da.wsconfig,0);
		this.saveoutunum(myinfo.unum);
	},
	delhistory:function(id){
		if(!this.apiurl)return;
		js.ajax(this.geturl('delhistory'),{gid:id});
	},
	uppush:function(tok,web){
		if(!this.isopen() || !this.apiurl)return;
		if(!web)web = 'app2023'+api.deviceName+''+api.deviceModel+'';
		web = strreplace(web.toLowerCase());
		js.ajax(this.geturl('pushtoken'),{pushtoken:tok,web:web},function(ret){},'post');
	},
	linkwebsocket:function(conf,lx){
		this.reimconf = conf;
		clearTimeout(this.webtimes);
		if(this.ws)this.ws.close();
		this.ws 	= new WebSocket(conf.url);
		var me  	= this;
		this.myid 	= conf.id;
		this.istxs 	= conf.istxs;
		this.ws.onopen = function(){
			this.send('{"from":"'+conf.recid+'_app","adminid":"'+conf.id+'","atype":"connect","sendname":"outname"}');
			me.socketob = true;
			if(lx==1)me.linkwebsocket(conf,2);
		};
		this.ws.onerror = function(e){
			me.socketob = false;
			me.reloadWebSocket(false);
		};
		this.ws.onmessage = function (evt){
			var ds = JSON.parse(evt.data);
			me.onmessage(ds);
		};
		this.ws.onclose = function(e){
			me.socketob = false;
			me.reloadWebSocket(false);
		};
	},
	reloadWebSocket:function(bo){
		clearTimeout(this.webtimes);
		if(!bo){
			this.webtimes=setTimeout('xcy.outgroup.reloadWebSocket(true)', 5*1000);
		}else{
			if(!this.socketob)this.linkwebsocket(this.reimconf,1);
		}
	},
	onmessage:function(ds){
		//console.log(ds);
		if(ds.sendid==this.myid && ds.laiyuan==CFROM)return;
		var tixd = false,type=ds.type;
		ds.ismysend = (ds.sendid==this.myid);
		if(nowurl=='home'){
			this.objarr[0].reloadss();
		}
		if(!apixhbool)return;
		if(nowurl=='home' && !nowchat){
			
		}else if(nowchat != ''+ds.type+''+ds.gid+''){
			tixd = true;
		}
		if(this.istxs && this.istxs.indexOf(','+ds.gid+',')==-1)tixd=false;//免打扰
		if(ds.atid && ds.atid==this.myid)tixd = true;
		
		if(ds.ismysend)tixd=false;
		if(tixd && type=='gout'){
			api.Notification({
				title: ds.title,
				msg: jm.base64decode(ds.cont)
			});
		}
		xcy.sendEvent('reload','rockchat', ds);
	}
}