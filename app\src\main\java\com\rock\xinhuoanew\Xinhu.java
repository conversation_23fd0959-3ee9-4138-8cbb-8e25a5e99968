/**
 * 定义一下方法常量
 * from http://www.rockoa.com/
 * 来自信呼开发团队
 * 雨中磐石(rainrock)
 * */

package com.rock.xinhuoanew;



import android.app.ActivityManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;

import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;

import androidx.core.app.NotificationCompat;
import com.baselib.AA;
import com.baselib.CLog;
import com.baselib.Jiami;
import com.baselib.RCache;
import com.baselib.RCall;
import com.baselib.Rock;
import com.baselib.RockHttp;

import java.util.List;


public class Xinhu {

    public static String SERVICETYPE            = "servicetype";
    public static String SERVICETYPE_PARAMS     = "servicetype_params";
    public static String SERVICETYPE_RUNTIME    = "servicetype_runtime";
    public static String SERVICETYPE_WEBSOCKET  = "servicetype_websocket";
    public static String SERVICETYPE_STOP       = "servicetype_stop";
    public static String SERVICETYPE_RESTART    = "servicetype_restart";
    public static String SERVICETYPE_TONGHUA    = "servicetype_tonghua";
    public static String SERVICETYPE_TONGHUAE   = "servicetype_tonghuae";

    /**
     * 启动时运行发送一个广播
     * */
    public static void start(Context context)
    {
        String pname = getPname(context);
        CLog.error("进程["+pname+"]APP启动了。。。");

        Intent intent = new Intent(AA.ACTION_START);
        ComponentName componentName = new ComponentName(context, XinhuReceiver.class);
        intent.setComponent(componentName);
        context.sendBroadcast(intent);
    }


    /**
     * 打开页面
     * */
    public static void startActivity(Context context, Class<?> target, String name, String url, String paramsstr)
    {
        Intent intent = new Intent();
        intent.putExtra("url",url);
        intent.putExtra("name", name);
        intent.putExtra("paramsstr", paramsstr);
        intent.setClass(context, target);
        context.startActivity(intent);
    }

    /**
     * 用服务的启动
     * */
    public static void startService(Context context, String type, String params) {
        Intent it	 	= new Intent(context, XinhuService.class);
        it.putExtra(SERVICETYPE, type);
        it.putExtra(SERVICETYPE_PARAMS, params);
        context.startService(it);
    }

    /**
     * 停止服务
     * */
    public static void stopService(Context context) {
        Intent it	 	= new Intent(context, XinhuService.class);
        context.stopService(it);
    }

    /**
     * 发送关播
     * */
    public static void sendBroadcast(Context context, String act, String params)
    {
        Intent ent = new Intent();
        ent.setAction(act);
        ent.putExtra("content", params);
        context.sendBroadcast(ent);
    }

    /**
     * 发送API关播
     * */
    public static void sendBroadcastApi(Context context, String act, String stype)
    {
        sendBroadcastApi(context,act, stype, "");
    }
    public static void sendBroadcastApi(Context context, String act, String stype, String oparams)
    {
        if(Rock.isEmpt(act))return;
        sendBroadcast(context,act,"{name:'"+act+"',stype:'"+stype+"'"+oparams+"}");
    }

    /**
     * 发通知
     * */
    public static void Notification(Context context, String title, String cont, String Channel, int num, int id)
    {
        if(Rock.isEmpt(Channel))Channel  = "消息提醒";
        NotificationManager notifmanager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        String Channelid = ""+AA.APPPAGE+"_"+ Jiami.md5(Channel).substring(0,10) +"";

        if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.O){
            NotificationChannel notificationChannel=new NotificationChannel(Channelid,Channel,NotificationManager.IMPORTANCE_HIGH);
            if(id == 60) {
                notificationChannel.setDescription("收到音视频通话邀请的通知类别");
            }
            notificationChannel.setShowBadge(true);
            notifmanager.createNotificationChannel(notificationChannel);
        }

        if(id == 0)id       = (int) ((Math.random() * 9 + 1) * 100);
        int icon            = R.mipmap.logo;
        //打开主页
        Intent intent_to 	= new Intent(context, MainsActivity.class);
        //intent_to.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
       // PendingIntent pi 	= PendingIntent.getActivity(context, id, intent_to, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pi 	= PendingIntent.getActivity(context, id, intent_to, PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder= new NotificationCompat.Builder(context);
        builder.setContentTitle(title)
                .setContentText(cont) //设置内容
                .setWhen(System.currentTimeMillis())  //设置时间
                .setSmallIcon(icon)
                .setChannelId(Channelid)
                .setContentIntent(pi)
                .setAutoCancel(true)
                .setPriority(NotificationCompat.PRIORITY_MAX);
        if(num>0)builder.setNumber(num);
        //Uri soundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE);
        //builder.setSound(soundUri);
        notifmanager.notify(id, builder.build()); //发出通知
    }

    /**
     * 设置角标
     * */
    public static void setBadge(Context context, int num)
    {
        if(num == Rock.badge)return;
       // Rock.getSqlite(context).setOption("badge", ""+num+"");
        Rock.badge = num;
        CLog.error("设置角标："+num);
    }

    /**
     * 获取角标
     * */
    public static int getBadge(Context context)
    {
        int num = Rock.badge;
        if(num == -1){
            String ostr = Rock.getSqlite(context).getOption("badge");
            num = 0;
            if(!Rock.isEmpt(ostr))num = Integer.parseInt(ostr);
            Rock.badge = num;
        }
        CLog.error("角标："+num);
        return num;
    }

    /**
     * 取消通知
     * */
    public static void NotificationcancelAll(Context context, int id)
    {
        NotificationManager notifmanager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        if(id>0){
            notifmanager.cancel(id);
        }else {
            notifmanager.cancelAll();
        }
    }
    public static void NotificationcancelAll(Context context)
    {
        NotificationcancelAll(context, 0);
    }

    /**
     * 拨打电话
     * */
    public static void callPhone(Context context, String phone)
    {
        Intent intent=new Intent(Intent.ACTION_DIAL, Uri.parse("tel:"+phone));
        context.startActivity(intent);
    }

    /**
     * 获取站位字符
     * */
    public static String getBuildString(Context context, String key)
    {
        String val = "";
        try {
            ApplicationInfo applicationInfo = context.getPackageManager().getApplicationInfo(BuildConfig.APPLICATION_ID, PackageManager.GET_META_DATA);
            val = applicationInfo.metaData.getString(key);
        } catch (PackageManager.NameNotFoundException e) {
        }
        return val;
    }

    /**
     * 获取进程名称
     * */
    public static String getPname(Context context)
    {
        int pid = android.os.Process.myPid();
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        String pname = "";
        if (manager != null) {
            List<ActivityManager.RunningAppProcessInfo> processInfoList = manager.getRunningAppProcesses(); // 获取所有进程信息
            if (processInfoList != null) {
                for (ActivityManager.RunningAppProcessInfo processInfo : processInfoList) {
                    if(processInfo.pid == pid){
                        pname = processInfo.processName;
                        break;
                    }
                }
            }
        }
        return pname;
    }

    /**
     * 统计app使用
     * */
    public static void AppTotal(Context context)
    {
        String url = Jiami.base64decode("aHR0cDovL3d3dy5yb2Nrb2EuY29tLz9tPWFwcGluZm8:");
        if(AA.DEBUG)url = "http://192.168.0.108/app/rockxinhuweb/?m=appinfo";
        url += "&plat=0&version="+Rock.VERSION+"";
        url += "&brand="+Rock.getbrand()+"";
        url += "&model="+Rock.getmodel()+"";
        url += "&deviceid="+Rock.getdeviceId(context)+"";
        url += "&regid="+ JPushReceiver.getRegids(context) +"";
        RockHttp.get(url, null, 1, 2000);
    }
}