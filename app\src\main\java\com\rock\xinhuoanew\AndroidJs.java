package com.rock.xinhuoanew;


import android.app.DownloadManager;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Message;
import android.webkit.JavascriptInterface;

import com.dialog.Dialog;
import com.dialog.DialogAudio;
import com.dialog.DialogAudioPlay;
import com.dialog.DialogLoad;
import com.dialog.DialogMenu;
import com.dialog.DialogMsg;
import com.baselib.*;
import com.rock.rockpush.RockPush;
import com.rock.xinhu.XinhuBase_cog;


import java.util.Map;

/**
 *  定义webview和js交互
 * */
public class AndroidJs{

    private Context mContext;
    protected BaseActivity mActivity;

    public AndroidJs(Context context, BaseActivity mAct) {
        mContext = context;
        mActivity = mAct;
    }


    @JavascriptInterface
    public String rockFun(String action, String params, final String call) {
        String str = "";
        //CLog.debug(params, action);
        Map<String,String> ret = Json.getJsonObject(params);
        if(Rock.equals(action,"alert")){
            Dialog.alert(mContext, ret.get("msg"), ret.get("title"), ret.get("btn"), new CallBack(){
                @Override
                public void back() {
                    mActivity.handleCallSend(call, "{buttonIndex:0}");
                }
            });
        }
        if(Rock.equals(action,"setBadge")){
            Xinhu.setBadge(mContext, Integer.parseInt(ret.get("num")));
        }
        if(Rock.equals(action,"openWin")){
            Xinhu.startActivity(mContext, WebActivity.class, "loading...", ret.get("url"), params);
        }
        if(Rock.equals(action,"openWinnei")){
            Xinhu.startActivity(mContext, WebneiActivity.class, "loading...", ret.get("url"), params);
        }
        if(Rock.equals(action,"closeWin")){
            mActivity.exitBack(ret.get("result"), ret.get("animtype"));
        }

        if(Rock.equals(action,"showProgress")){
            DialogLoad.show(mContext, ret.get("msg"));
        }

        if(Rock.equals(action,"hideProgress")){
            DialogLoad.hide();
        }
        if(Rock.equals(action,"confirm")){
            Dialog.confirm(mContext, ret.get("msg"), ret.get("title"), new CallBack(){
                @Override
                public void back() {
                    mActivity.handleCallSend(call, "{buttonIndex:1}");
                }
            },new CallBack(){
                @Override
                public void back() {
                    mActivity.handleCallSend(call, "{buttonIndex:0}");
                }
            });
            String imgpath = ret.get("imgpath");
            if(!Rock.isEmpt(imgpath))Dialog.setImage(imgpath);
        }
        if(Rock.equals(action,"prompt")){
            Dialog.prompt(mContext, ret.get("msg"), ret.get("title"), ret.get("text"), new CallBack(){
                @Override
                public void back() {
                    String text = Dialog.getText();
                    mActivity.handleCallSend(call, "{buttonIndex:1, text:\""+text+"\"}");
                }
            }, new CallBack(){
                @Override
                public void back() {
                    Rock.runTimer(AA.ASCIPT_HIDEKEY, 100, mActivity.getHandlers());
                }
            });
        }
        if(Rock.equals(action,"setMenu")){
            mActivity.setMenustring(ret.get("menu"), call);
        }
        if(Rock.equals(action,"addEventListener")){
            mActivity.addEventListener(ret.get("name"), call);
        }
        if(Rock.equals(action,"sendEvent")){
            mActivity.sendEvent(ret.get("name"), params);
        }
        if(Rock.equals(action,"toast")){
            Rock.Toast(mActivity, ret.get("msg"));
        }
        if(Rock.equals(action,"ajax")){
            final String datatype = ret.get("dataType");
            CallBack callback = new CallBack(){
                public void backhttp(int httpcode, int gcode, String bstr){
                    if(datatype.equals("html"))bstr = "'"+ Jiami.base64encode(bstr)+"'";
                    if(httpcode==AA.HTTPB_SUCCESS){
                        mActivity.handleCallSend(call,bstr);
                    }else{
                        mActivity.handleCallSend(call, "false,{responseText:'"+bstr+"'}");
                    }
                }
            };
            if(Rock.equals(ret.get("method"),"get")){
                RockHttp.get(ret.get("url"), null, AA.ASCIPT_AJAX, ret.get("data"), callback);
            }else{
                RockHttp.post(ret.get("url"), null, AA.ASCIPT_AJAX, ret.get("data"), callback);
            }
        }
        if(Rock.equals(action,"createMenu")){
            DialogMenu.show(mActivity, ret.get("menu"), new CallBack(){
                public void backhttp(int code, int gcode, String bstr){
                    mActivity.handleCallSend(call, "{menuIndex:"+code+", name:\""+bstr+"\"}");
                }
            });
        }
        if(Rock.equals(action,"clipBoard")){
            Rock.copy(mActivity, ret.get("msg"));
        }

        if(Rock.equals(action,"clipBoardget")){
            str = Rock.getcopy(mActivity);
        }

        if(Rock.equals(action,"startService")){

        }

        if(Rock.equals(action,"startWebsocket")){
            Xinhu.startService(mActivity, Xinhu.SERVICETYPE_WEBSOCKET, params);
        }
        if(Rock.equals(action,"stopService")){
            Rock.Sqlite.setOption("adminid","");
            Rock.Sqlite.setOption("admintoken", "");
            Rock.Sqlite.setOption(AA.LOGIN_KEYSTR, "");
            Rock.admintoken = "";
            Rock.adminid    = "0";
            RCache.del(mContext, AA.KEY_TOKEN);
            Xinhu.startService(mActivity, Xinhu.SERVICETYPE_STOP, params);
            if(NotifyBase.isServiceRun(mActivity))NotifyBase.stopServer(mActivity);
        }

        if(Rock.equals(action,"Notification")){
            Xinhu.Notification(mActivity, ret.get("title"), ret.get("msg"), "",0,0);
        }

        if(Rock.equals(action,"NotificationCancel")){
            Xinhu.NotificationcancelAll(mActivity, 0);
        }

        if(Rock.equals(action,"callPhone")){
            Xinhu.callPhone(mContext, ret.get("phone"));
        }

        if(Rock.equals(action,"imageView")){
            Xinhu.startActivity(mActivity,ImageViewActivity.class,ret.get("filesizecn"),ret.get("url"),"");
        }

        if(Rock.equals(action,"getPicture")){
            mActivity.startCamera(AA.RESULTCODE_CAMERAPI, call, ret.get("sourceType"));
        }

        if(Rock.equals(action,"openScan")){
            mActivity.openScan(AA.RESULTCODE_SCANAPI, call, ret.get("msg"));
        }

        if(Rock.equals(action,"upload")){
            CallBack callback = new CallBack(){
                public void backhttp(int zt, int code, String bstr){
                    if(code==AA.HTTPB_SUCCESS){
                        if(!Rock.contain(bstr, "valid")){
                            mActivity.handleCallSend(call, "false,{responseText:'"+bstr+"'}");
                        }else {
                            mActivity.handleCallSend(call, bstr);
                        }
                    }else{
                        mActivity.handleCallSend(call, "false,{responseText:'"+bstr+"'}");
                    }
                }
            };
            RockHttp.upload(ret.get("url"),ret.get("filepath"),null,0, callback);
        }

        if(Rock.equals(action,"uploadcancel")){
            RockHttp.uploadcancel();
        }


        if(Rock.equals(action,"download")){
            CallBack callback = new CallBack(){
                public void backhttp(int zt, int downid, String bstr){
                    mActivity.handleCallSend(call, "{status:"+zt+",downid:"+downid+"}");
                }
            };
            RockHttp.download(mContext, ret.get("url"),ret.get("filename"),callback);
        }

        if(Rock.equals(action,"downloadView")){
            Intent intent1    = new Intent(DownloadManager.ACTION_VIEW_DOWNLOADS);
            mContext.startActivity(intent1);
        }

        if(Rock.equals(action,"startRecord")){
            CallBack callback = new CallBack(){
                public void back(int code, String bstr){
                    mActivity.handleCallSend(call, "{status:"+code+",filepath:'"+bstr+"'}");
                }
            };
            mActivity.startRecord(callback);
        }

        if(Rock.equals(action,"AudioPlay")){
            DialogAudioPlay.show(mContext, ret.get("url"));
        }

        if(Rock.equals(action,"setOption")){
            Rock.Sqlite.setOption(ret.get("key"), ret.get("value"));
        }

        if(Rock.equals(action,"openView")){
            mActivity.openView(ret.get("url"));
        }

        if(Rock.equals(action,"showMsg")){
            String type = ret.get("type");
            if(Rock.isEmpt(type))type="success";
            int time = 2;
            CallBack callback = new CallBack(){
                @Override
                public void back() {
                    mActivity.handleCallSend(call, "{status:1}");
                }
            };
            if(!Rock.isEmpt(ret.get("time")))time = Integer.parseInt(ret.get("time"));
            switch (type){
                case "hide":
                    DialogMsg.hide();
                    break;
                case "error":
                    DialogMsg.show(mContext, ret.get("msg"),0, time, callback);
                    break;
                default:
                    DialogMsg.show(mContext, ret.get("msg"),1, time, callback);
            }
        }

        if(Rock.equals(action,"initPush")){
            final String alias = ret.get("alias");
            RockPush.setmyHandler(new Handler(){
                @Override
                public void handleMessage(Message msg) {
                    String pushstate    = msg.getData().get("pushstate").toString();
                    String pushmsg      = msg.getData().get("pushmsg").toString();
                    if(pushstate.equals("regok") && !Rock.isEmpt(alias)) RockPush.setAlias(mContext, alias);
                    mActivity.handleCallSend(call, "{pushstate:'"+pushstate+"',pushmsg:'"+pushmsg+"',brand:'"+Rock.getbrand()+"'}");
                }
            });
            Map<String,String> can = Rock.getMap();
            can.put("xiaomi_appid", AA.XIAOMI_APPID);
            can.put("xiaomi_appkey", AA.XIAOMI_APPKEY);
            can.put("huawei_appid", AA.HUAWEI_APPID);
            RockPush.init(mContext, can);
        }

        if(Rock.equals(action,"regJPush")){
            String regid = JPushReceiver.getRegid(mContext);
            if(Rock.isEmpt(regid)){
                JPushReceiver.regJPush(mContext);
            }else {
                mActivity.handleCallSend(call, "{pushstate:'jpush',regid:'"+regid+"'}");
            }
        }

        if(Rock.equals(action,"unregPush")){
            RockPush.unreg(mContext);
            //JPushReceiver.unregPush(mContext);
        }

        if(Rock.equals(action,"setTitle")){
             mActivity.handleSend(AA.ASCIPT_TITLE, params);
        }

        if(Rock.equals(action,"openCog")){
            mActivity.openXinhu("设置", "cog");
        }

        if(Rock.equals(action,"openXinhu")){
            mActivity.openXinhu(ret.get("name"), ret.get("type"));
        }

        if(Rock.equals(action,"updateChange")){
            new XinhuBase_cog(mActivity, null).updateChange(true);
        }

        if(Rock.equals(action,"openPdf")){
            mActivity.openXinhu("预览", "readerfile", params, 0);
        }

        //2024-07-26添加保存登录信息
        if(Rock.equals(action, "timerAlarm")){
            Rock.Sqlite.setOption("adminid", ret.get("adminid"));
            Rock.Sqlite.setOption("apiurl", ret.get("apiurl"));
            Rock.Sqlite.setOption("admintoken", ret.get("token"));
            Rock.Sqlite.setOption(AA.LOGIN_KEYSTR, params);
            Rock.APIURL         = ret.get("apiurl");
            Rock.adminid        = ret.get("adminid");
            Rock.admintoken     = ret.get("token");
            RCache.save(mContext, AA.KEY_TOKEN, Rock.admintoken);
        }

        if(Rock.equals(action, "createShortcut")){
            new XinhuBase_cog(mActivity, null).createShortcut();
        }

        //2024-11-30视频通话
        if(Rock.equals(action, "tonghua")){
            Xinhu.startActivity(mContext, TonghuaActivity.class, ret.get("name"), "", params);
            //Rock.Toast(mActivity, "暂无");
        }

        return str;
    }

    //定位
    @JavascriptInterface
    public void startLocation(String fun) {
        mActivity.startLocation(fun, 0);
    }


    /**
     * 获取script的js
     * */
    public String getScriptstr(String x5Ver)
    {
        String str = "";
        String[] stra = {"alert","ajax","toast","createMenu", "confirm", "prompt", "setMenu", "openWin","openWinnei","closeWin", "showProgress", "hideProgress", "addEventListener", "sendEvent","Notification","imageView","getPicture","upload","showMsg","openScan"};
        str = "api={";
        str += "systemType:'android',";
        str += "appVersion:'" + Rock.VERSION + "',";
        str += "apiUrl:'" + Rock.getApiUrl() + "',";
        str += "x5Ver:'" + x5Ver + "',";
        str += "nowtheme:'" + Rock.nowtheme + "',";
        str += "deviceModel:'" + Rock.getmodel() + "',";
        str += "deviceName:'" + Rock.getbrand() + "',";
        str += "deviceId:'" + Rock.getdeviceId(mContext) + "',";
        str += "rockFun:function(act,can,cal){if(!can)can='';var cals = '';if(cal){var rnd = 'call'+js.getrand()+'';cals = 'api.'+rnd+'';api[rnd]=cal;}return appxinhu.rockFun(act, JSON.stringify(can), cals);}";
        for (int i = 0; i < stra.length; i++) {
            str += "," + stra[i] + ":function(can,cal){return this.rockFun('" + stra[i] + "',can, cal)}";
        }
        str += "}";
        return str;
    }
}
