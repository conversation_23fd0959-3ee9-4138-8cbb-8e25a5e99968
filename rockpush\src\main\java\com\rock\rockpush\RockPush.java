/**
 * 推送服务操作
 * */


package com.rock.rockpush;

import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.huawei.hms.aaid.HmsInstanceId;
import com.huawei.hms.common.ApiException;
import com.xiaomi.mipush.sdk.MiPushClient;

import java.util.Map;

public class RockPush {

    public static final String APPPAGE   	        = "xinhuoanew2023";
    public static final Boolean DEBUG   	        = true;

    public static final int PUSH_XIAOMI_REGOK       = 101;
    public static final int PUSH_XIAOMI_REGER       = 102;
    public static final int PUSH_XIAOMI_SETALIAS    = 103;
    public static final int PUSH_UNKOWN 		    = 104;
    public static final int PUSH_HUAWEI_REGOK 		= 105;
    protected static Handler myHandler  = null;


    private static String xiaomi_appid   	        = "";
    private static String xiaomi_appkey   	        = "";
    private static String huawei_appid   	        = "";

    /**
     * 设置回调信息
     * */
    public static void  setmyHandler(Handler myhander) {
        myHandler = myhander;
    }

    public static void debug(String msg)
    {
        Log.e(APPPAGE, msg);
    }

    /**
     * 获取品牌
     * */
    public static String getbrand()
    {
        String str = android.os.Build.BRAND;
        str= str.toLowerCase();
        if(str.equals("honor"))str+="huawei";
        //str= "huawei";
        return str;
    }

    /**
     * 返回信息
     * */
    public static void back(int code, String bstr){
        if(myHandler==null)return;
        String  state = "unkown";
        if(code==PUSH_XIAOMI_REGOK)state="regok";
        if(code==PUSH_XIAOMI_REGER)state="regerr";
        if(code==PUSH_XIAOMI_SETALIAS)state="alias";
        if(code==PUSH_HUAWEI_REGOK)state="hwreg";
        if(code==PUSH_UNKOWN)state="notpush";
        Message message = new Message();
        message.what = code;
        Bundle mBundle = new Bundle();
        mBundle.putString("pushstate", state);
        mBundle.putString("pushmsg", bstr);
        message.setData(mBundle);
        //debug(""+getbrand()+":("+state+"):"+bstr+"");
        myHandler.sendMessage(message);
    }

    /**
     * 判断是不是小米手机
     * */
    public static Boolean isxiaomi()
    {
        String brand = getbrand();
        return (brand.indexOf("mi")>-1);
    }

    /**
     * 判断是不是华为手机
     * */
    public static Boolean ishuawei()
    {
        String brand = getbrand();
        return (brand.indexOf("huawei")>-1);
    }


    /**
     * 注册推送
     * */
    public static void  init(Context context, Map<String,String> ret) {
        xiaomi_appid    = ret.get("xiaomi_appid");
        xiaomi_appkey   = ret.get("xiaomi_appkey");
        huawei_appid    = ret.get("huawei_appid");
        if(isxiaomi()) {
            xiaomiPush(context, 0);
        }else if(ishuawei()) {
            huaweiToken(context, 0);
        }else{
            back(PUSH_UNKOWN, ""+getbrand()+"不支持推送");
        }
    }

    /**
     * 华为的推送服务
     * */
    private static void huaweiToken(final Context context, final int lx)
    {
        final String hwappid = huawei_appid;
        if (TextUtils.isEmpty(hwappid))return;
        new Thread() {
            @Override
            public void run() {
                try {
                    if(lx==0) {
                        String token = HmsInstanceId.getInstance(context).getToken(hwappid, APPPAGE);
                        if (!TextUtils.isEmpty(token)) back(PUSH_HUAWEI_REGOK, token);
                        debug("获取华为token：" + token + "");
                    }
                    if(lx==1) {
                        HmsInstanceId.getInstance(context).deleteToken(hwappid, APPPAGE);
                        debug("删除华为token");
                    }
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            }
        }.start();
    }

    /**
     * 注销推送
     * */
    public static void  unreg(Context context) {
        if(isxiaomi()) {
            xiaomiPush(context, 1);
        }else if(ishuawei()) {
            huaweiToken(context, 1);
        }
    }

    /**
     * 设置别名
     * */
    public static void setAlias(Context context, String alias)
    {
        if(isxiaomi()) {
            MiPushClient.setAlias(context, alias, alias);
        }
    }

    /**
     * 小米推送
     * */
    private static void xiaomiPush(Context context, int lx)
    {
        String appid  = xiaomi_appid;
        String appkey = xiaomi_appkey;
        if (TextUtils.isEmpty(appid) || TextUtils.isEmpty(appkey))return;
        if(lx==0){
            MiPushClient.registerPush(context, appid, appkey);
        }
        if(lx==1){
            MiPushClient.unregisterPush(context);
        }
    }
}
