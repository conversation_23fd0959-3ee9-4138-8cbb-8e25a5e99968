package com.rock.xinhuoanew;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.baselib.AA;
import com.baselib.CLog;
import com.baselib.RCache;
import com.baselib.Rock;

import cn.jpush.android.api.JPushInterface;


/**
 * 广播接收器
 * */
public class XinhuReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if(action.equals(AA.ALARM_ACTION)) {
            //NotifyBase.reStart(context, 1, 2*60);
        }
        if(action.equals(AA.ACTION_SHORTCUT)){
            //Rock.Toast(context, "快捷方式已创建");
            CLog.debug("快捷方式已创建");
        }

        //启动时
        if(action.equals(AA.ACTION_START)) {
            String token = RCache.get(context, AA.KEY_TOKEN);
            //启动极光推送20250603弃用
            if(!Rock.isEmpt(token)) {
               // Boolean bo = JPushReceiver.regJPush(context);
                //if(bo)NotifyBase.startJobServer(context, 10);
            }
        }
    }

}

