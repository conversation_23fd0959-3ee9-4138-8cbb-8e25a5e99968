<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">



    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="15dp"
        android:text="根据自己喜欢设置主题颜色"
        android:textSize="16dp"
        android:textColor="@color/hui"
        />


    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:layout_marginTop="5dp"
        android:background="@color/line2" />
    <RadioGroup
        android:layout_width="match_parent"
        android:background="@color/white"
        android:id="@+id/radioGroup1"
        android:layout_height="match_parent" >


        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton0"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="@color/black"
            android:layout_marginLeft="15dp"
            android:textSize="@dimen/listdp"
            android:text="■自定义" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:layout_marginLeft="15dp"
            android:background="@color/line2" />

        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="#1389D3"
            android:layout_marginLeft="15dp"
            android:textSize="@dimen/listdp"
            android:checked="true"
            android:text="■默认主题" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:layout_marginLeft="15dp"
            android:background="@color/line2" />

        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton2"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="#99CC66"
            android:layout_marginLeft="15dp"
            android:textSize="@dimen/listdp"
            android:text="■主题2" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:layout_marginLeft="15dp"
            android:background="@color/line2" />

        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton3"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="#003366"
            android:layout_marginLeft="15dp"
            android:textSize="@dimen/listdp"
            android:text="■主题3" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:layout_marginLeft="15dp"
            android:background="@color/line2" />

        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton4"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="#6666CC"
            android:layout_marginLeft="15dp"
            android:textSize="@dimen/listdp"
            android:text="■主题4" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:layout_marginLeft="15dp"
            android:background="@color/line2" />

        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton5"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="#CC3333"
            android:layout_marginLeft="15dp"
            android:textSize="@dimen/listdp"
            android:text="■主题5" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:layout_marginLeft="15dp"
            android:background="@color/line2" />

        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton6"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="#009966"
            android:layout_marginLeft="15dp"
            android:textSize="@dimen/listdp"
            android:text="■主题6" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:layout_marginLeft="15dp"
            android:background="@color/line2" />

        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton7"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="#333333"
            android:layout_marginLeft="15dp"
            android:textSize="@dimen/listdp"
            android:text="■主题7" />

    </RadioGroup>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />

    <com.view.RockTextView
        android:id="@+id/button"
        android:layout_width="120dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:background="@drawable/btn_zhu"
        android:gravity="center"
        android:text="保 存"
        android:textColor="@color/white"
        android:textSize="@dimen/listdp" />




</LinearLayout>