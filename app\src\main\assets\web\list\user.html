<script>
$(document).ready(function(){
	
	var c = {
		init:function(){
			this.loaddata();
			$('#myshow_name').html(''+adminname+'');
			$('#myshow_user').html(js.getoption('adminzhan'));
			$('#myshow_deptname').html(js.getoption('deptallname'));
			$('#myshow_companyname').html(js.getoption('nowcompany'));
			get('myface').src=xcy.getface(js.getoption('adminface'),get('myface'));
			var str = js.getoption('showmyinfo');
			if(str)this.loaddatashow(JSON.parse(str));
			xcy.showqipao('historyjson', '0');
			xcy.showqipao('agentjson', '1');
			if(apixhbool){
				$('#versiondiv').html('V'+api.appVersion+'');
			}
			if(apiurl.indexOf('demo.rockoa.com')>0){
				//if(adminid==8)$('#opendzi').show();
			}
			//$('#aboutdiv').show();
			if(js.getoption('outgroupopen')=='open'){
				get('switchCP').checked=true;
				$('#outgroupdiv').show();
			}
			var str = apiurl+'';
			$('#nowurl').html(str.substr(0,10)+'***'+str.substr(-4));
		},
		loaddata:function(lz){
			js.ajax('indexreim|showmyinfo', false,function(da){
				c.loaddatashow(da.data, true);
			},'get');
		},
		loaddatashow:function(ret, bo){
			c.data = ret;
			js.setoption('deptallname',ret.deptallname);
			js.setoption('ranking',ret.ranking);
			adminface	= ret.face;
			js.setoption('adminface',adminface);
			adminname 	= ret.name;
			js.setoption('adminname',adminname);
			js.setoption('adminid',ret.id);
			adminid 	= ret.id;
			get('myface').src=xcy.getface(adminface,get('myface'));
			$('#myshow_id').html(adminid);
			$('#myname').html(adminname);
			var url = location.href;
			var str = url.indexOf('http')>-1 ? ' <span class="rock_badge">本地</span>':'';
			str = '';
			$('#myshow_name').html(adminname+''+str+'');
			$('#myshow_deptname').html(ret.deptallname);
			$('#myshow_companyname').html(ret.companyinfo.name);
			js.setoption('nowcompany', ret.companyinfo.name);
			if(ret.companymode){
				$('#userchangediv').show();
			}else{
				$('#userchangediv').hide();
			}
			var sj = ret.mobile;
			if(sj)$('#myshow_mobile').html(''+sj.substr(0,3)+'****'+sj.substr(-4)+'');
			$('#myshow_ranking').html(ret.ranking);
			if(bo)js.setoption('showmyinfo', JSON.stringify(ret));
			xcy.reloadok();
		},
		loginout:function(){
			var msg = '确定要退出登录吗？';
			rockconfirm(msg, function(jg){
				if(jg=='yes')c.loginouts();
			});
		},
		loginouts:function(){
			var msg = '退出中...';
			if(apixhbool){
				api.showProgress({msg:msg});
			}else{
				js.loading(msg)
			}
			js.ajax('login|loginexit',false,function(){
				c.loginoutok();
			}, 'get', function(){
				c.loginoutok();
			});
		},
		loginoutok:function(){
			xcy.loginExit();
			if(apixhbool){
				api.showMsg({
					msg:'已成功退出',
					type:'success',
					time:1
				},function(){
					js.reload();
				});
			}else{
				js.wx.msgok('已成功退出',function(){
					js.reload();
				},1);
			}
		},
		editpass:function(){
			xcy.opennei({name:'修改密码','url':'editpass'});
		},
		setstyles:function(){
			xcy.opennei({name:'切换主题',url:'theme'});
		},
		setfontsize:function(){
			xcy.opennei({name:'字体大小',url:'themd'});
		},
		clearcache:function(){
			if(!apixhbool){
				js.wx.msgok('清除成功');
			}else{
				api.rockFun('clearCache');
			}
		},
		changecompany:function(){
			xcy.opennei({name:'切换单位',url:'company'});
		},
		gengxinx:function(){
			if(apixhbool && apptypeleixing){
				api.rockFun('updateChange');
			}else{
				js.msg('msg', '已是最新版本')
			}
		},
		editface:function(){
			if(!apixhbool)return;
			api.createMenu({
				menu:'修改头像|'+maincolor+',拍照,相册中选'
			},function(ret){
				var index = ret.menuIndex;
				if(index==1)c.paizhao('camera');
				if(index==2)c.paizhao('album');
			});
		},
		paizhao:function(lx){
			api.getPicture({
				sourceType:lx
			},function(ret){
				c.sendapifile(ret.filepath, ret);
			});
		},
		changeappok:function(ret){
			if(ret.data){
				var ext = ret.data.substr(-3).toLowerCase();
				if(!js.isimg(ext)){
					js.wx.msgerror('请选图片类型');
				}else{
					c.sendapifile(ret.data);
				}
			}
		},
		sendapifile:function(path,ret){
			api.showProgress({msg:'上传中...'});
			var url = js.apiurl('upload','upfile',{noasyn:'yes',thumbnail:'150x150'});
			api.upload({
				filepath:path,
				url:url
			},function(ret,err){
				if(ret){
					if(ret.valid==0){
					}else{
						c.sendfileok(ret);
					}
				}else{
					api.showMsg({
						type:'error',
						msg:err.responseText
					});
				}
			});
		},
		sendfileok:function(d1){
			js.ajax('user|editface',{fid:d1.id}, function(ret){
				var face;
				if(ret && (face = ret.data)){
					adminface 	= face;
					c.data.face = face;
					js.setoption('adminface', face);
					get('myface').src=face;
					api.showMsg({msg:'头像已修改'});
				}else{
					api.showMsg({type:'error',msg:'修改失败'});
				}
			});
		},
		openzjx:function(){
			var d = this.data;
			d.companyinfo = '';
			js.setoption('nowuser', JSON.stringify(d));
			var ustr={name:d.name,id:d.id,url:'userinfo'};
			xcy.opennei(ustr);
		},
		qiehuanke:function(){
			setTimeout(function(){c.qiehuankes()},500);
		},
		qiehuankes:function(){
			var isop = 'close';
			if(get('switchCP').checked)isop='open';
			js.setoption('outgroupopen', isop);
			if(isop == 'close')xcy.outgroup.clearchache();
			js.ajax('indexreim|openoutqun',{isop:isop},false,'get',function(){js.msg('none');});
		},
		aboutwm:function(){
			xcy.opennei({name:'关于我们',url:'about'});
		},
		opencog:function(){
			if(apixhbool){
				api.rockFun('openCog');
			}else{
				xcy.opennei({name:'设置',url:'cog'});
			}
		}
	}
	js.initbtn(c);
	c.init();
	
	xcy.touchload['user']=function(){
		c.loaddata('');
	}
});
</script>

<div class="rock_cells" style="margin-top:15px">
	<div class="rock_cell rock_cell_access">
		<div class="rock_cell__hd"  clickevt="editface" style="position: relative;margin-right: 10px;">
			<img src="images/noface.png" class="r-face" id="myface"  style="width:60px;display: block;height:60px"/>
		</div>
		<div class="rock_cell__bd"  clickevt="openzjx">
			<p id="myshow_name" class="fontsize">您好</p>
			<p id="myshow_deptname" style="color: #aaaaaa;margin-top:5px;font-size:14px;">部门</p>
		</div>
		<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
	</div>
</div>	



<div class="rock_cells__title">个人信息</div>
<div class="rock_cells">
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			职位
		</div>
		<div style="color: #888888;" id="myshow_ranking"></div>
	</div>
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			我手机号
		</div>
		<div id="myshow_mobile" style="color: #888888;"></div>
	</div>
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			用户名
		</div>
		<div style="color: #888888;" id="myshow_user"></div>
	</div>
	<div class="rock_cell rock_cell_access" id="userchangediv" style="display:none" clickevt="changecompany">
		<div class="rock_cell__bd fontsize">
			切换单位
		</div>
		<div style="color:#888888"><span id="myshow_companyname"></span>&nbsp;&nbsp;<span style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></span></div>
	</div>
	<div class="rock_cell rock_cell_access" clickevt="editpass">
		<div class="rock_cell__bd fontsize">
			修改密码
		</div>
		<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
	</div>
</div>


<div class="rock_cells__title">关于</div>
<div class="rock_cells">

	<div class="rock_cell rock_cell_access"  clickevt="opencog">
		<div class="rock_cell__bd fontsize">
			设置
		</div>
		<div style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></div>
	</div>
	<div class="rock_cell">
		<div class="rock_cell__bd fontsize">
			当前地址
		</div>
		<div style="color:#888888"><span id="nowurl"></span></div>
	</div>
	<div class="rock_cell rock_cell_access" clickevt="gengxinx">
		<div class="rock_cell__bd fontsize">
			当前版本
		</div>
		<div style="color:#888888"><span id="versiondiv">V0.0.1</span>&nbsp;&nbsp;<span style="color:#aaaaaa;font-size:20px"><i class="icon-angle-right"></i></span></div>
	</div>
	
	<div class="rock_cell rock_cell_access" style="display:none" id="outgroupdiv">
		<div class="rock_cell__bd fontsize">
			开启外部群
		</div>
		<div style="color:#888888">
			<label for="switchCP"  clickevt="qiehuanke" class="rock_switch-cp">
				<input id="switchCP" class="rock_switch-cp__input" type="checkbox">
				<div class="rock_switch-cp__box"></div>
			</span>
		</div>
	</div>
</div>


<div style="height:20px"></div>
<div class="rock_btn_cell fontsize" clickevt="loginout">退出登录</div>
<div style="height:15px"></div>
