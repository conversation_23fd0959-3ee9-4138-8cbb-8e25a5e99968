<script>
$(document).ready(function(){
	{params}
	
	var c = {
		init:function(){
			var str = '<span clickevt="addnew" class="r-position-right r-header-btn"><i class="icon-plus"></i></span>';
			$('#header_show').append(str);
			this.reshow();
			js.initbtn(this);
		},
		reshow:function(){
			var gsstr = js.getoption('loginjson');
			if(!gsstr)gsstr='[{"url":"'+apiurl+'","title":"'+systemtitle+'"}]';
			var da 	  = JSON.parse(gsstr);
			this.data = da;
			console.log(da);
			$('#list').html('');
			var nurl = js.getoption('apiurl');
			if(!nurl)nurl = apiurl;
			apiurl	 = nurl;
			for(var i=0;i<da.length;i++)this.showlist(da[i],i, nurl);
		},
		showlist:function(a,i, nurl){
			var sin = a.titleall;if(!sin)sin = a.title;
			if(a.url==nurl)sin+=' <font color="green"><i class="icon-ok"></i></font>';
			var sstr = a.url;
			if(a.adminname)sstr+='('+a.adminname+')';
			if(a.url!=nurl)sstr+=' &nbsp; <span onclick="lx{rand}.dellist('+i+')"><i class="icon-trash"></i></span>';
			var s='<a onclick="lx{rand}.openuinfo('+i+')" class="rock_cell rock_cell_access">';
			s+='	<div class="rock_cell__hd">';
			s+='		<img class="r-face" src="'+a.url+'images/logo.png" style="width:34px;height:34px;display:block;margin-right:10px"></div>';
			s+='	</div>';
			s+='	<div class="rock_cell__bd">';
			s+='		<p class="fontsize">'+sin+'</p>';
			s+='		<p style="font-size:13px;color:#888888">'+sstr+'</p>';
			s+='	</div>';
			s+='</a>';
			$('#list').append(s);
		},
		addnew:function(){
			setcogurl();
		},
		login:function(){
			xcy.back();
		},
		openuinfo:function(oi){
			var d = this.data[oi];
			if(this.isdelbool || !d || apiurl==d.url)return;
			js.setoption('nowtheme','');
			js.setoption('agentjson', '');
			js.setoption('historyjson', '');
			js.setoption('myhomenum', '');
			js.setoption('tonghuabo', '');
			js.setoption('deptjson', '');
			js.setoption('userjson', '');
			js.setoption('groupjson', '');
			js.setoption('silderarr', '');
			js.setoption('apiurl',d.url);
			xcy.sendEvent('shuaxin','rocklogin');
			
			if(apixhbool){
				api.showMsg({
					msg:'切换成功',
					type:'success',
					time:2
				},function(){
					xcy.back();
				});
			}else{
				js.msgok('切换成功',function(){
					xcy.back();
				},1);
			}
		},
		dellist:function(oi){
			if(this.data.length==1){
				js.msgerror('最后一个不要删除');return;
			}
			this.data[oi].isdel = true;
			this.isdelbool = true;
			this.reapis();
			setTimeout(function(){c.isdelbool=false},1000);
		},
		setapiurl:function(dz){
			if(!dz)return;
			if(dz.substr(0,4)!='http')dz='http://'+dz;
			if(dz.substr(-1)!='/')dz+='/';
			apiurl = dz;
			this.yangzhen('验证中...');
		},
		yangzhen:function(str){
			if(str)js.loading(str);
			js.ajax('login|appinit|none', false, function(ret){
				var da = ret.data;
				c.addapi(da);
			},'get', function(st){
				js.msgerror('验证失败：“'+apiurl+'”无法访问');
			});
		},
		addapi:function(d){
			var url = apiurl,da=this.data,ds=[],i,d1,isnuw=false;
			for(i=0;i<da.length;i++){
				d1 = da[i];
				if(!d1.isdel){
					if(d1.url==url){
						isnuw = true;
						d1.title = d.title;
						d1.titleall = d.titleall;
						d1.loginyzm = d.loginyzm;
						d1.apptheme = d.apptheme;
					}
					ds.push(d1);
				}
			}
			if(!isnuw){
				d.url = url;
				ds.push(d);
			}
			js.setoption('loginjson', JSON.stringify(ds));
			this.reshow();
		},
		reapis:function(){
			var i,da=this.data,d1,ds=[];
			for(i=0;i<da.length;i++){
				d1 = da[i];
				if(!d1.isdel){
					ds.push(d1);
				}
			}
			js.setoption('loginjson', JSON.stringify(ds));
			this.reshow();
		}
	}
	c.init();
	
	lx{rand} = c;
	
	var tempurlapi = '',nowshru='';
	setcogurl=function(){
		//if(js.ajaxbool)return;
		if(apixhbool){
			//var jz = api.rockFun('clipBoardget');
			//if(jz && jz.indexOf('http')==0)tempurlapi = jz;
			if(tempurlapi==''){
				tempurlapi = apiurl+'';
			}
			api.prompt({
				title:'系统地址',
				msg:'请输入你OA系统地址',
				text:tempurlapi
			}, function(ret) {
				var index = ret.buttonIndex;
				var dz = ret.text;
				tempurlapi='';
				if(index==1 && dz)c.setapiurl(dz);
			});
		}else{
			tempurlapi = apiurl+'';
			js.wx.prompt('','请输入你OA系统地址,<a href="javascript:;" onclick="get(\'prompttxt\').value=\'\'" class="zhu">清空</a>',function(dz){
				c.setapiurl(dz);
			},tempurlapi);
		}
	}
	
	xcy.initApp=function(){
		initAppbool = true;
		api.setMenu({menu:'＋新增地址'},function(ret){
			if(ret.menuIndex==1){
				setcogurl();
			}
		});
	};
});
</script>

<div class="rock_cells__title">可登录列表</div>
<div id="list" style="margin-top:0" class="rock_cells"></div>

<div class="rock_footer" align="center" style="margin-top:20px">
	<a href="javascript:;" class="zhu" clickevt="login" class="rock_footer__link">&lt;返回登录</a>
	&nbsp;
	<a href="javascript:;" class="zhu" clickevt="addnew" class="rock_footer__link">＋新增</a>
</div>

<div class="blank15"></div>