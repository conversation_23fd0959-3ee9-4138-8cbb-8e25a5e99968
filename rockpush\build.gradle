plugins {
    id 'com.android.library'
}

android {
    compileSdk 31

    defaultConfig {
        minSdk 21
        targetSdk 31
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        debug {
            debuggable true
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

dependencies {
    // 使用compileOnly依赖，编译时可见，但不打包到AAR中，实际依赖由主app模块提供
    compileOnly fileTree(dir: 'libs', include: ['*.aar']) //小米推送编译时依赖
    implementation 'com.huawei.hms:push:6.9.0.300'  //引入华为推送
}