<script>
$(document).ready(function(){
	{params}
	
	var c = {
		url:'',
		yingnum:'',
		init:function(){
			this.url = params.dizhi;
			var url  = this.url;
			/*
			var x5ver= api.x5Ver;
			if(x5ver && x5ver>0){
				var  bo = c.openpdfs(url);
				if(bo)return;
			}
			*/
			setTimeout('api.showProgress()',10);
			xcy.setTitle('加载中..');
			location.href=this.url;
		},
		//判断是不是打开文件（弃用）
		openpdfs:function(str){
			if(str.indexOf('m=public&a=fileviewer')>0){
				var fileid = js.request('id','', str);
				js.ajax('file|getfilenew',{id:fileid}, function(ret){
					if(!c.openpdf(ret.data)){
						location.href=c.url;
					}
				},'get','加载中...');
				return true;
			}
			return false;
		},
		openpdf:function(d){
			var path = d.pdfpath;
			if(d.fileext=='pdf'){
				path = d.filepath;
				if(d.filepathout)path = d.filepathout;
			}
			if(path){
				path = xcy.getface(path);
				api.rockFun('openPdf', {
					fileurl:path,
					filename:d.filename,
					filesizecn:d.filesizecn,
					animtype:'show',
					opentyle:'x5'
				});
				api.closeWin({animtype:'hide'});
				return true;
			}
			return false;
		}
	}
	c.init();
});
</script>
<div align="center"  style="margin-top:50px"><i style="height:40px;width:40px" class="rock-loading"></i></div>