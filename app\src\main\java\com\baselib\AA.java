/**
 * 说明：Android基础使用
 * 创建：雨中磐石  from www.rockoa.com
 * 时间：2014-11-28
 * 邮箱：<EMAIL>
 * QQ：290802026
 * */

package com.baselib;



public final class AA{

	public static final String APPPAGE   		= "xinhuoanew2023";
	public static final String APPTYPE   		= "xinhuoa"; //app类型，xinhuoa,rockkefu
	public static final boolean DEBUG   		= false;		 //调试模式
	public static final boolean APPLOCAL  		= false;		 //默认true不要改,false是给客户编译
	public static final int YINGTYPE  			= 0;		 //0默认


	public static final String SHOWURL		 	= "file:///android_asset/web/"; //*不可修改*远程系统地址
	public static final String APIURL		 	= "http://demo.rockoa.com/"; //默认OA的地址改成你自己的如：http://demo.rockoa.com/

	//public static final String SHOWURL_DEBUG 	= "http://192.168.0.108/app/xinhuoa_java/app/src/main/assets/web/"; //远程系统地址（开发调试）
	public static final String SHOWURL_DEBUG 	= "file:///android_asset/web/"; //远程系统地址（开发调试）
	public static final String APIURL_DEBUG  	= "http://192.168.5.200/app/xinhu/"; //默认OA的地址（开发调试）


	//小米的推送配置(没有不需要设置为空)
	public static final String XIAOMI_APPID 	= "2882303761520187654";
	public static final String XIAOMI_APPKEY 	= "5432018755654";

	//华为推送配置(没有不需要设置为空)
	public static final String HUAWEI_APPID 	= "108405719";












	//---------------下面那些固定值就不要改了--------------------------
	public static final int DB_VERSION 		= 1;

	public static final int HTTPB_SUCCESS 	= 200;
	public static final int HTTPB_ERROR   	= 301;
	public static final int HTTPB_TIMEOUT 	= 300;
	public static final int HTTPB_NOTWEN   	= 302;//无法访问网络
	public static final int HTTPB_BACKCODE 	= 303;//网络请求

	public static final int RESULTCODE_CHANGEFILE   	= 20;
	public static final int RESULTCODE_CHANGECAMERA   	= 22;
	public static final int RESULTCODE_CHANGECAMERB   	= 23;
	public static final int RESULTCODE_CAMERA   		= 24;
	public static final int RESULTCODE_CHANGEALBUM   	= 25;
	public static final int RESULTCODE_CAMERAPI   		= 26;
	public static final int RESULTCODE_SCANAPI   		= 27;
	public static final int RESULTCODE_LOGINSERVER  	= 28;
	public static final int RESULTCODE_SCAN  			= 29;
	public static final int RESULTCODE_FONTSIZE  		= 30;

	public static final int PERMISSION_CAMERA			= 50;
	public static final int PERMISSION_RECORD_AUDIO		= 52;
	public static final int PERMISSION_LOCATION			= 53;


	public static final int ASCIPT_CALL 		= 401;
	public static final int ASCIPT_AJAX 		= 403;
	public static final int ASCIPT_TITLE 		= 404;
	public static final int WEBVIEW_OPEN 		= 402;
	public static final int ASCIPT_HIDEKEY 		= 405;

	public static final int HTTPG_SOTIMEOUT 	= 30*1000; //读取超时时间
	public static final int HTTPG_CONNTIMEOUT 	= 30*1000; //请求超时时间
	public static final int HTTPG_UPLOADTIMEOUT = 2*60*1000; //上传超时时间(2分钟)



	public static final String ALARM_ACTION			= "testalarmaction";
	public static final String TONGHUA_ACTION		= "tonghuaaction";
	public static final String LOGIN_KEYSTR			= "logininfo2024";
	public static final String MAIN_ALARM			= "mainalarmaction";
	public static final String ACTION_SHORTCUT		= "createShortcut";
	public static final String OPEN_SOCKET			= "opensocket";
	public static final String ACTION_START			= "com.rock.xinhuoa.ACTIONSTART";

	public static final String KEY_TOKEN			= "admintoken";

}