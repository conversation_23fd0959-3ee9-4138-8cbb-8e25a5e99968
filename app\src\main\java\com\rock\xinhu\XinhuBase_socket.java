package com.rock.xinhu;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.TextView;

import com.baselib.AA;
import com.baselib.AR;
import com.baselib.Rock;
import com.rock.xinhuoanew.NotifyBase;

public class XinhuBase_socket extends XinhuBase {

    private TextView btnObj,msgView;

    public XinhuBase_socket(Context context, View view) {
        super(context, view);
    }

    public void initBase()
    {
        btnObj    = mView.findViewById(AR.getID("button"));
        msgView   = (TextView) mView.findViewById(AR.getID("neimsg"));
        isOpens();
    }

    public void setOnClickListener(View.OnClickListener cick)
    {
        btnObj.setOnClickListener(cick);
        mView.findViewById(AR.getID("qxbtn")).setOnClickListener(cick);
        Rock.setBackground(btnObj);
    }

    public void onViewClick(int id)
    {
        if(id==AR.getID("button")){
            Boolean bool = isOpens();
            if(bool){
                Rock.Sqlite.setOption(AA.OPEN_SOCKET, "no");
                NotifyBase.stopServer(mContext);
            }else{
                Rock.Sqlite.setOption(AA.OPEN_SOCKET, "yes");
                String logininfo      = Rock.Sqlite.getOption(AA.LOGIN_KEYSTR);
                //if(!Rock.isEmpt(logininfo)) NotifyBase.firstStart(mContext, 0);
            }
            isOpens();
        }
        if(id==AR.getID("qxbtn")){
            Rock.openAppSettings(mContext);
        }
    }

    public void onBack()
    {
        mObj.exitBack("socket", "");
    }

    private Boolean isOpens(){
        Boolean bool = false;
        String val = Rock.Sqlite.getOption(AA.OPEN_SOCKET);
        if(Rock.equals(val, "yes"))bool = true;
        if(bool){
            msgView.setText("已开启");
            btnObj.setText("停用");
            msgView.setTextColor(Color.parseColor("#009966"));
        }else{
            msgView.setText("未开启");
            btnObj.setText("开启");
            msgView.setTextColor(Color.RED);
        }
        return bool;
    }
}
