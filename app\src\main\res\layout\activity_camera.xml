<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
android:id="@+id/rl"
android:layout_width="fill_parent"
android:background="@color/black"
android:layout_height="fill_parent" >

    <LinearLayout
        android:id="@+id/back"
        android:layout_width="@dimen/headheight"
        android:layout_height="@dimen/headheight"
        android:background="@drawable/btn_tm"
        android:layout_alignParentLeft="true"
        android:layout_marginTop="28dp"
        android:gravity="center"
        android:orientation="vertical">
        <com.view.RockTextViewIcon
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_back"
            android:textSize="20dp"
            android:textColor="@color/white" />
    </LinearLayout>


    <ImageView
        android:id="@+id/ImageControl"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_centerInParent="true"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="10dp"
        android:scaleType="fitCenter"
        android:src="@mipmap/noimg"
        android:visibility="gone" />


    <CheckBox
        android:id="@+id/yuanbtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="20dp"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:text="发送原图"
        android:textSize="16dp"
        android:textColor="@color/white" />

    <TextView
        android:id="@+id/sendbtn"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/btn_zhu"
        android:gravity="center"
        android:text="发送"
        android:textColor="@color/white"
        android:textSize="18dp" />

</RelativeLayout>