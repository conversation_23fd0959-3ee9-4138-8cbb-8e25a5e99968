plugins {
    id 'com.android.application'
    id 'com.huawei.agconnect' //华为推送用到这里
}

android {
    compileSdk 31

    defaultConfig {
        applicationId "com.rock.xinhuoanew"
        targetSdkVersion 31
        minSdk 21
        targetSdk 31
        versionCode 192         //版本序号
        versionName "0.1.92"    //版本名称2025-01-15

        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a", "x86_64"
        }

        manifestPlaceholders = [
                JPUSH_PKGNAME : applicationId,
                JPUSH_APPKEY  : "", //极光推送上注册的(没有不需要设置为空)615a996d9c8f81ac5c90ff3f
                JPUSH_CHANNEL : "xinhuoa", //暂时填写默认值即可.
        ]

        //buildConfigField "String[]", "RockpushConfig", "{\"${xiaomi_appid}\",\"${xiaomi_appkey}\", \"${huawei_appid}\"}"
    }

    //配置keystore签名证书
    signingConfigs {
        release {
            storeFile file("nxhymas.keystore")
            storePassword "139139139"
            keyAlias "nxhymas"
            keyPassword "139139139"
        }
    }

    buildTypes {
        debug {
            debuggable true
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            def date = new Date().format("yyyyMMddhhmmss", TimeZone.getTimeZone("GMT+08"))
            outputFileName = "xinhuoa_v${variant.versionName}_${date}.apk"
        }
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.3.0'
    implementation 'com.tencent.map.geolocation:TencentLocationSdk-openplatform:7.3.0' //腾讯地图定位
   //implementation 'cn.bingoogolapple:bga-qrcode-zbar:1.3.7' //扫一扫
    implementation 'com.github.bingoogolapple.BGAQRCode-Android:zbar:1.3.8' //扫一扫
    implementation 'org.java-websocket:Java-WebSocket:1.4.0' //websocket
    implementation project(':rockpush') //引入推送的模块
    //implementation 'com.wdeo3601:pdf-view:1.0.4' //预览pdf


    implementation fileTree(dir: 'libs', include: ['*.aar'])

    //implementation files('libs\\jpush-android-5.5.3.jar')
    implementation 'cn.jiguang.sdk:jpush:4.9.0'
    implementation 'cn.jiguang.sdk:jcore:4.1.0'               //使用指定的 JCore 版本
}