package com.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Typeface;
import android.util.AttributeSet;

import com.baselib.CLog;
import com.baselib.Rock;

import java.util.HashMap;
import java.util.Map;


public class RockTextViewIcon extends androidx.appcompat.widget.AppCompatTextView {

    public RockTextViewIcon(Context context) {
        super(context);
    }

    public RockTextViewIcon(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public RockTextViewIcon(Context context, AttributeSet attrs) {
        super(context, attrs);
        importFont(context);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
    }
    //设置字体图标
    private void importFont(Context context){
        this.setTypeface(Typeface.createFromAsset(context.getAssets(), "icon/iconfont.ttf"));
    }
}