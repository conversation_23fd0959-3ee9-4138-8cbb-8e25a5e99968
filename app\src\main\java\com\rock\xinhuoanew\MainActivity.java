package com.rock.xinhuoanew;





import android.content.BroadcastReceiver;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;


import android.os.Build;
import android.os.Bundle;
import android.view.View;

import com.baselib.RCall;

import com.baselib.RTouchListener;
import com.dialog.Dialog;

import com.baselib.CallBack;
import com.rock.xinhu.XinhuBase_cog;

import com.baselib.AA;
import com.baselib.AR;
import com.baselib.CLog;
import com.baselib.Rock;

import java.util.Timer;
import java.util.TimerTask;

import cn.jpush.android.api.JPushInterface;


public class MainActivity extends WebviewActivity {

    private Boolean yunBool = false;

    /**
     * 加载配置
     * */
    public void getConfig()
    {
        Rock.nowtheme   = Rock.Sqlite.getOption("nowtheme");
        String fontsize = Rock.Sqlite.getOption("fontsize");
        Rock.APIURL     = Rock.Sqlite.getOption("apiurl");
        if(Rock.isEmpt(Rock.APIURL))Rock.APIURL     = Rock.getApiUrl();
        if(Rock.isEmpt(fontsize))fontsize  = "0";
        Rock.fontsize   = Integer.parseInt(fontsize);
        if(!Rock.isEmpt(Rock.nowtheme))Rock.defTheme = Rock.nowtheme;
        Rock.adminid       = Rock.Sqlite.getOption("adminid");
        Rock.admintoken    = Rock.Sqlite.getOption("admintoken");
    }

    protected void initCreate() {
        if(Rock.mainOpen) {
            CLog.error("主界面已经打开过了");
            finish();
            return;
        }
        Rock.qianBool   = true;
        Rock.mainOpen   = true;
        setContentView(R.layout.activity_main);
        this.yunBool    = true;
        Rock.Sqlite     = Rock.getSqlite(this);
        Rock.VERSION    = BuildConfig.VERSION_NAME;
        Rock.deviceId   = Rock.getdeviceId(this);
        AR.setContext(this);
        Rock.Sqlite.getappnum();
        getConfig();//-----首次加载就可以 ------

        //从快捷方式过来的
        Bundle bundle = this.getIntent().getExtras();
        String apiurl = "";
        if(bundle != null) apiurl = bundle.getString("apiurl");

        findViewById(R.id.more).setOnClickListener(OnViewClickListener);
        findViewById(R.id.title).setOnClickListener(OnViewClickListener);
        String url = Rock.getAppHtml();
        //if(!Rock.isEmpt(apiurl)) url += "?apiurl="+apiurl+"";
        gotoUrl(url);
        regBgstart();

        CLog.debug("initMains_"+Rock.deviceId+"_"+Rock.getbrand()+"_"+Rock.getmodel()+"_Android"+ Build.VERSION.RELEASE+"_SDK"+Build.VERSION.SDK_INT+"");

        if(!Rock.isEmpt(Rock.nowtheme)) {
            View headerv = (View) findViewById(R.id.headertop);
            headerv.setBackgroundColor(Color.parseColor(Rock.nowtheme));
        }

        setOnLongClick(new View.OnLongClickListener(){
            @Override
            public boolean onLongClick(View view) {
                Xinhu.sendBroadcastApi(mActivity, guanbo_Action, "longmenu");
                return true;
            }
        });

        //检查更新
        new XinhuBase_cog(this, null).updateChange(false);

        XinhuService.mainOpenBool = true;

        //统计app清空，不要可以删除
        if(AA.APPLOCAL)Xinhu.AppTotal(this);
    }




    //点击
    protected void onViewClick(View v) {
        super.onViewClick(v);
        int id = v.getId();
        if(id == R.id.title){
            if(AA.DEBUG){
                //DialogLoad.show(this);

            }else {
                if (Rock.deviceId.equals("7b9b874fdecbe6fc")) openXinhu("登录", "login");
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Xinhu.startService(mActivity, Xinhu.SERVICETYPE_RESTART, "main");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if(mainReceiver != null)unregisterReceiver(mainReceiver);
        if(this.yunBool) {
            Rock.mainOpen = false;
            XinhuService.mainOpenBool = false;
        }
    }


    protected void onhandleCallback(int what, int arg1, String bstr){
        super.onhandleCallback(what,arg1,bstr);
        if(what== AA.WEBVIEW_OPEN && bstr.equals("open_1")){
            View tv = (View)findViewById(R.id.mainbody);
            tv.setVisibility(View.GONE);
            if(!Rock.isEmpt(Rock.nowtheme)) {
                setStatusColor(Color.parseColor(Rock.nowtheme));
            }else{
                setStatusColor(getResources().getColor(R.color.mcolor));
            }
            if(AA.YINGTYPE>0){
                String firstabout = Rock.Sqlite.getOption("firstabout");
                if(!firstabout.equals("yes"))showXieyi();
            }
        }

    }



    private int keyci = 0;
    protected void onKeyBack(){
        keyci++;
        if(keyci==1) {
            Rock.Toast(this, "在按一次退出应用");
            new Timer().schedule(new TimerTask() {public void run() { keyci=0; }}, 1000);
        }else{
            Rock.toastHide();
            finish();
        }
    }

    private void showXieyi()
    {
        String cont = "您在使用信呼OA或服务前，请认真阅读并充分理解《用户协议》《隐私政策》的相关说明：\n1、我们需要/可能需要收集和使用您的个人信息包括您须授权和可选择授权我们收集、使用的信息。\n2、您可以访问、删除、修改您的个人信息，管理您的账户。\n点击“同意”继续使用，点“不同意”退出应用。\n您可以通过“我的-设置-关于我们”再次查看隐私政策。";
        Dialog.confirm(this, cont, "使用前说明", null, null);
        Dialog.setOkbtn("同意", new CallBack(){
            @Override
            public void back() {
                Rock.Sqlite.setOption("firstabout", "yes");
                Rock.Toast(mActivity, "感谢您的支持");
            }
        });
        Dialog.setCenterbtn("查看协议", new CallBack(){
            @Override
            public void back() {
                openXinhu("关于我们", "about");
            }
        });
        Dialog.setCancelbtn("不同意", new CallBack(){
            @Override
            public void back() {
                finish();
            }
        });
        Dialog.setCancelable(false);
    }

    //注册一个关播
    private BroadcastReceiver mainReceiver = null;
    private void regBgstart(){
        IntentFilter filter = new IntentFilter();
        filter.addAction(AA.MAIN_ALARM);
        filter.addAction(AA.TONGHUA_ACTION);
        //filter.addAction(Intent.ACTION_CLOSE_SYSTEM_DIALOGS);//回到桌面
        mainReceiver = new BroadcastReceiver(){
            @Override
            public void onReceive(Context context, Intent intent) {
                String action   = intent.getAction();
                String content  = "";
                Bundle bundle   = intent.getExtras();
                if(bundle !=null) {
                    content = intent.getExtras().getString("content");
                }
                if(Rock.equals(action, AA.MAIN_ALARM)) {
                    if (isQianBool) Rock.Sqlite.setOption(action, content); //在前台才保存
                }
                if(Rock.equals(action, AA.TONGHUA_ACTION)) {
                    RCall.message(context, content);
                }
            }
        };
        registerReceiver(mainReceiver, filter);
    }


}