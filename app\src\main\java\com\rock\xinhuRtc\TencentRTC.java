package com.rock.xinhuRtc;

import android.content.Context;
import android.os.Bundle;


import com.baselib.CLog;
import com.baselib.CallBack;
import com.baselib.RCall;
import com.tencent.rtmp.ui.TXCloudVideoView;
import com.tencent.trtc.TRTCCloud;
import com.tencent.trtc.TRTCCloudDef;
import com.tencent.trtc.TRTCCloudListener;


public class TencentRTC {

	public static Context mContext;

	public static TRTCCloud mTRTCCloud;


	private static CallBack mCallback	= null;

	/**
	 * 创建
	 * */
	public static void create(Context context, CallBack call) {
		if(mTRTCCloud != null)stop();

		mContext 	= context;
		mCallback 	= call;
		mTRTCCloud 	= TRTCCloud.sharedInstance(context);
		mTRTCCloud.addListener(new TRTCCloudListener() {
			@Override
			public void onError(int errCode, String errMsg, Bundle extraInfo) {
				super.onError(errCode, errMsg, extraInfo);
				CLog.error(errMsg + extraInfo, "-TRTC");
			}

			@Override
			public void onEnterRoom(long result) {
				super.onEnterRoom(result);
				CLog.debug("我进入房间：" + result, "-TRTC");
			}

			@Override
			public void onExitRoom(int reason) {
				super.onExitRoom(reason);
				CLog.debug("我退出房间" + reason, "-TRTC");
			}

			@Override
			public void onRemoteUserEnterRoom(String userId) {
				CLog.debug("来了房间" + userId, "-TRTC");
				if(mCallback != null)mCallback.back(RCall.ROOM_IN, userId);
			}

			// 感知远端用户离开房间的通知，并更新远端用户列表(mUserList)
			@Override
			public void onRemoteUserLeaveRoom(String userId, int reason) {
				CLog.debug("离开房间" + userId, "-TRTC");
				if(mCallback != null)mCallback.back(RCall.ROOM_OUT, userId);
			}
		});
	}

	/**
	 * 加入房间
	 * */
	public static void joinChannel(String appid, String userid, String roomid, String usersig) {
		TRTCCloudDef.TRTCParams params = new TRTCCloudDef.TRTCParams();
		params.sdkAppId = Integer.parseInt(appid);
		params.userId 	= "rock"+userid;
		params.strRoomId= roomid;
		params.userSig 	= usersig;
		params.role = TRTCCloudDef.TRTCRoleAnchor;
// 如果您的场景是“在线直播”，请将应用场景设置为 TRTC_APP_SCENE_LIVE
		mTRTCCloud.enterRoom(params, TRTCCloudDef.TRTC_APP_SCENE_LIVE);
	}

	/**
	 * 停止全部
	 * */
	public static void stop() {
		if (mTRTCCloud != null) {
			mTRTCCloud.stopLocalAudio();
			mTRTCCloud.stopLocalPreview();
			mTRTCCloud.exitRoom();
			mTRTCCloud.setListener(null);
		}
		mCallback  = null;
		mTRTCCloud = null;
		TRTCCloud.destroySharedInstance();
	}

	/**
	 * 显示本地摄像头
	 * @param isqian 是否前置设置图 true前缀，false后置
	 * */
	public static void startlocalVideo(TXCloudVideoView cameraVideo, Boolean isqian) {
		// 设置本地画面的预览模式：开启左右镜像，设置画面为填充模式
		TRTCCloudDef.TRTCRenderParams param = new TRTCCloudDef.TRTCRenderParams();
		param.fillMode = TRTCCloudDef.TRTC_VIDEO_RENDER_MODE_FILL;
		param.mirrorType = TRTCCloudDef.TRTC_VIDEO_MIRROR_TYPE_AUTO;
		mTRTCCloud.setLocalRenderParams(param);
		isqianVideo = isqian;
		// 启动本地摄像头的预览（localCameraVideo 是用于渲染本地渲染画面的控件）
		mTRTCCloud.startLocalPreview(isqian, cameraVideo);
		mTRTCCloud.muteLocalVideo(false);
	}
	public static Boolean isqianVideo = false;

	/**
	 * 关闭摄像头
	 * */
	public static void stoplocalVideo() {
		mTRTCCloud.muteLocalVideo(true);
		mTRTCCloud.stopLocalPreview();
	}

	/**
	 * 开启音频
	 * */
	public static void startlocalAudio()
	{
		// 开启麦克风采集，并设置当前场景为：语音模式（高噪声抑制能力、强弱网络抗性）
		//mTRTCCloud.startLocalAudio(TRTCCloudDef.TRTC_AUDIO_QUALITY_SPEECH);
		// 开启麦克风采集，并设置当前场景为：音乐模式（高保真采集、低音质损失，推荐配合专业声卡使用）
		mTRTCCloud.startLocalAudio(TRTCCloudDef.TRTC_AUDIO_QUALITY_MUSIC);
		//开启免提
		setAudioRoute(TRTCCloudDef.TRTC_AUDIO_ROUTE_SPEAKER);
	}

	public static void setAudioRoute(int lx)
	{
		mTRTCCloud.setAudioRoute(lx);
	}

	/**
	 * 设置远程音频的
	 * */
	public static void startRemoteVideo(TXCloudVideoView cameraVideo, String userid)
	{
		// 将远端用户 denny 的主路画面设置为填充模式，并开启左右镜像模式
		TRTCCloudDef.TRTCRenderParams param = new TRTCCloudDef.TRTCRenderParams();
		param.fillMode   	= TRTCCloudDef.TRTC_VIDEO_RENDER_MODE_FILL;
		param.mirrorType   = TRTCCloudDef.TRTC_VIDEO_MIRROR_TYPE_DISABLE;
		mTRTCCloud.setRemoteRenderParams(userid, TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG, param);

		mTRTCCloud.startRemoteView(userid, TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG, cameraVideo);
		//mTRTCCloud.startRemoteView(userid, TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_SMALL, cameraVideo);
	}

}