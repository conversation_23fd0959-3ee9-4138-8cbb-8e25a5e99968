<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">




    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="15dp"
        android:text="根据自己喜欢设置字体大小"
        android:textSize="16dp"
        android:textColor="@color/hui"
        />

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:layout_marginTop="5dp"
        android:background="@color/line2" />
    <RadioGroup
        android:layout_width="match_parent"
        android:background="@color/white"
        android:id="@+id/radioGroup1"
        android:layout_height="match_parent" >


        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton0"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="@color/black"
            android:layout_marginLeft="15dp"
            android:textSize="@dimen/listdp"
            android:checked="true"
            android:textColorHighlight="@color/white"
            android:text="正常" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:layout_marginLeft="15dp"
            android:background="@color/line2" />

        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="@color/black"
            android:layout_marginLeft="15dp"
            android:textColorHighlight="@color/white"
            android:textSize="20dp"
            android:text="中号" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1px"
            android:layout_marginLeft="15dp"
            android:background="@color/line2" />

        <com.view.RockRadioButton
            android:id="@+id/radioGroupButton2"
            android:layout_width="match_parent"
            android:layout_height="@dimen/listheight"
            android:textColor="@color/black"
            android:layout_marginLeft="15dp"
            android:textColorHighlight="@color/white"
            android:textSize="22dp"
            android:text="大号" />

    </RadioGroup>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/line2" />


    <com.view.RockTextView
        android:id="@+id/button"
        android:layout_width="120dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:background="@drawable/btn_zhu"
        android:gravity="center"
        android:text="保 存"
        android:textColor="@color/white"
        android:textSize="@dimen/listdp" />



</LinearLayout>