package com.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Path;
import android.graphics.RectF;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;

import com.baselib.AA;
import com.baselib.CLog;
import com.baselib.Image;
import com.baselib.Jiami;
import com.baselib.RockFile;
import com.baselib.RockHttp;
import com.rock.xinhuoanew.R;

import java.io.File;


public class RockImageView  extends androidx.appcompat.widget.AppCompatImageView {

    private Context mContext;
    private float radius = 0; //180.0全圆角
    private String imagesdPath = "";

    public RockImageView(Context context) {
        super(context);
        mContext = context;
    }

    public RockImageView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mContext = context;
        getAttrs(attrs);
    }

    public RockImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        getAttrs(attrs);
    }

    //获取属性
    protected void getAttrs(AttributeSet attrs)
    {
        TypedArray a = mContext.obtainStyledAttributes(attrs,R.styleable.RockImageView);
        radius = a.getFloat(R.styleable.RockImageView_radius, 0);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if(radius>0) {
            Path path = new Path();
            int w = this.getWidth();
            int h = this.getHeight();
            path.addRoundRect(new RectF(0, 0, w, h), radius, radius, Path.Direction.CW);
            canvas.clipPath(path);
        }
        super.onDraw(canvas);
    }



    /**
     * 设置图片地址
     * */
    public void setPath(String url)
    {
        String cappath       = RockFile.getCachedir(mContext, "rockimageview");
        String savepath      = ""+cappath+"/rockimg"+ Jiami.md5(url)+"."+RockFile.getExt(url)+"";
        this.imagesdPath     = savepath;
        File file = new File(savepath);
        if(file.exists()) {
            setImageURI(Uri.fromFile(file));
        }else {
            RockHttp.down(url,savepath, new Handler(){
                @Override
                public void handleMessage(Message msg) {
                    if(msg.arg1==AA.HTTPB_SUCCESS) {
                        setImageURI(Uri.fromFile(new File(savepath)));
                    }
                }
            }, 1);
        }
    }

    /**
     * 获取路径
     * */
    public String getsdPath()
    {
        return this.imagesdPath;
    }
}
