
package com.rock.xinhu;


import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.TextView;

import com.dialog.Dialog;
import com.dialog.DialogMsg;
import com.baselib.AR;
import com.baselib.CallBack;
import com.baselib.Rock;


public class XinhuBase_about extends XinhuBase {


    public XinhuBase_about(Context context, View view) {
        super(context, view);
    }

    public void initBase()
    {
        TextView tv = mView.findViewById(AR.getID("version"));
        tv.setText("Version "+ Rock.VERSION+"");

        if(!Rock.isEmpt(Rock.nowtheme)) {
            tv = mView.findViewById(AR.getID("btn03"));
            tv.setTextColor(Color.parseColor(Rock.nowtheme));
            tv = mView.findViewById(AR.getID("btn04"));
            tv.setTextColor(Color.parseColor(Rock.nowtheme));
        }
    }

    public void setOnClickListener(View.OnClickListener cick)
    {
        mView.findViewById(AR.getID("btn01")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn02")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn03")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn04")).setOnClickListener(cick);
        mView.findViewById(AR.getID("btn06")).setOnClickListener(cick);
    }

    public void onViewClick(int id)
    {
        if(id==AR.getID("btn01")){
            Dialog.alert(mContext, "感谢您的使用，我们会越做越好的。");
        }
        if(id==AR.getID("btn02")){
            Dialog.prompt(mContext, "请输入您想说的话：", "", "", new CallBack(){
                @Override
                public void back() {
                    String txt = Dialog.getText();
                    if(!Rock.isEmpt(txt)){
                        DialogMsg.success(mContext, "感谢您的支持！");
                    }
                }
            }, null);
        }
        if(id==AR.getID("btn03")){
            mObj.openXinhu("用户协议","aboutxieyi");
        }
        if(id==AR.getID("btn04")){
            mObj.openXinhu("隐私政策","aboutyinsi");
        }
        if(id==AR.getID("btn06")){
            Rock.openAppSettings(mContext);
        }
    }

}