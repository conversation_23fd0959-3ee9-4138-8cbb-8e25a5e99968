package com.rock.xinhuoanew;

import android.annotation.SuppressLint;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.baselib.AA;
import com.baselib.CDate;
import com.baselib.CLog;
import com.baselib.Image;
import com.baselib.Jiami;
import com.baselib.Rock;
import com.baselib.RockFile;
import com.baselib.RockHttp;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;


public class ImageViewActivity extends ARockActivity {


    protected  ImageView  imgView;
    protected  ProgressBar  progressBar;
    protected  TextView  downText;
    protected  String  imgurl,filename,filepath,filesizecn;

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_imageview);
        findViewById(R.id.back).setOnClickListener(OnViewClickListener);
        findViewById(R.id.downbtn).setOnClickListener(OnViewClickListener);
        findViewById(R.id.down).setOnClickListener(OnViewClickListener);

         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
         }

        Bundle bundle       = this.getIntent().getExtras();
        String name         = bundle.getString("name");
        imgurl              = bundle.getString("url");
        imgView =(ImageView) findViewById(R.id.ImageControl);

        progressBar         = (ProgressBar)findViewById(R.id.progressBar);

        downText = (TextView)findViewById(R.id.downbtn);

        String ext  = RockFile.getExt(imgurl);
        filename    = RockFile.getFilename(imgurl.replace("_s.","."));

        imgView.setOnTouchListener(new View.OnTouchListener(){
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                return yybtnonTouch(view, motionEvent);
            }
        });

        filesizecn = name;
        if(!Rock.isEmpt(name)){
            downText.setText("原图"+name+",下载查看原图");
            filepath = "";
        }else{
            downText.setVisibility(View.GONE);
        }
        downimage(imgurl, myhandler,0);
        overridePendingTransition(R.anim.main_zoom,R.anim.main_out);
    }

    protected View.OnClickListener OnViewClickListener = new View.OnClickListener() {
        public void onClick(View v) {
            onViewClick(v);
        }
    };

    protected void onViewClick(View v) {
        int id = v.getId();
        if(id == R.id.back){
            exitView();
        }
        if(id == R.id.down){
            downfile(imgurl);
        }
        if(id == R.id.downbtn){
            downText.setText("下载原图中...");
            downimage(imgurl.replace("_s.","."), myhandler,1);
        }
    }

    private void exitView()
    {
        finish();
        overridePendingTransition(R.anim.main_in,R.anim.main_out);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            exitView();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void downfile(String url)
    {
        url = url.replace("_s.",".");
        RockHttp.download(this, url, filename, null);
    }

    private void showimgsss(Bitmap bitmap,int gcode)
    {
        imgView.setVisibility(View.VISIBLE);
        progressBar.setVisibility(View.GONE);
        imgView.setImageBitmap(bitmap);
        juzhong(bitmap);
        if(gcode==1 && !Rock.isEmpt(filesizecn)){
            downText.setText("已显示原图("+filesizecn+")");
        }
    }
    private void onhandleMessage(Message msg)
    {
        if(msg.arg1==AA.HTTPB_SUCCESS){
            String retsult  = "";
            Bundle mBundle  = msg.getData();
            if(mBundle!=null)retsult   = mBundle.get("result").toString();
            showPathimg(retsult, msg.what);
        }else{
            Rock.Toast(this,"无法读取图片");
        }
    }

    //下载图片
    private void downimage(String url, Handler ssHandler, int code)
    {
        progressBar.setVisibility(View.VISIBLE);
        String cappath      = RockFile.getCachedir(this, "imageview");
        String fileext      = RockFile.getExt(url);
        String spath        = ""+cappath+"/imageview"+ Jiami.md5(url)+"."+fileext+"";

        String nurl         = url.replace("_s.",".");
        String npath        = ""+cappath+"/imageview"+ Jiami.md5(nurl)+"."+fileext+"";
        if(new File(npath).exists()){
            showPathimg(npath, 1);
            return;
        }
        if(new File(spath).exists()) {
            showPathimg(spath, code);
            return;
        }
        RockHttp.down(url, spath, ssHandler, code);
    }

    //根据路径显示图片
    private void showPathimg(String npath, int gcode){
        BitmapFactory.Options newOpts = new BitmapFactory.Options();
        Bitmap bitmap = BitmapFactory.decodeFile(npath, newOpts);
        showimgsss(bitmap, gcode);
    }

    protected Handler myhandler = new Handler(){
        @Override
        public void handleMessage(Message msg) {
            onhandleMessage(msg);
        }
    };


    private void juzhong(Bitmap bitmap)
    {
        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        int width   = displayMetrics.widthPixels;
        int height  = displayMetrics.heightPixels;
        Matrix jzatrix = new Matrix();
        int zw = imgView.getWidth(),zh = imgView.getHeight();
        int nw = bitmap.getWidth(),nh  = bitmap.getHeight();
        int bl = 100;
       // CLog.debug(""+zw+","+nw+","+width+":"+zh+","+nh+","+height+"");
        if(zw==0){
            zw = width;
            zh = height;
        }
        if(nw>zw && zw>300){
            bl = (int)(zw*100/nw);
            nw = zw;
            nh = (int)(bl*0.01*nh);
        }
        float dx = (float)((zw-nw)*0.5);
        float dy = (float)((zh-nh)*0.5);
        if(nw<300) {
            dx= dx/1.5F;
            dy= dy/1.5F;
        }
        if(dx<0){
            dx = dy = 0;
        }
        jzatrix.postTranslate(dx,dy);
        if(nw<300){
            jzatrix.postScale(3,3,dx,dy);
        }
        //缩放比例
        if(bl<90){
            float bis = (float)(bl*0.01);
            jzatrix.postScale(bis,bis,dx,dy);
        }
        imgView.setImageMatrix(jzatrix);
        endMatrix = jzatrix;
    }

    private int MODE;//当前状态
    public static final int MODE_NONE = 0;//无操作
    public static final int MODE_DRAG = 1;//单指操作
    public static final int MODE_SCALE = 2;//双指操作

    private Matrix startMatrix = new Matrix();//初始矩阵
    private Matrix endMatrix = new Matrix();//变化后的矩阵
    private PointF startPointF = new PointF();//初始坐标
    private float distance;//初始距离
    private float scaleMultiple;//缩放倍数

    public boolean yybtnonTouch(View v, MotionEvent event) {
        switch (event.getAction()&event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN://单指触碰

                //起始矩阵先获取ImageView的当前状态
                startMatrix.set(imgView.getImageMatrix());
                //获取起始坐标
                startPointF.set(event.getX(), event.getY());
                //此时状态是单指操作
                MODE = MODE_DRAG;

                break;
            case MotionEvent.ACTION_POINTER_DOWN://双指触碰
                //最后的状态传给起始状态
                startMatrix.set(endMatrix);
                //获取距离
                distance = getDistance(event);
                //状态改为双指操作
                MODE = MODE_SCALE;

                break;
            case MotionEvent.ACTION_MOVE://滑动（单+双）
                if (MODE == MODE_DRAG) {//单指滑动时
                    //获取初始矩阵
                    endMatrix.set(startMatrix);
                    //向矩阵传入位移距离
                    endMatrix.postTranslate(event.getX() - startPointF.x, event.getY() - startPointF.y);
                } else if (MODE == MODE_SCALE) {//双指滑动时
                    //计算缩放倍数
                    scaleMultiple = getDistance(event)/distance;
                    //获取初始矩阵
                    endMatrix.set(startMatrix);
                    //向矩阵传入缩放倍数
                    endMatrix.postScale(scaleMultiple, scaleMultiple,startPointF.x,startPointF.y);
                }
                break;
            case MotionEvent.ACTION_UP://单指离开
            case MotionEvent.ACTION_POINTER_UP://双指离开
                //手指离开后，重置状态
                MODE = MODE_NONE;
                break;
        }
        //事件结束后，把矩阵的变化同步到ImageView上
        imgView.setImageMatrix(endMatrix);
        return true;
    }

    //获取距离
    private static float getDistance(MotionEvent event) {//获取两点间距离
        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float) Math.sqrt(x * x + y * y);
    }
}
