
/**
*	插件：模式窗口弹出提示
*	开发者：雨中磐石
*	地址：www.rockoa.com
*	日期：2023-07-17
*/

js.modal = {
	init:function(){
		
	},
	tanbodyindex:90,
	main:function(cans){
		var H	= (document.body.scrollHeight<winHb())?winHb()-5:document.body.scrollHeight;
		var W	= document.documentElement.scrollWidth+document.body.scrollLeft;
		var can	= js.applyIf(cans,{html:'',action:'a'+js.getrand()+'',btn:[],bodystyle:'',title:'系统提示'});
		var act = cans.action,w=cans.width,h=cans.height;
		this.tanbodyindex++;
		var l=(winWb()-w)*0.5,t=(winHb()-h-20)*0.5;
		var s = '',mid	= ''+act+'_main',i,d;
		if(w>winWb())w=winWb()-20;
		var move = 'onmousedown="js.modal.move(\''+mid+'\')"';
		if(ismobile==1)move='ontouchstart="js.modal.movewe(\''+mid+'\', this, event)"';
		var s = '<div id="'+mid+'" style="position:fixed;background-color:white;left:'+l+'px;width:'+w+'px;top:'+t+'px;box-shadow:0px 0px 10px rgba(0,0,0,0.3);border-radius:6px"><div style="background:var(--main-bgcolor);border-radius:5px;">';
		s+='	<div style="-moz-user-select:none;-webkit-user-select:none;user-select:none;border-bottom:var(--border);">';
		s+='		<table border="0" width="100%" style="background:none" cellspacing="0" cellpadding="0"><tr>';
		s+='			<td height="50" style="font-weight:bold;cursor:move;padding-left:10px" width="100%" '+move+' id="'+act+'_title" class="zhu">'+can.title+'</td>';
		s+='			<td><div  id="'+act+'_spancancel1" style="padding:0px 8px;height:50px;line-height:45px;overflow:hidden;cursor:pointer;" onclick="js.modal.close(\''+act+'\')">✖</div></td>';
		s+='		</tr></table>';
		s+='	</div>';
		s+='	<div id="'+act+'_body" style="'+can.bodystyle+';">'+can.html+'</div>';
		s+='	<div id="'+act+'_bbar" style="overflow:hidden;padding:12px 10px;background:rgba(0,0,0,0.05);border-radius:0px 0px 5px 5px;white-space:nowrap;display:flex;align-items:center;" align="right"><div style="flex:1;overflow:hidden" id="msgview_'+act+'"></div>';
		for(i=0; i<can.btn.length; i++){
			d = can.btn[i];
			if(!d.bgcolor)d.bgcolor='';
			if(!d.cls)d.cls='';
			s+='<button type="button" oi="'+i+'" style="padding:8px 15px;margin-left:10px;background:'+d.bgcolor+'" id="'+act+'_btn'+i+'" class="webbtn '+d.cls+'">'+d.text+'</button>';
		}
		s+='		<button type="button" id="'+act+'_spancancel" onclick="js.modal.close(\''+act+'\')" style="padding:8px 15px;background:rgba(0,0,0,0.5);margin-left:10px" class="webbtn">取消</button>';
		s+='		';
		s+='	</div>';
		s+='</div></div>';
		var str = '<div id="amain_'+act+'" tanbodynew="'+act+'" style="position:absolute;height:'+H+'px;width:'+W+'px;background:rgba(0,0,0,0.3);z-index:'+this.tanbodyindex+';left:0px;top:0px">'+s+'</div>';
		$('body').append(str);
		
		if(can.closed=='none'){
			$('#'+act+'_spancancel').remove();
			$('#'+act+'_spancancel1').remove();
		}
		if(can.bbar=='none'){
			$('#'+act+'_bbar').remove();
			$('#'+mid+'').append('<div style="height:5px;overflow:hidden;border-radius:0px 0px 5px 5px"></div>');
		}
		this.resize(act);
		return can;
	},
	resize:function(act){
		var mid	= ''+act+'_main';
		var o1  = $('#'+mid+'');
		var h1 = o1.height();
		var w1 = o1.width();	
		var l=(winWb()-w1)*0.5,t=(winHb()-h1)*0.5;if(t<0)t=5;
		o1.css({'left':''+l+'px','top':''+t+'px'});
	},
	alert:function(cans, fun){
		this.alertmain(cans, fun, 0);
	},
	alertmain:function(cans, fun, lx){
		var can = {
			action:'confirm',
			width:350,
			height:200,
			closed:'none',
			inputmsg:'',
			type:'textarea',
			value:'',
			onbefore:function(){},
			btn:[{text:'确定'}]
		};
		if(lx==1 || lx==2)can.btn.push({text:'取消',bgcolor:'gray'});
		if(typeof(cans)!='string'){
			for(var i in cans)can[i]=cans[i];
			if(cans.btntext)can.btn[0].text = cans.btntext;
		}else{
			can.msg = cans;
		}
		if(lx==2){
			var str = '<div style="padding:10px;" align="center">';
			str+='<div align="left" style="padding-left:10px">'+can.msg+'</div><div style="display:flex">';
			if(can.type=='number' || can.type=='text'){
				str+='<input class="input" type="'+can.type+'" autocomplete="off" id="confirm_input" style="flex:1;border-radius:5px" value="'+can.value+'" placeholder="'+can.inputmsg+'" />';
			}else{
				str+='<textarea class="input" id="confirm_input" autocomplete="off" style="flex:1;height:60px;border-radius:5px" placeholder="'+can.inputmsg+'">'+can.value+'</textarea>';
			}
			str+='</div></div>';
			can.html = str;
		}else{
			can.html = '<div style="padding:20px;line-height:30px">'+can.msg+'</div>';
		}
		this.main(can);
		function backl(jg){
			var val=$('#confirm_input').val();
			if(val==null)val='';
			var bsv = can.onbefore(jg,val);
			if(bsv){js.show.error(bsv);return;}
			if(typeof(fun)=='function'){
				var cbo = fun(jg, val);
				if(cbo)return false;
			}
			js.modal.close(can.action);
			return false;
		}
		$('#'+can.action+'_btn0').click(function(){backl('yes')});
		$('#'+can.action+'_btn1').click(function(){backl('no')});
	},
	confirm:function(cans, fun){
		this.alertmain(cans, fun, 1);
	},
	prompt:function(cans,fun){
		this.alertmain(cans, fun, 2);
	},
	input:function(cans,fun){
		this.alertmain(cans, fun, 2);
	},
	move:function(id,rl){
		var _left=0,_top=0,_x=0,_right=0,_y=0;
		var obj	= id;if(!rl)rl='left';
		if(typeof(id)=='string')obj=get(id);
		var _Down=function(e){
			try{
				var s='<div id="divmovetemp" style="filter:Alpha(Opacity=0);opacity:0;z-index:99999;width:100%;height:100%;position:absolute;background-color:#000000;left:0px;top:0px;cursor:move"></div>';
				$('body').prepend(s);
				_x = e.clientX;_y = e.clientY;_left=parseInt(obj.style.left);_top=parseInt(obj.style.top);_right=parseInt(obj.style.right);
				document.onselectstart=function(){return false}
			}catch(e1){}		
		}
		var _Move=function(e){
			try{
				var c=get('divmovetemp').innerHTML;
				var x = e.clientX-_x,y = e.clientY-_y;
				if(rl=='left')obj.style.left=_left+x+'px';
				if(rl=='right')obj.style.right=_right-x+'px';
				obj.style.top=_top+y+'px';
			}catch(e1){_Down(e)}
		}
		var _Up=function(){
			document.onmousemove='';
			document.onselectstart='';
			$('#divmovetemp').remove();	
		}
		document.onmousemove=_Move;
		document.onmouseup=_Up;
	},
	movewe:function(id, o, ev){
		var o1    = get(id);
		var obj   = $(o1);
		var ismov = obj.attr('movebo');
		this.devcan    = [0,0,0,0];
		this.devcan[0] = ev.touches[0].clientX;
		this.devcan[1] = ev.touches[0].clientY;
		
		this.devcan[2] = parseInt(o1.style.left);
		this.devcan[3] = parseInt(o1.style.top);
		if(ismov!='true'){
			o.addEventListener('touchmove',function(e){
				e.preventDefault();
				var _x = e.touches[0].clientX-js.modal.devcan[0],_y=e.touches[0].clientY-js.modal.devcan[1];
				obj.css({left:''+(js.modal.devcan[2]+_x)+'px',top:''+(js.modal.devcan[3]+_y)+'px'});
			});  
		}
		obj.attr('movebo','true');
	},
	close:function(act){
		if(!act)act='confirm';
		$('#amain_'+act+'').remove();
	}
}

js.tanbody = function(act,bt,w,h,can){
	var cans = js.apply({
		action:act,
		title:bt,
		width:w,
		height:h
	}, can);
	js.modal.main(cans);
}

js.tanclose = function(act){
	js.modal.close(act);
}

js.prompt = function(bt,fun, msg, val){
	this.modal.prompt({
		title:bt,
		msg:msg,
		value:val
	},fun);
}

js.alert = function(msg,fun){
	this.modal.alert({msg:msg}, fun);
}

js.confirm = function(msg,fun){
	this.modal.confirm(msg, fun);
}