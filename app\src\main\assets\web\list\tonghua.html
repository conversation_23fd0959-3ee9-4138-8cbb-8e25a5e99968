<script>
/**
*	这个是音视频通话使用的
*	开发者：雨中磐石
*	官网：www.rockoa.com
*	软件：信呼OA
*	更新时间：2021-10-08
*/

$(document).ready(function(){
	{params}
	
	var agoraRtc = false;
	var c = {
		thcan:{},
		isjietong:false,
		ismybool:false,
		callshu:30, //呼叫多少秒无人接听自动取消
		init:function(){
			this.typearr = ['语音','视频'];
			this.type = 1; 
			this.id   = parseInt(params.id);
			this.name   = parseInt(params.name);
			this.iscall = params.iscall;
			var face = xcy.getface(js.getoption('tonghuaface'));
			get('tonghuaface').src=face;
			get('tonghuafaces').src=face;
			this.topheight = $('#header_line').height();
			
			//接收端
			if(!this.iscall){
				this.type = params.type;
				this.channel = params.channel;
				this.thcan.appid = params.appid;
				this.jieshoucall();
			}else{
				$('#calldiv').show();
			}
		},
		start:function(){
			xcy.showtheme('#000000');
			$('#mainbody').css('background','#000000');
		},
		hujiao:function(o1,lx){
			if(this.hujiaobo)return;
			js.loading('请求中...');
			this.hujiaobo = true;
			this.type 	  = lx;
			js.ajax('tonghua|thinit',{id:this.id,type:lx},function(ret){
				c.hujiaoback(ret);
			}, 'get', function(str){
				js.msgerror(str);
				c.stateupdate(str);
				c.hujiaobo=false;
			});
		},
		hujiaoback:function(ret){
			this.thcan = ret.data;
			this.channel = this.thcan.channel;
			$('#backbtn').hide();
			$('#hujiaobtn').remove();
			$('#cancelbtn').show();
			this.initagoraRtc();
			xcy.tonghuareg();
			this.callstartplay();
			this.callstrimes = js.now('time');
			this.callmiashue();
		},
		callmiashue:function(){
			clearTimeout(this.callmiashuetime);
			var sj = parseInt((js.now('time')-this.callstrimes)*0.001);
			if(sj>this.callshu){
				this.cancelhus(5);
				return;
			}
			if(sj%2==0 && sj>0 && !this.ztpandbool){
				this.ztpandbool = true;
				js.ajax('tonghua|state',{channel:this.channel}, function(ret){
					var zt = ret.data.state;
					c.ztpandbool = false;
					if(!c.calljutong && (zt=='tongyi' || zt=='jujue'))c.callbackss(zt);
				},'get', function(st1){
					c.ztpandbool = false;
					js.msg();
				});
			}
			this.stateupdate(''+this.typearr[this.type]+'通话呼叫中已呼叫'+sj+'秒...');
			this.callmiashuetime = setTimeout(function(){c.callmiashue()},1000);
		},
		callstartplay:function(){
			api.startPlay({
				path: 'widget://res/sound/call.mp3'
			}, function(ret, err) {
				if (ret) {
					c.callstartplay();
				}
			});
		},
		callstopplay:function(){
			api.stopPlay();
		},
		cancalpayls:function(){
			api.startPlay({
				path: 'widget://res/sound/gua.mp3'
			}, function(ret, err) {
				
			});
		},
		
		//接通后显示视频
		localvadio:function(){
			if(this.localvadiobool)return;
			this.localvadiobool = true;
			this.isjietong = true;
			this.showtoast(''+this.typearr[this.type]+'已接通');
			this.stateupdate(''+this.typearr[this.type]+'通话中(00:00)');
			$('#backbtn').hide();
			this.starttime  = js.now('time');
			this.channel	= this.thcan.channel;
			this.showmian();
			agoraRtc.joinChannel({
				token:this.thcan.token,
				channel:this.thcan.channel,
				uid:this.thcan.uid
			}, function(ret) {
				
			});
			if(this.type==1){
				$('#shipindiv').show();
				$('#yuyindiv').hide();
			}
		},
		showmian:function(){
			var sj = parseInt((js.now('time')-this.starttime)*0.001);
			this.tonghuasj = sj;
			var fz = 0;
			if(sj>59)fz = parseInt(sj/60);
			var ms = sj-fz*60;
			this.stateupdate(''+this.typearr[this.type]+'通话中('+xy10(fz)+':'+xy10(ms)+')');
			if(sj>0 && sj%2==0 && !this.showmiaobool){
				this.showmiaobool = true;
				js.ajax('tonghua|stateth',{channel:this.channel}, function(ret){
					var zt = ret.data.state;
					c.showmiaobool = false;
					if(zt=='jiesu')c.callbackss(zt);
				},'get', function(st1){
					c.showmiaobool = false;
					js.msg();
				});
			}
			this.showmiantime = setTimeout(function(){c.showmian()},1000);
		},
		localvadioshow:function(){
			$('#calldiv').remove();
			$('#canceldiv').show();
			
			if(this.type==1){
				agoraRtc.enableVideo(function(ret) {
					//js.msgok('打开视频并且开始推流：'+JSON.stringify(ret));
				});
				this.start();
				var mehs = winHb()-60-120-this.topheight;
				agoraRtc.setupRemoteVideo({
					uid:this.id,
					rect:{ x:0, y:this.topheight+60, w:winWb(), h:mehs },
					fixed:false,
					renderMode:1
				}, function(ret) {
					
				});
				agoraRtc.setupLocalVideo({
					rect:{ x:winWb()-120, y:c.topheight, w:120, h:200 },
					fixed:false,
					renderMode:1
				}, function(ret) {
					
				});
			}else{
				agoraRtc.enableAudio(function(ret) {
					//js.msgok('打开音频推流：'+JSON.stringify(ret));
				});
			}
		},
		initagoraRtc:function(){
			agoraRtc = api.require('agoraRtc');
			agoraRtc.init({appId:this.thcan.appid});
			
			agoraRtc.joinChannelSuccessListener(function(ret) {
				//js.msgok('加入频道成功：'+JSON.stringify(ret));
				c.localvadioshow();
			});
			
			agoraRtc.errorListener(function(ret) {
				if(ret.errorCode!=18)js.msgerror('Error：'+JSON.stringify(ret));
			});
			
			agoraRtc.remoteUserJoinedListener(function(ret) {
				//js.msgok('有人来：'+JSON.stringify(ret)); //说明接通了
			});
			
			agoraRtc.leaveChannelListener(function(ret) {
				//js.msgok('退出频道成功：'+JSON.stringify(ret));
			});
			
			agoraRtc.remoteUserOfflineListener(function(ret) {
				//js.msgok('有人离开频道：'+JSON.stringify(ret));
				c.guaduanok(false);
			});
			//先离开频道
			agoraRtc.leaveChannel(function(ret){});
		},
		
		guaduan:function(){
			this.guaduanok(true);
		},
		guaduanok:function(bo){
			if(this.guaduanbool)return;
			this.guaduanbool = true;
			if(bo){
				this.showtoast('挂断中...');
				js.ajax('tonghua|jiesu',{channel:this.thcan.channel,toid:this.id}, function(ret){
					c.guaduanokok();
				},'get', function(str){
					c.guaduanokok();
				});
			}else{
				this.guaduanokok();
			}
		},
		guaduanokok:function(){
			clearTimeout(this.showmiantime);
			agoraRtc.leaveChannel(function(ret){});
			agoraRtc.destroy();
			this.stateupdate('通话已结束', true);
			$('#canceldiv').remove();
			this.cancalpayls();
			this.closewin();
		},
		closewin:function(){
			$('#backbtn').show();
			setTimeout('xcy.back()',2000);
		},
		showtoast:function(str){
			api.toast({msg: str,duration: 2000});
		},
		stateupdate(str, bo){
			$('#wiatling').html(str);
			$('#wiatlings').html(str);
			if(bo)this.showtoast(str);
		},
		qiehysq:function(){
			this.showtoast('扬声器默认打开，无法关闭');
		},
		cancelhu:function(){
			this.cancelhus(3);
		},
		cancelhus:function(zt){
			$('#cancelbtn').hide();
			clearTimeout(this.callmiashuetime);
			var ss1='';if(zt==5)ss1='无人接听';
			js.loading(''+ss1+'取消呼叫中...');
			this.stateupdate(''+ss1+'取消呼叫中...');
			this.callstopplay();
			js.ajax('tonghua|cancel',{channel:this.thcan.channel,state:zt},function(ret){
				agoraRtc.destroy();
				c.stateupdate(''+ss1+'已取消呼叫', true);
				c.closewin();
			}, 'get', function(str){
				$('#cancelbtn').show();
				js.msgerror(str);
				c.stateupdate(str);
			});
		},
		
		initApp:function(){
			var cans = xcy.pageParams;
			if(!cans.iscall){
				this.type = cans.type;
				this.name = cans.name;
				var str = this.typearr[this.type];
				var notify = {title:''+str+'通话',content: ''+this.name+'邀请您'+str+'通话...'};
				api.notification({
					//sound:'default',
					vibrate:[100, 500, 200],
					notify: notify
				});
				this.payerstart();
			}
		},
		
		payerstart:function(){
			api.startPlay({
				path: 'widget://res/sound/mi.mp3'
			}, function(ret, err) {
				if (ret) {
					c.payerstart();
				}
			});
		},
		payerend:function(){
			api.stopPlay();
		},
		
		//接电话端处理初始化
		jieshoucall:function(){
			$('#backbtn').hide();
			$('#anjiandiv').show();
			this.sytime = xcy.pageParams.sytime;
			js.ajax('tonghua|receopen',{channel:this.channel}, function(ret){
				var das = ret.data;
				if(das.sytime){
					c.sytime = das.sytime;
					c.jieshoutimes(-1);
				}
			});
			this.jieshoutimes(-1);
		},
		jieshoutimes:function(i1){
			clearTimeout(this.jieshoutimesa);
			var str = this.typearr[this.type];
			var sys = this.callshu - this.sytime - i1;
			if(sys<0){
				this.stateupdate('超时自动取消', true);
				this.jiedianha(false,3);
			}else{
				if(i1%2==0 && i1>0 && !this.ztpandbool){
					this.ztpandbool = true;
					js.ajax('tonghua|state',{channel:this.channel}, function(ret){
						var zt = ret.data.state;
						c.ztpandbool = false;
						if(zt=='cancel' || zt=='tongyi' || zt=='jujue')c.callbackss(zt);
					},'get', function(st1){
						c.ztpandbool = false;
						js.msg();
					});
				}
				this.stateupdate('邀请与您'+str+'通话('+sys+')...');
				this.jieshoutimesa = setTimeout(function(){c.jieshoutimes(i1+1)},1000);
			}
		},
		jiedianha:function(o1,lx){
			clearTimeout(this.jieshoutimesa);
			this.ismybool = true;
			$('#anjiandiv').hide();
			api.cancelNotification({id: -1});
			this.payerend();
			if(lx==1){
				this.stateupdate('接通中...');
				this.initagoraRtc();
				js.ajax('tonghua|jietong',{channel:this.channel,state:1},function(ret){
					c.jietongback(ret);
				}, 'get', function(str){
					js.msgerror(str);
					c.stateupdate(str);
					$('#anjiandiv').show();
				});
			}
			if(lx==2){
				this.stateupdate('拒绝中...');
				js.ajax('tonghua|jie',{channel:this.channel,state:2},function(ret){
					var ms2 = '已拒绝通话';
					if(ret.data.satype)ms2 = ret.data.satype;
					c.jiequxian(ms2);
				},'get', function(str){
					js.msgerror(str);
					c.stateupdate(str);
					$('#anjiandiv').show();
				});
			}
			if(lx==3){
				this.closewin();
			}
		},
		jiequxian:function(s1){
			this.stateupdate(s1, true);
			this.closewin();
		},
		jietongback:function(ret){
			this.thcan = ret.data;
			if(ret.data.satype){
				this.jiequxian(ret.data.satype);
			}else{
				this.stateupdate(''+this.typearr[this.type]+'通话中...');
				this.localvadio();//被叫端接通
			}
		},
		callbackss:function(lx){
			this.callstopplay();
			clearTimeout(this.callmiashuetime);
			clearTimeout(this.jieshoutimesa);
			if(lx=='jujue'){
				this.calljutong = true;
				if(this.iscall){
					$('#cancelbtn').remove();;
					this.stateupdate('对方拒绝通话', true);
					this.closewin();
				}
				if(!this.iscall && !this.ismybool){
					this.stateupdate('已在另端拒绝', true);
					$('#anjiandiv').remove();
					this.closewin();
				}
			}
			if(lx=='tongyi'){
				this.calljutong = true;
				if(this.iscall){
					this.stateupdate('接通中...');
					this.localvadio(); //主叫端接通
				}
				if(!this.iscall && !this.ismybool){
					this.stateupdate('已在另端接通', true);
					$('#anjiandiv').remove();
					this.closewin();
				}
			}
			
			//----以下是接收端处理----
			//呼叫已经取消
			if(lx=='cancel' && get('anjiandiv')){
				api.cancelNotification({id: -1});
				$('#anjiandiv').remove();
				clearTimeout(this.jieshoutimesa);
				this.stateupdate('对方已取消', true);
				this.closewin();
			}
			//对方挂电话
			if(lx=='jiesu'){
				this.guaduanok(false);
			}
		}
	}
	c.init();
	js.initbtn(c);
	
	xcy.tonghuamessdata=function(da){
		c.callbackss(da.calltype);
	}
	
	xcy.initApp=function(){
		initAppbool = true;
		xcy.tonghuareg();
		c.initApp();
	}
});
</script>
<div align="left" style="padding:0px 10px;height:60px;overflow:hidden;display:none" id="shipindiv">
	<table style="margin-top:10px"><tr>
	<td><img id="tonghuafaces" src="images/noface.png" width="40px" height="40px" style="border-radius:50%"></td>
	<td width="10"></td>
	<td><div id="wiatlings" style="color:#888888;font-size:14px">等待呼叫</div></td>
	</tr></table>
</div>
<div align="center" id="yuyindiv">
	<div style="margin-top:50px"><img id="tonghuaface" src="images/noface.png" width="100px" height="100px" style="border-radius:50%"></div>
	<div id="wiatling" style="color:#888888;font-size:14px">等待呼叫</div>
</div>


<div align="center" id="calldiv" style="position:fixed;bottom:50px;width:100%;display:none">
	<div id="hujiaobtn">
		<div style="color:#888888;font-size:12px">请选择通话类型</div>
		<table><tr>
		<td align="center">
			<div class="webbtn" clickevt="hujiao,0" align="center" style="width:90px;height:90px;background:#ff6600;color:white;line-height:90px;border-radius:50%;font-size:16px;margin-top:10px;padding:0"><i class="icon-volume-up"></i> 语音</div>
		</td>
		<td width="50"></td>
		<td align="center">
		<div class="webbtn"  clickevt="hujiao,1" align="center"  style="width:90px;height:90px;background:#339933;color:white;line-height:90px;border-radius:50%;font-size:16px;padding:0;margin-top:20px"><i class="icon-facetime-video"></i> 视频</div>
		</td>
		</tr></table>
	</div>
	<div id="cancelbtn" style="display:none">
		<div class="webbtn" clickevt="cancelhu" style="width:80px;height:80px;background:#FF6666;color:white;line-height:80px;border-radius:50%;font-size:40px;padding:0;margin-top:20px;transform:rotate(135deg);"><i class="icon-phone"></i></div>
		<div style="color:#888888;font-size:12px;margin-top:5px">取消</div>
	</div>
</div>
<div align="center" id="canceldiv" style="position:fixed;bottom:0px;width:100%;display:none;height:120px;overflow:hidden;">
	<table style="margin-top:10px"><tr>
	<td align="center">
		<div class="webbtn" clickevt="guaduan" style="width:80px;height:80px;background:#FF6666;color:white;line-height:80px;border-radius:50%;font-size:40px;padding:0;margin-top:20px;margin:0;transform:rotate(135deg);"><i class="icon-phone"></i></div>
		<div style="color:#888888;font-size:12px;margin-top:5px">挂断</div>
	</td>
	<td width="30"></td>
	<td align="center">
		<div class="webbtn" clickevt="qiehysq" style="width:80px;height:80px;background:white;color:#000000;line-height:80px;border-radius:50%;font-size:40px;padding:0;margin-top:20px;margin:0"><i class="icon-volume-up"></i></div>
		<div style="color:#888888;font-size:12px;margin-top:5px">扬声器已开</div>
	</td>
	
	</tr></table>
</div>

<div align="center" id="anjiandiv" style="position:fixed;bottom:50px;width:100%;display:none">
	<div>
		<table><tr>
		<td align="center">
			<div class="webbtn" clickevt="jiedianha,2" style="width:80px;height:80px;background:#FF6666;color:white;line-height:80px;border-radius:50%;font-size:40px;padding:0;margin-top:20px;transform:rotate(135deg);"><i class="icon-phone"></i></div>
			<div style="color:#888888;font-size:12px;margin-top:5px">拒绝</div>
		</td>
		<td width="50"></td>
		<td align="center">
			<div class="webbtn" clickevt="jiedianha,1" style="width:80px;height:80px;background:#339933;color:white;line-height:80px;border-radius:50%;font-size:40px;padding:0;margin-top:20px"><i class="icon-phone"></i></div>
			<div style="color:#888888;font-size:12px;margin-top:5px">接听</div>
		</td>
		</tr></table>
	</div>
	
</div>