package com.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.Color;
import android.util.AttributeSet;

import com.baselib.CLog;
import com.baselib.Rock;


public class RockRadioButton extends androidx.appcompat.widget.AppCompatRadioButton {

    public RockRadioButton(Context context) {
        super(context);
    }

    public RockRadioButton(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public RockRadioButton(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int hint = this.getHighlightColor();
        if(hint!=Color.WHITE){
            float size = 18;
            if(Rock.fontsize==1)size = 20;
            if(Rock.fontsize==2)size = 22;
            this.setTextSize(size);
        }
        this.setButtonTintList(ColorStateList.valueOf(Color.parseColor(Rock.defTheme)));
    }
}