
package com.rock.xinhu;


import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dialog.Dialog;
import com.dialog.DialogLoad;
import com.dialog.DialogMsg;
import com.baselib.AA;
import com.baselib.AR;
import com.baselib.CLog;
import com.baselib.CallBack;
import com.baselib.Json;
import com.baselib.Rock;
import com.baselib.RockHttp;
import com.view.RockImageView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class XinhuBase_loginserver extends XinhuBase {

    private LinearLayout layout = null;
    private List<Map<String, String>> loginlist = null;
    private String apiurl;

    public XinhuBase_loginserver(Context context, View view) {
        super(context, view);
    }

    public void initBase()
    {
        ImageView iv = mObj.findViewById(AR.getID("moreimg"));
        iv.setImageDrawable(mContext.getResources().getDrawable(AR.getmipmapID("add")));
        mObj.showMore();

        layout = (LinearLayout) mView.findViewById(AR.getID("listshow"));
        firstyz(Rock.getApiUrl(), 1);
        showlist();
    }

    public String getYanurl(String url)
    {
        String str = url+"api.php?m=login&a=appinit";
        return str;
    }

    private void firstyz(String url,int gcode)
    {
        apiurl = url;
        RockHttp.get(getYanurl(url), myhandler, gcode);
    }



    public void setOnClickListener(View.OnClickListener cick)
    {

    }

    private void addUrl(String url)
    {
        if(url.equals("http://"))return;
        if(!url.substring(url.length()-1).equals("/")){
            url+="/";
        }
        firstyz(url, 2);
        DialogLoad.show(mContext, "验证中...");
    }

    public void onMenuClick(int index, String name)
    {
        if(index==-1){
            Dialog.prompt(mContext, "请输入你的系统地址：", "系统地址", "http://", new CallBack(){
                @Override
                public void back() {
                    String txt = Dialog.getText();
                    if(!Rock.isEmpt(txt))addUrl(txt);
                }
            }, null);
        }
    }


    protected void onhandleCallback(int gcode,int arg1,String retsult)
    {
        if(gcode==1){
            Json.strParse(arg1, retsult, new CallBack(){
                @Override
                public void backMap(Map<String, String> ret) {
                    saveLoginjson(ret, apiurl);
                }
            }, null);
        }
        if(gcode==2){
            Json.strParse(arg1, retsult, new CallBack(){
                @Override
                public void backMap(Map<String, String> ret) {
                    DialogMsg.success(mContext, "添加成功");
                    saveLoginjson(ret, apiurl);
                }
            }, new CallBack(){
                @Override
                public void backstr(String bstr) {
                    DialogMsg.error(mContext, "地址“"+apiurl+"”不能用\n"+bstr+"");
                }
            });
        }
    }

    /**
     * 获取登录信息
     * */
    public Map<String, String> getLoginMap(String url)
    {
        if(loginlist==null)getLoginList();
        int i;
        Map<String, String> da,ret = null;
        for(i=0;i<loginlist.size();i++) {
            da = loginlist.get(i);
            if(da.get("url").equals(url)){
                ret = da;
                break;
            }
        }
        return ret;
    }

    /**
     * 保存登录信息
     * */
    public void saveLoginjson(Map<String, String> ret, String url)
    {
        Map<String, String> da;

        int xu = -1,i,len=loginlist.size();
        for(i=0;i<loginlist.size();i++) {
            da = loginlist.get(i);
            if(da.get("url").equals(url)){
                xu = i;
            }
        }
        if(xu==-1){
            da = new HashMap<String, String>();
            da.put("url", url);
        }else{
            da = loginlist.get(xu);
        }
        for(Map.Entry<String,String> entry:ret.entrySet()){
            da.put(entry.getKey(),entry.getValue());
        }
        if(xu==-1){
            loginlist.add(da);
        }else{
            loginlist.set(xu,da);
        }

        String allstr = "";
        for(i=0;i<loginlist.size();i++) {
            if(i>0)allstr+=",";
            da = loginlist.get(i);
            allstr+=Json.getJsonString(da);
        }
        allstr = "["+allstr+"]";
        Rock.Sqlite.setOption("loginjson", allstr);
        showlistObj(); //加载了就显示
    }

    //显示列表
    private void showlistObj()
    {
        if(layout==null)return;
        layout.removeAllViews();
        TextView tv;
        Map<String, String> ret;
        RockImageView imgv;
        String str,str1;
        int i;

        for(i=0;i<loginlist.size();i++) {
            ret = loginlist.get(i);
            View v = Rock.getView(mContext, AR.getlayoutID("list_base"));
            imgv = v.findViewById(AR.getID("icons"));
            str = ret.get("titleall");
            if(Rock.isEmpt(str))str = ret.get("title");

            tv = v.findViewById(AR.getID("title"));
            tv.setText(str);

            tv = v.findViewById(AR.getID("titles"));
            str = ret.get("url");
            str1 = ret.get("adminname");
            if(!Rock.isEmpt(str1))str+="("+str1+")";
            tv.setText(str);

            imgv.setPath(Rock.getFace("images/logo.png", ret.get("url")));
            tv = v.findViewById(AR.getID("check"));
            if(ret.get("url").equals(Rock.APIURL)){
                tv.setText("√");
                tv.setTextColor(Color.parseColor(Rock.defTheme));
            }else {
                tv.setText("×");
                final int oi = i;
                v.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        changeSys(oi);
                    }
                });
                tv.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        delChange(oi);
                    }
                });
            }
            layout.addView(v);
        }
    }

    private void delChange(int i)
    {
       // Map<String, String> ret = loginlist.get(i);
        Dialog.confirm(mContext, "确定要删除此数据吗？", "", new CallBack(){
            @Override
            public void back() {
                removeLoginlist(i);
            }
        },null);
    }
    private void removeLoginlist(int oi)
    {
        loginlist.remove(oi);
        Map<String, String> da;
        String allstr = "";
        int i;
        for(i=0;i<loginlist.size();i++) {
            if(i>0)allstr+=",";
            da = loginlist.get(i);
            allstr+=Json.getJsonString(da);
        }
        allstr = "["+allstr+"]";
        Rock.Sqlite.setOption("loginjson", allstr);
        showlistObj();
    }

    /**
     * 获取登录数组
     * */
    public void getLoginList()
    {
        String loginjson = Rock.Sqlite.getOption("loginjson");
        if(Rock.isEmpt(loginjson))loginjson = "[{\"url\":\""+Rock.getApiUrl()+"\", \"title\":\""+AR.getString("app_name")+"\"}]";
        loginlist = Json.getJsonArray(loginjson);
    }

    private void showlist()
    {
        getLoginList();
        showlistObj();
    }
    private void changeSys(int i)
    {
        Map<String, String> ret = loginlist.get(i);
        Dialog.confirm(mContext, "是否切换到“"+ret.get("titleall")+"”", "", new CallBack(){
            @Override
            public void back() {
                String url = ret.get("url");
                Rock.APIURL= url;
                Rock.Sqlite.setOption("apiurl", url);
                DialogMsg.show(mContext, "切换成功", 1, 1, new CallBack(){
                    @Override
                    public void back() {
                        mObj.exitBack(Rock.APIURL, "");
                    }
                });
            }
        },null);
    }
}