
package com.rock.xinhu;


import android.content.Context;

import android.graphics.Color;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;


import com.dialog.Dialog;
import com.dialog.DialogLoad;
import com.dialog.DialogMsg;
import com.baselib.AA;
import com.baselib.AR;
import com.baselib.CLog;
import com.baselib.CallBack;
import com.baselib.Jiami;
import com.baselib.Json;
import com.baselib.Rock;
import com.baselib.RockHttp;
import com.view.RockImageView;

import java.util.HashMap;
import java.util.Map;


public class XinhuBase_login extends XinhuBase {

    private XinhuBase_loginserver Server;
    private Map<String, String> loginInfo;
    private RockImageView faceMobj;

    private TextView chageStr,btnObj,btnyzmObj;
    private View div01,div02;
    private int logintype = 0,yzmtime = 60; //0帐号验证码登录，1验证码登录
    private String temptoken = "",loginyzm = "",tempurl="",tempuser="";
    private Boolean loginBool= false;


    public XinhuBase_login(Context context, View view) {
        super(context, view);
    }

    public void initBase()
    {
        Server = new XinhuBase_loginserver(mContext, null);
        mObj.hideBack();
        ImageView iv = mObj.findViewById(AR.getID("moreimg"));
        iv.setImageDrawable(mContext.getResources().getDrawable(AR.getmipmapID("add")));
        mObj.showMore();

        chageStr = mView.findViewById(AR.getID("yzmstr"));
        btnObj   = mView.findViewById(AR.getID("button"));
        btnyzmObj= mView.findViewById(AR.getID("mobileyzmhq"));
        faceMobj = mView.findViewById(AR.getID("face"));
        div01    = mView.findViewById(AR.getID("div01"));
        div02    = mView.findViewById(AR.getID("div02"));

        RockHttp.get(Server.getYanurl(Rock.APIURL), myhandler, 1);
        showLogin();
    }

    public String getMenuString()
    {
        return  "登录地址设置,扫一扫,APP设置";
    }

    public void setOnClickListener(View.OnClickListener cick)
    {
        btnObj.setOnClickListener(cick);
        Rock.setBackground(btnObj);

        btnyzmObj.setOnClickListener(cick);
        Rock.setBackground(btnyzmObj);

        chageStr.setOnClickListener(cick);
        if(!Rock.isEmpt(Rock.nowtheme)){
            chageStr.setTextColor(Color.parseColor(Rock.nowtheme));
        }
    }

    public void onViewClick(int id)
    {
        if(id==AR.getID("yzmstr")){
            qiehuan();
        }
        if(id==AR.getID("button")){
            loginCheck();
        }
        if(id==AR.getID("mobileyzmhq")){
            getYzmcodes();
        }
    }

    public void onMenuClick(int index, String name)
    {
        if(loginBool)return;
        if(index==0){
            mObj.openXinhu("设置登录地址", "loginserver", "", AA.RESULTCODE_LOGINSERVER);
        }
        if(index==1){
            mObj.openScan(AA.RESULTCODE_SCAN, "none", "");
        }
        if(index==2){
            mObj.openXinhu("系统", "cog");
        }
    }

    public void onActivityResult(int requestCode, String result){
        if(requestCode==AA.RESULTCODE_LOGINSERVER){
            getYan();
        }
        if(requestCode==AA.RESULTCODE_SCAN){
            saoyisaoLogin(result);
        }
    }

    //扫一扫快捷登录
    private void saoyisaoLogin(String str)
    {
        CLog.debug(str);
        if(!Rock.contain(str, "d=we")){
            DialogMsg.error(mContext, "错误的二维码");
            return;
        }
        String[] arr = str.split("\\?"),arr1,arr2;
        String dizhi = arr[0];
        if(arr.length==1)return;
        String user="",token="";
        arr1 = arr[1].split("&");
        for(int i=0;i<arr1.length;i++){
            arr2 = arr1[i].split("=");
            if(arr2[0].equals("user"))user   = arr2[1];
            if(arr2[0].equals("token"))token = arr2[1];
        }
        DialogLoad.show(mContext, "处理中...");
        temptoken   = token;
        tempurl     = dizhi;
        tempuser    = user;
        String url = Server.getYanurl(dizhi);
        RockHttp.get(url, myhandler, 6);
    }
    private void saoyisaoLogins()
    {
        Rock.APIURL= tempurl;
        Rock.Sqlite.setOption("apiurl", tempurl);
        showLogin();
        loginCheck();
    }

    //切换过来时验证
    private void getYan()
    {
        Server.getLoginList();
        chageStr.setVisibility(View.GONE);
        DialogLoad.show(mContext, "验证中...");
        String url = Server.getYanurl(Rock.APIURL);
        RockHttp.get(url, myhandler, 1);
    }

    private void qiehuan()
    {
        if(div01.getVisibility()==View.VISIBLE){
            div01.setVisibility(View.GONE);
            div02.setVisibility(View.VISIBLE);
            chageStr.setText("帐号密码登录");
            logintype = 1;
        }else{
            div02.setVisibility(View.GONE);
            div01.setVisibility(View.VISIBLE);
            chageStr.setText("验证码登录");
            logintype = 0;
        }
    }

    protected void onhandleCallback(int gcode, int arg1, String retsult)
    {
        if(gcode==1){
            DialogLoad.hide();
            btnyzmObj.setEnabled(true);
            Json.strParse(arg1, retsult, new CallBack(){
                @Override
                public void backMap(Map<String, String> ret) {
                    Server.saveLoginjson(ret, Rock.APIURL);
                    showLogin();
                }
            }, new CallBack(){
                @Override
                public void backstr(String bstr) {
                    DialogMsg.error(mContext, "地址“"+Rock.APIURL+"”不能用\n"+bstr+"");
                }
            });
        }
        if(gcode==2){
            loginCheckBack(arg1, retsult);
        }
        if(gcode==3 || gcode==4){
            Json.strParse(arg1, retsult, new CallBack(){
                @Override
                public void backMap(Map<String, String> ret) {
                    DialogLoad.hide();
                    Rock.Toast(mContext, "验证码已发送到对应手机号");
                    if(gcode==3)Dialog.setCenterbtn("已获取", null);
                    if(gcode==4){
                        yzmtime=60;
                        dingStime();
                    }
                }
            }, new CallBack(){
                @Override
                public void backstr(String bstr) {
                    DialogMsg.error(mContext, bstr,3);
                    if(gcode==3)Dialog.setCenterbtn("错误", null);
                    if(gcode==4){
                        btnyzmObj.setText("获取");
                        btnyzmObj.setEnabled(true);
                    }
                }
            });
        }
        //倒计时验证码按钮
        if(gcode==5){
            if(yzmtime==-2){
                btnyzmObj.setText("获取");
            }else {
                yzmtime--;
                if (yzmtime > 0) {
                    dingStime();
                } else {
                    btnyzmObj.setText("在获取");
                    btnyzmObj.setEnabled(true);
                }
            }
        }
        if(gcode==6){
            DialogLoad.hide();
            Json.strParse(arg1, retsult, new CallBack(){
                @Override
                public void backMap(Map<String, String> ret) {
                    ret.put("adminuser", Jiami.base64decode(tempuser));
                    ret.put("adminpass", "");
                    ret.put("adminmobile", "");
                    ret.put("adminname", "");
                    ret.put("face", "");
                    Server.saveLoginjson(ret, tempurl);
                    saoyisaoLogins();
                }
            }, new CallBack(){
                @Override
                public void backstr(String bstr) {
                    temptoken = "";
                    tempuser  = "";
                    DialogMsg.error(mContext, "地址“"+tempurl+"”不能用\n"+bstr+"");
                }
            });
        }
    }

    //显示登录
    private void showLogin()
    {
        loginInfo = Server.getLoginMap(Rock.APIURL);
        String face = loginInfo.get("face");
        if(Rock.isEmpt(face))face = "images/logo.png";
        faceMobj.setPath(Rock.getFace(face));
        String apptheme = loginInfo.get("apptheme");
        if(!Rock.isEmpt(apptheme)) {
            Rock.nowtheme = apptheme;
            Rock.Sqlite.setOption("nowtheme", apptheme);
        }
        //登录方式:0仅使用帐号+密码,1帐号+密码/手机+验证码,2帐号+密码+验证码,3仅使用手机+验证码
        String loginyzm = loginInfo.get("loginyzm"),str;
        if(Rock.isEmpt(loginyzm))loginyzm = "0";
        if(loginyzm.equals("1") || loginyzm.equals("2")) {
            chageStr.setVisibility(View.VISIBLE);
        }else{
            chageStr.setVisibility(View.GONE);
        }
        if(loginyzm.equals("3")){
            chageStr.setVisibility(View.GONE);
            logintype = 1;
            div01.setVisibility(View.GONE);
            div02.setVisibility(View.VISIBLE);
        }
        EditText tv;
        str = loginInfo.get("adminuser");
        tv  = mView.findViewById(AR.getID("user"));
        if(!Rock.isEmpt(str)){
            tv.setText(str);
            str = loginInfo.get("adminpass");
            tv  = mView.findViewById(AR.getID("pass"));
            if(!Rock.isEmpt(str)){
                tv.setText(str);
            }else{
                tv.setText("");
            }
            str = loginInfo.get("adminmobile");
            tv  = mView.findViewById(AR.getID("mobile"));
            if(!Rock.isEmpt(str)){
                tv.setText(str);
            }else{
                tv.setText("");
            }
        }else{
            tv.setText("");
            tv  = mView.findViewById(AR.getID("pass"));
            tv.setText("");
            tv  = mView.findViewById(AR.getID("mobile"));
            tv.setText("");
        }
        tv  = mView.findViewById(AR.getID("mobileyzm"));
        tv.setText("");
    }

    //点击登录
    private void loginCheck()
    {
       // if(loginBool){
       //     DialogLoad.show(mContext, "登录中...");
       //     return;
       // }
        EditText tv;
        String user="",pass="",mobile="";
        Map<String, String> ret = new HashMap<String, String>();
        if(Rock.isEmpt(temptoken)) {
            if (logintype == 0) {
                tv = mView.findViewById(AR.getID("user"));
                user = tv.getText().toString();
                tv = mView.findViewById(AR.getID("pass"));
                pass = tv.getText().toString();
                if (Rock.isEmpt(user)) {
                    Dialog.alert(mContext, "请输入用户名");
                    return;
                }
                if (Rock.isEmpt(pass)) {
                    Dialog.alert(mContext, "请输入密码");
                    return;
                }
                ret.put("adminuser", user);
                ret.put("adminpass", pass);
                Server.saveLoginjson(ret, Rock.APIURL);
            } else {
                tv = mView.findViewById(AR.getID("mobile"));
                mobile = tv.getText().toString();
                tv = mView.findViewById(AR.getID("mobileyzm"));
                loginyzm = tv.getText().toString();
                if (Rock.isEmpt(mobile) || mobile.length() != 11) {
                    Dialog.alert(mContext, "请输入11位数的手机号");
                    return;
                }
                if (Rock.isEmpt(loginyzm) || loginyzm.length() != 6) {
                    Dialog.alert(mContext, "验证码必须是6位数字");
                    return;
                }
                user = mobile;
                ret.put("adminmobile", mobile);
                Server.saveLoginjson(ret, Rock.APIURL);
            }
        }else{
            tv = mView.findViewById(AR.getID("user"));
            user = tv.getText().toString();
        }
        mObj.hideKeyboard();
        DialogLoad.show(mContext, "登录中...");
        loginBool = true;
        Map<String, String> params = new HashMap<String, String>();
        String web      = "app2023"+Rock.getbrand()+""+android.os.Build.MODEL+"";
        params.put("web", web);
        params.put("token", temptoken);
        params.put("device", Rock.deviceId);
        params.put("cfrom", "nppandroid");
        params.put("ltype", ""+logintype+"");
        params.put("yanzm", loginyzm);
        params.put("user", Jiami.base64encode(user));
        params.put("pass", Jiami.base64encode(pass));
        String canstr     = Json.getJsonParams(params);
        RockHttp.post(Rock.getApiUrl("login", "check"), myhandler, 2, canstr, null);
        loginyzm  = "";
        temptoken = "";
    }
    //登录返回
    private void loginCheckBack(int code, String result)
    {
        loginBool = false;
        DialogLoad.hide();
        Json.strParse(code, result, new CallBack(){
            @Override
            public void backMap(Map<String, String> ret) {
                loginCheckBacks(1,ret);
            }
        }, new CallBack(){
            public void backstr(String bstr) {
                DialogMsg.error(mContext, bstr);
            }
            public void backMap(Map<String, String> ret) {
                loginCheckBacks(0,ret);
            }
        });
    }
    private void loginCheckBacks(int code, Map<String, String> ret)
    {
        if(code==1){
            yzmtime = -2;
            loginBool = false;
            faceMobj.setPath(ret.get("face"));
            Rock.Sqlite.setOption("adminid", ret.get("uid"));
            Rock.Sqlite.setOption("admintoken", ret.get("token"));
            Rock.Sqlite.setOption("adminface", ret.get("face"));
            Rock.Sqlite.setOption("apptitle", ret.get("title"));
            Rock.Sqlite.setOption("adminname", ret.get("adminname"));
            Rock.Sqlite.setOption("logininfo", Json.getJsonString(ret));
            Map<String, String> logret = new HashMap<String, String>();
            logret.put("title", ret.get("title"));
            if(logintype==1)logret.put("adminuser", ret.get("user"));
            logret.put("face", ret.get("face"));
            logret.put("adminname", ret.get("name"));
            Server.saveLoginjson(logret, Rock.APIURL);
            DialogMsg.show(mContext, "登录成功",1, 1, new CallBack(){
                public void back() {
                    mObj.exitBack();
                }
            });
        }else{
            String shouji = ret.get("shouji");
            if(Rock.isEmpt(shouji))return;
            Dialog.prompt(mContext, "请输入"+shouji+"手机验证码", "手机验证码", "", new CallBack(){
                public void back() {
                    String str = Dialog.getText();
                    if(Rock.isEmpt(str) || str.length()!=6){
                        DialogMsg.error(mContext, "请输入6位验证码");
                    }else{
                        loginyzm = str;
                        loginCheck();
                    }
                }
            }, null);
            Dialog.setCenterbtn("获取", new CallBack(){
                public void back() {
                    getYzmcode(ret.get("mobile"));
                    Dialog.setCenterbtn("获取中", null);
                }
            });
            Dialog.setCancelable(false);
        }
    }
    private void getYzmcode(String sj)
    {
        String canstr = "mobile="+sj+"&device="+Rock.deviceId+"";
        RockHttp.post(Rock.getApiUrl("yanzm", "index"), myhandler, 3, canstr, null);
    }
    private void getYzmcodes()
    {
        EditText tv;
        tv          = mView.findViewById(AR.getID("mobile"));
        String mobile = tv.getText().toString();
        if(Rock.isEmpt(mobile) || mobile.length()!=11){
            Dialog.alert(mContext, "请输入11位数的手机号");
            return;
        }
        DialogLoad.show(mContext,"获取中...");
        btnyzmObj.setText("获取中");
        btnyzmObj.setEnabled(false);
        String canstr = "mobile="+mobile+"&device="+Rock.deviceId+"";
        RockHttp.post(Rock.getApiUrl("yanzm", "glogin"), myhandler, 4, canstr, null);
    }
    private void dingStime()
    {
        btnyzmObj.setText(""+yzmtime+"S");
        Rock.runTimer(5,1000, myhandler);
    }
}