<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
android:layout_width="fill_parent"
android:background="@color/black"
android:layout_height="fill_parent" >

    <LinearLayout
        android:id="@+id/back"
        android:layout_width="@dimen/headheight"
        android:layout_height="@dimen/headheight"
        android:background="@drawable/btn_tm"
        android:layout_alignParentLeft="true"
        android:layout_marginTop="28dp"
        android:gravity="center"
        android:orientation="vertical">
        <com.view.RockTextViewIcon
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_back"
            android:textSize="20dp"
            android:textColor="@color/white" />
    </LinearLayout>

    <cn.bingoogolapple.qrcode.zbar.ZBarView
        android:id="@+id/zbarview"
        android:layout_width="220dp"
        android:layout_height="220dp"
        android:layout_gravity="center"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"

        app:qrcv_animTime="1000"
        app:qrcv_barCodeTipText="将条码放入框内，即可自动扫描"
        app:qrcv_barcodeRectHeight="120dp"
        app:qrcv_borderColor="@android:color/white"
        app:qrcv_borderSize="1dp"
        app:qrcv_cornerColor="@color/mcolor"
        app:qrcv_cornerLength="20dp"
        app:qrcv_cornerSize="3dp"
        app:qrcv_isBarcode="false"
        app:qrcv_isOnlyDecodeScanBoxArea="false"
        app:qrcv_isShowDefaultGridScanLineDrawable="true"
        app:qrcv_isShowDefaultScanLineDrawable="true"
        app:qrcv_isShowLocationPoint="true"
        app:qrcv_isShowTipBackground="true"
        app:qrcv_isShowTipTextAsSingleLine="false"
        app:qrcv_isTipTextBelowRect="true"
        app:qrcv_maskColor="#33FFFFFF"
        app:qrcv_qrCodeTipText="扫码二维吗"
        app:qrcv_rectWidth="220dp"
        app:qrcv_scanLineColor="@color/mcolor"
        app:qrcv_toolbarHeight="0dp"
        app:qrcv_topOffset="0dp"
        app:qrcv_verticalBias="-1" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/scan_msg"
        android:text="对准二维码进行自动扫描"
        android:textColor="@color/white"
        android:gravity="center"
        android:layout_centerHorizontal="true"
        android:textSize="18dp"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_below="@id/zbarview"
        />



</RelativeLayout>