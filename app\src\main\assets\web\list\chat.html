<link rel="stylesheet" href="res/css/chat.css?3"/>
<script src="js/strformat.js?3"></script>
<script src="res/plugin/jquery-rockupload.js"></script>
<script>
$(document).ready(function(){
	{params}
	touchobj = false;
	var im = {
		minid:999999999,
		atid:0,
		inputlx:false,
		receinfor:false,
		uploadurl:'',
		uptypes:'*',
		createid:0,
		loadci:0,
		inputheight:60,
		nowstate:'',
		init:function(){
			this.listdata = {};
			this.sendinfo = {id:adminid,face:adminface,name:adminname};
			this.createinput();
			this.sendid = ''+adminid+'';
			strformat.emotspath=''+apiurl+'web/';
			this.type = params.type;
			this.gid  = params.id;
			nowchat	  = ''+this.type+''+this.gid+'';
			this.showobj  = $('#showview');
			this.inputobj = $('#contentss');
			//$('#btn').click(function(){
			//	im.sendcont();
			//});
			this.inputobj.keyup(function(){
				im.changeinput();
			});
			this.inputobj.click(function(){
				im.hideTool();
			});
			this.loaddata();
			this.readinforshow();
			this.initapp();
			this.initupfile();

			strformat.openimg=function(o1,fid1){
				im.clickimg(o1,fid1);
			};
			strformat.openfile=function(cs1,cs2,cs3){
				im.openfile(cs1,cs2,cs3);
			};
			strformat.clickface=function(o1,rnd){
				api.imageView({url:o1.src});
			};
			this.showobj.click(function(e){
				im.clickbody(this,e);
				im.hideTool();
			});
			xcy.more=function(){
				im.chatxq();
			}
		},
		initbool:false,
		initapp:function(){
			if(this.initbool)return;
			if(!apixhbool)return;
			clearTimeout(this.readeime);
			this.initbool=true;
			xcy.sendEvent('nowchat','',{nowchat:nowchat});
			api.addEventListener({
				name: 'rockchat'
			}, function(ret) {
				if(ret.name=='rockchat'){
					var slx = ret.stype;
					im.readinforshows(ret);//收到消息
				}
			});
			api.setMenu({menu:'会话详情'},function(ret){
				if(ret.menuIndex==1){
					im.chatxq();
				}
			});
		},
		showmenu:function(){
			setTimeout('touchobj=false',200);
			if(!touchobj)return;
			var rand = $(touchobj).attr('rand');
			var d    = this.listdata[rand];
			if(!d)return;
			this.randmess = rand;
			this.nowdata  = d;
			this.nowcont  = $(touchobj).text();
			this.chuihuinr= 'PGRlbCBzdHlsZT0iY29sb3I6Z3JheSI!5bey5pKk5ZuePC9kZWw!';
			var chehui = js.getoption('chehui');
			if(this.type=='gout')chehui=120;
			var da = ['复制此消息','删除|'+deletecolor+'', '@TA'],ischui=false,isch=false;
			if(d.cont!=this.chuihuinr)da.push('收藏');
			if(chehui>0 && d.sendid==this.sendid){
				var t1 = js.now('time', d.optdt),t2 = js.now('time');
				var t3 = (t2-t1)*0.001;
				if(t3<chehui)isch=true;
			}
			if(this.createid==this.sendid)isch=true;//群主都可以撤回
			if(d.cont==this.chuihuinr)isch=false;
			if(isch){da.push('撤回');ischui=true;}
			var wdwd = '已读未读情况';
			if(this.type!='gout' && d.sendid==this.sendid)da.push(wdwd);
			
			api.createMenu({
				menu:da.join(',')
			}, function(ret){
				var str = ret.name,index =ret.menuIndex ;
				if(index==0)im.copyner();
				if(index==1)im.delrecord();
				if(index==2)im.anteta();
				if(str=='撤回' && ischui)im.gochehui();
				if(str=='收藏')im.staradd();
				if(str==wdwd)im.showqingkue();
			});
		},
		showqingkue:function(){
			xcy.opennei({name:'消息情况',messid:this.nowdata.id,'url':'chatqk'});
		},
		copyner:function(){
			api.rockFun('clipBoard',{
				msg: this.nowcont
			});
			api.toast({msg:'已复制'});
		},
		urlstr:function(st1){
			if(this.type=='gout'){
				var urla = st1.split('|');
				return xcy.outgroup.geturl(urla[1]);
			}
			return st1;
		},
		delrecord:function(){
			$('#ltcont_'+this.randmess+'').remove();
			var ids = this.nowdata.id;
			if(!isNaN(ids)){
				js.ajax(this.urlstr('reim|clearrecord'),{type:this.type,gid:this.gid,ids:ids});
			}
		},
		gochehui:function(){
			var o1dd = $('#qipaocont_'+this.randmess+'');
			var ids = this.nowdata.id;
			if(!isNaN(ids)){
				o1dd.html(js.getmsg('撤回中...'));
				js.ajax(this.urlstr('reim|chehuimess'),{type:this.type,gid:this.gid,ids:ids}, function(ret){
					im.listdata[im.randmess].ocont = im.listdata[im.randmess].cont;
					im.listdata[im.randmess].cont = 'PGRlbCBzdHlsZT0iY29sb3I6Z3JheSI!5bey5pKk5ZuePC9kZWw!';
					o1dd.html(js.getmsg(ret.data.msg1,'green')+' <a onclick="im{rand}.rebianji(this,\''+im.randmess+'\')" href="javascript:;">重新编辑</a>');
				},'get', function(){
					o1dd.html(js.getmsg('撤回失败','red'));
				});
			}
		},
		rebianji:function(o1,rnd){
			var d  = this.listdata[rnd],
			nstr = jm.base64decode(d.ocont);
			$('body').append('<div id="addcstre" style="display:none">'+nstr+'</div>');	
			this.inputobj.val($('#addcstre').text());
			$('#addcstre').remove();
		},
		anteta:function(){
			var na = this.nowdata.sendname;if(!na)na = adminname;
			this.atshow(this.nowdata.sendid,na);
		},
		chatxq:function(){
			if(this.type=='group' || this.type=='gout'){
				var ustr={name:document.title,type:this.type,id:this.gid,url:'chatx'};
				xcy.opennei(ustr);
			}else{
				js.setoption('nowuser', JSON.stringify(this.receinfor));
				var ustr={name:this.receinfor.name,laiyuan:'chat',id:this.receinfor.id,url:'userinfo'};
				xcy.opennei(ustr);
			}
		},
		changeinput:function(){
			var val = this.inputobj.val();
			if(val){
				$('#addbtn').hide();
				$('#btn').show();
			}else{
				$('#addbtn').show();
				$('#btn').hide();
			}
		},
		submitinput:function(){
			try{im.sendcont();}catch(e){}
			return false;
		},
	
		loaddata:function(o1, iref){
			if(this.boolload)return;
			var iref = (!iref)?false:true;
			var minid= 0;
			if(iref)minid=this.minid;
			if(o1)$(o1).html('<i style="height:13px;width:13px" class="rock-loading"></i> 加载中...');
			this.boolload 	= true;
			this.isshangla 	= false;
			js.ajax(this.urlstr('reim|getrecord'),{type:this.type,gid:this.gid,minid:minid,lastdt:'',laiyuan:'not',loadci:this.loadci},function(ret){
				if(o1)$(o1).html('');
				im.boolload = false;
				im.loaddatashow(ret.data, iref);
			});
		},
		readinforshow:function(){
			clearTimeout(this.readeime);
			if(!apixhbool){
				this.readeime=setTimeout(function(){im.readinforshows()},1000*10);
			}
		},
		clickbody:function(o,e){
			if(e.target.nodeName=='FONT'){
				var o1   = $(e.target);
				var atid = o1.attr('atid'),nst;
				if(atid){
					nst = o1.text();
					this.atshow(atid,nst.substr(1));
				}
			}
		},
		atshow:function(id1,na1){
			this.atid = id1;
			this.inputobj.val('@'+na1+' '+this.inputobj.val());
		},
		readinforshows:function(d){
			var slx 	= '';
			if(d)slx 	= d.stype;//showAlert(JSON.stringify(d));
			if(slx=='at'){
				if(d.type==this.type && d.gid==this.gid){
					this.atshow(d.id,d.namexm);
				}
				return;
			}
			if(slx=='star'){
				if(d.type==this.type && d.gid==this.gid){
					this.staropen(d);;
				}
				return;
			}
			if(slx=='longmenu'){
				this.showmenu();
				return;
			}
			if(slx=='onresume'){
				xcy.sendEvent('nowchat','',{nowchat:nowchat});
				this.nowstate = slx;
				this.loadnew();
				return;
			}
			if(slx=='onstop'){
				this.nowstate = slx;
				return;
			}
			if(slx=='reload' || slx=='newmess'){
				this.loadnew();
			}
			if(d && d.type=='chehui'){
				$('#qipaocont_mess_'+d.messid+'').html(jm.base64decode(d.cont));
			}
		},
		loadnew:function(){
			var minid=this.minid;
			if(!this.readbool)js.ajax(this.urlstr('reim|getrecord'),{type:this.type,gid:this.gid,minid:0,laiyuan:'new',lastdt:this.lastdt,loadci:this.loadci},function(ret){
				im.loaddatashow(ret.data, false, true);
				im.readinforshow();
			},'get', function(){
				im.readbool=false;
			});
			this.readbool = true;
		},
		//isls 新数据
		fileobj:{},
		loaddatashow:function(ret,isbf, isls){
			var a 		= ret.rows;
			this.loadci++;
			this.readbool = false;
			if(!this.lastdt && ret.servernow){
				js.servernow = ret.servernow;
				js.getsplit();
				this.showobj.html('');
			}
			var recfo = ret.receinfor;
			if(recfo)this.receinfor = recfo;
			if(recfo && (recfo.type=='group' || recfo.type=='gout')){
				xcy.setTitle(''+recfo.name+'('+recfo.utotal+')');
				//$('#header_title').html(''+recfo.name+'('+recfo.utotal+')'+xcy.grouptype(recfo.deptid, recfo.type)+'');
				var bqa = xcy.grouptype(recfo.deptid, recfo.type, true);
				if(apixhbool && bqa)api.rockFun('setTitle', {
					'labelbgcolor':bqa[1],
					'label':bqa[0]
				});
				if(recfo.createid)this.createid = recfo.createid;
			}
			var secfo = ret.sendinfo;
			if(secfo){
				this.sendinfo= secfo;
				this.sendid = secfo.id;
				adminface = secfo.face;
				this.uploadurl = secfo.uploadurl;
				if(secfo.uptypes)this.uptypes = secfo.uptypes;
			}
			xcy.sendEvent('biaoyd','',{type:this.type,gid:this.gid});
			this.lastdt = ret.nowdt;
			var i,len 	= a.length,cont,lex,nas,fase,nr,d=false,na=[],rnd,sid,frs,nfr1;
			$('#loadmored').remove();
			if(isbf){
				if(len>0)this.showobj.prepend('<div class="showblanks">---------↑以上是新加载---------</div>');
				na = a;
			}else{
				for(i= len-1; i>=0; i--)na.push(a[i]);
			}
			for(i= 0; i<len; i++){
				d   = na[i];
				sid = parseFloat(d.id);
				lex = 'right';
				nas = this.sendinfo.name;
				fase= this.sendinfo.face;
				if(d.sendid!=this.sendid){
					lex='left';
					nas= d.sendname;
					fase= d.face;
				}
				rnd = 'mess_'+sid+'';
				if(!this.listdata[rnd]){
					this.listdata[rnd]=d;
					frs = d.filers;
					if(frs)this.fileobj[frs.fileid]=frs;
					if(frs && frs.fileid && js.isimg(frs.fileext)){
						nfr1 = js.getoption('filesrc_'+frs.fileid+'');
						if(nfr1)frs.thumbpath = nfr1;
					}
					nr  = this.contshozt(frs, rnd);
					if(nr=='')nr= jm.base64decode(d.cont);
					cont= strformat.showqp(lex,nas,d.optdt,nr ,'', fase, rnd,d.bqname,d.bqcolor);
					if(!isbf){
						this.addcont(cont, isbf);
					}else{
						this.showobj.prepend(cont);
					}
				}
				if(sid<this.minid)this.minid=sid;
			}
			if(len>0 && !isls){
				var s = '<div id="histordiv" class="showblanks" >';
				if(ret.wdtotal==0){
					s+='---------↑以上是历史记录---------';
					if(ret.systotal>0){
						this.showobj.prepend('<div class="showblanks" ><a onclick="im{rand}.loadmoreda(this)"><i class="icon-time"></i> 还有'+ret.systotal+'条点我查看更多</a></div>');
						this.isshangla=true;
					}else{
						this.showobj.prepend('<div class="showblanks" >没有更多了</div>');
						this.isshangla=false;
					}
				}else{
					s+='---↑以上是历史,还有未读信息'+ret.wdtotal+'条,<a href="javascript:;" onclick="im{rand}.loaddata(this)">点击加载</a>---';
				}
				s+='</div>';
				if(!isbf)this.addcont(s);
			}
			if(d && isls){
				if(this.nowstate=='onstop' && apixhbool){
					var cont = jm.base64decode(d.cont);
					if(cont.indexOf('通话]')==-1)api.Notification({
						title:'新消息',
						msg:''+d.sendname+'发来信息消息'
					});
				}
				//showAlert(JSON.stringify(d));
			}
			if(ret.showmsg)this.addmsg(ret.showmsg);
			xcy.reloadok();
		},
		dropdown_success:function(){
			if(this.isshangla){
				this.loadmoreda(false);
			}else{
				setTimeout(function(){xcy.reloadok()},1000);
			}
		},
		loadmoreda:function(o1){
			this.loaddata(o1, true);
		},
		scrollboot:function(){
			$('#mainbody').animate({scrollTop:get('mainbody').scrollHeight},100);
		},
		addcont:function(cont, isbf){
			var o	= this.showobj;
			if(cont){if(isbf){o.prepend(cont);}else{o.append(cont);}}
			clearTimeout(this.scrolltime);
			this.scrolltime	= setTimeout(function(){
				im.scrollboot();
			}, 50);
		},
		clickimg:function(o1,fid){
			var o=$(o1);
			var src = o1.src.replace('_s.','.');
			if(apixhbool && src.substr(-3)!='gif'){
				var frs = this.fileobj[fid];
				api.imageView({
					filesizecn:frs.filesizecn,
					url:frs.thumbpath,
				});
			}else{
				js.showplugin('imgview',{
					url:src,ismobile:true,
					fileid:fid,
					onloadsuccess:function(img){
						if(img.src.substr(-3)=='gif'){
							o1.src = img.src;
							js.setoption('filesrc_'+this.fileid+'', img.src);
						}
					}
				});
			}
		},
		contshozt:function(d,rnd){
			return strformat.contshozt(d, rnd);
		},
		sendcont : function(ssnr){
			if(this.sendbool)return;
			js.msg('none');
			var nr  = '';
			if(ssnr){
				nr= ssnr;
			}else{
				nr= this.inputobj.val();
			}
			nr	= strformat.sendinstr(nr);
			nr	= nr.replace(/</gi,'&lt;').replace(/>/gi,'&gt;').replace(/\n/gi,'<br>');
			if(isempt(nr))return false;
			if(nr.indexOf('@')<0)this.atid=0;
			var conss = jm.base64encode(nr);
			if(conss.length>3998){
				js.msg('msg','发送内容太多了');
				return;
			}
			var nuid= js.now('time'),optdt = js.serverdt();
			if(optdt==this.nowoptdt){
				js.msg('msg','消息发太快了');
				return;
			}
			this.nowoptdt = optdt;
			var cont= strformat.showqp('right',this.sendinfo.name,optdt, nr, nuid, this.sendinfo.face,nuid,this.sendinfo.bqname,this.sendinfo.bqcolor);
			this.addcont(cont);
			if(!this.inputlx)this.inputobj.val('');
			this.changeinput();
			this.sendconts(conss, nuid, optdt, 0);
			return false;
		},
		sendconts:function(conss, nuid, optdt, fid){
			//if(apicloud)api.cancelNotification({id:-1});
			var d 	 = {cont:conss,gid:this.gid,type:this.type,nuid:nuid,optdt:optdt,fileid:fid,atid:''+this.atid+''};
			this.atid= 0;
			js.ajax(this.urlstr('reim|sendinfor'),d,function(ret){
				im.sendsuccess(ret.data,nuid,d);
			},'post',function(){
				im.senderror(nuid,d);
			});
		},
		senderror:function(nuid,ds){
			js.ajaxbool = false;
			if(!get(nuid))return;
			get(nuid).src='images/error.png';
			get(nuid).title='发送失败';
			if(ds)get(nuid).onclick=function(){
				rockconfirm('是否要重新发送？', function(jg){
					if(jg=='yes'){
						get(nuid).src='images/loadings.gif';
						get(nuid).title='发送中...';
						im.atid = ds.atid;
						im.sendconts(ds.cont,ds.nuid,ds.optdt,ds.fileid);
					}
				});
			}
		},
		sendsuccess:function(d,nuid,sd){
			this.bool = false;
			if(!d.id){
				this.senderror(nuid,sd);
				return;
			}
			this.listdata[d.nuid]=d;
			this.listdata['mess_'+d.id+'']=d;
			$('#'+d.nuid+'').remove();
			var bo = false;
			d.messid=d.id;
			d.face  = adminface;
			if(d.showmsg)this.addmsg(d.showmsg);
			if(apixhbool){
				if(this.type=='gout'){
					xcy.sendEvent('regout');
				}else{
					xcy.sendEvent('reload');
				}
			}
		},
		addmsg:function(msg){
			this.addcont('<div class="showblanks" >'+msg+'</div>');	
		},
		addinput:function(s){
			var val = this.inputobj.val()+s;
			this.inputobj.val(val).focus();
		},
		showemit:function(){
			var da = [];
			var a = strformat.emotsarr,i;
			for(i=1;i<50;i++)da.push({name:'&nbsp; <img src="web/images/im/emots/qq/'+(i-1)+'.gif" align="absmiddle">&nbsp;'+a[i], num:a[i]});
			js.showmenu({
				data:da,width:150,align:'left',
				onclick:function(d){
					im.addinput(d.num);
				}
			});
		},
		initupfile:function(){
			if(!this.uploadobj)this.uploadobj = $.rockupload({
				inputfile:'allfileinput',
				updir:'reimchat',
				urlparams:{noasyn:'yes','laiyuan':'chat'}, //不需要同步到文件平台上
				initpdbool:false,
				onchange:function(d){
					im.sendfileshow(d);
				},
				onprogress:function(f,per,evt){
					strformat.upprogresss(per);
				},
				onsuccess:function(f,str,o1){
					im.sendfileok(f,str);
				},
				onerror:function(str){
					js.msg('msg', str);
					im.senderror(im.upfilearr.nuid);
				}
			});
			strformat.upobj = this.uploadobj;
		},
		sendfile:function(){
			if(this.type=='gout'){
				js.msgerror('不支持');
			}else{
				this.uploadobj.click();
			}
		},
		sendfileshow:function(f,snr){
			f.face 		= this.sendinfo.face;
			f.bqname 	= this.sendinfo.bqname;
			f.bqcolor 	= this.sendinfo.bqcolor;
			f.sendname 	= this.sendinfo.name;
			var fa 	= strformat.showupfile(f,snr);
			var cont= fa.cont;
			this.upfilearr = fa;
			this.addcont(cont);
		},
		sendfileok:function(f,str){
			var tm		= this.upfilearr,conss='';
			var a 		= js.decode(str);
			a.isimg 	= js.isimg(a.fileext);
			a.thumbpath = xcy.getface(a.thumbpath);
			if(!a.fileid)a.fileid = a.id;
			if(this.type=='gout')a.fileid = a.filenum;
			strformat.upsuccess(a);
			if(a.isimg){
				conss = '[图片 '+a.filesizecn+']';
			}else{
				conss = '['+a.filename+' '+a.filesizecn+']'
			}
			if(a.filename.indexOf('rockyuyin')==0){
				var miao = strformat.getmiao(a.filename);
				if(!miao)miao = '1+';
				conss='[语音 '+miao+'秒]';
			}
			this.fileobj[a.fileid]= a;
			this.sendconts(jm.base64encode(conss), tm.nuid, tm.optdt, a.id);
		},
		fileyulan:function(pn,fid){
			var url = '?m=public&a=fileviewer&id='+fid+'';
			js.location(url);
		},
		getfinfo:function(dz,olts){
			var name = dz.substr(dz.lastIndexOf('/')+1);
			var ext  = name.substr(name.lastIndexOf('.')+1).toLowerCase();
			if(!olts)olts = {}
			olts.filename = name;
			olts.fileext = ext;
			return olts;
		},
		changeappok:function(ret){
			if(ret.data){
				im.sendapifile(ret.data);
			}
		},
		sendapifile:function(path,olts){
			var url = this.uploadobj.getupurl();
			if(this.type=='gout')url = this.uploadurl;
			if(!url){
				js.msgerror('无上传地址');
				return; 
			}
			var f = this.getfinfo(path,olts);
			if(!f.filesize)f.filesize= 3000;
			if(!f.filesizecn)f.filesizecn= js.formatsize(f.filesize);
			if(this.uptypes!='*'){
				if(this.uptypes.indexOf(','+f.fileext+',')<0){
					js.msg('msg','禁止上传类型只能选：'+this.uptypes+'');
					return; 
				}
			}
			f.isimg= false;
			var fnr = '';
			if(olts.base64Data)fnr='data:image/jpeg;base64,'+olts.base64Data;
			this.sendfileshow(f,fnr);
			xcy.resiezess();
			api.upload({
				filepath:path,
				url:url
			},function(ret,err){
				if(ret){
					if(ret.valid==0){
						strformat.upprogresss(ret.progress);
					}else{
						im.sendfileok({}, JSON.stringify(ret));
					}
				}else{
					js.msg('msg',err.responseText);
					im.senderror(im.upfilearr.nuid);
				}
			});
		},
		hideTool:function(){
			var bo = (xcy.otherheight>this.inputheight);
			xcy.otherheight = this.inputheight;
			$('#footerother').css('height',''+xcy.otherheight+'px');
			$('#toolsdiv').hide();
			$('#biaoqingdiv').hide();
			if(bo)xcy.resiezess();
		},
		changebiaoqing:function(){
			if(get('biaoqingdiv').style.display=='none'){
				if(!this.showbiaoqboo){
					$.getJSON('images/emotion/emotion.json',function(ret){
						$('#biaoqingdiv').html(im.showBiao(ret));
					});
				}
				this.showbiaoqboo = true;
				$('#biaoqingdiv').show();
				$('#toolsdiv').hide();
				xcy.otherheight = 300;
			}else{
				$('#biaoqingdiv').hide();
				xcy.otherheight = this.inputheight;;
			}
			$('#footerother').css('height',''+xcy.otherheight+'px');
			xcy.resiezess();
		},
		resieze:function(){
			this.scrollboot();
		},
		changeaddbtn:function(){
			if(get('toolsdiv').style.display=='none'){
				$('#biaoqingdiv').hide();
				$('#toolsdiv').html(this.showTool());
				$('#toolsdiv').show();
				xcy.otherheight = 300;
			}else{
				$('#toolsdiv').hide();
				xcy.otherheight = this.inputheight;
			}
			$('#footerother').css('height',''+xcy.otherheight+'px');
			xcy.resiezess();
		},
		clickBiao:function(s){
			var val = this.inputobj.val()+s;
			this.inputobj.val(val);
			this.changeinput();
		},
		paizhao:function(lx){
			api.getPicture({
				sourceType:lx
			},function(ret){
				im.sendapifile(ret.filepath, ret);
			});
		},
		fatonghua:function(){
			if(!this.receinfor)return;
			var da = this.receinfor;
			js.setoption('tonghuaface', da.face);
			xcy.opennei({name:da.name,id:da.id,iscall:true,'url':'tonghua'});
		},
		createinput:function(){
			var str = '<div style="height:'+this.inputheight+'px;user-select:none;border-top:var(--border)" class="r-chat" id="footerother">'+
				'<form onsubmit="return im{rand}.submitinput()">'+
				'<table border="0" cellspacing="0" height="'+this.inputheight+'" cellpadding="0" width="100%"><tr>'+
				'<td style="padding-left:8px;"><div onclick="im{rand}.yuyin()" style="height:34px;overflow:hidden;width:34px"><img id="luyinbtn" src="images/chatimg1.png" height="34px" width="34px"></div></td>'+
				'<td width="100%" style="padding:0px 8px;"><textarea autocomplete="off" style="width:100%;margin-top:7px;height:34px;padding:0px 8px;padding-top:6px;border:none;border-radius:5px;background:white;line-height:26px;" type="text" id="contentss" ondblclick="im{rand}.inputmax()" class="fontsize" onclick="this.focus()"></textarea><button class="fontsize" style="text-align:center;border:1px #cccccc solid;border-radius:6px;background:white;width:100%;display:none;height:36px;margin-left:10px" type="button" id="anzhubtn" class="r-chat-input" ontouchstart="im{rand}.startluyin()" value="">点击开始说话</button></td>'+
				'<td><div style="width:16px;overflow:hidden"></div></td>'+
				'<td style="padding-right:8px;"><div style="height:33px;overflow:hidden;width:33px" onclick="im{rand}.changebiaoqing()"><img src="images/chatimg0.png" height="33px" width="33px"></div></td>'+
				'<td style="padding-right:8px;">'+
				'<button style="border-radius:5px;margin:0px 3px 0px 3px;display:none" type="button" id="btn" ontouchstart="return false" ontouchend="im{rand}.sendcont()" class="r-chat-btn r-btn-active">发送</button>'+
				'<div style="height:34px;overflow:hidden;width:34px" onclick="im{rand}.changeaddbtn()" id="addbtn"><img src="images/chatimg2.png" height="34px" width="34px"></div>'+
				'</td>'+
				'</tr></table>'+
				'</form>'+
				'<div id="toolsdiv" style="height:240px;border-top:0.5px #cccccc solid;display:none"></div>'+
				'<div id="biaoqingdiv" style="height:240px;border-top:0.5px #cccccc solid;display:none;overflow-y:auto"><div align="center" style="margin-top:30px"><i style="height:40px;width:40px" class="rock-loading"></i></div></div>'+
			'</div>';
			
			$('#mainbody').after(str);
			get('anzhubtn').addEventListener('touchstart',function(e){
				e.preventDefault(e);
			},false);
			xcy.otherheight = this.inputheight;
			xcy.resiezess();
		},
		inputmax:function(){
			if(!apixhbool)return;
			return;
			rockprompt('','请输入内容', function(txt){
				c.inputobj.val(txt);
			},this.inputobj.val());
			return;
		},
		clickTool:function(lx, icn){
			if(icn=='camera')this.paizhao('camera');
			if(icn=='picture')this.paizhao('album');
			if(icn=='folder-close')this.sendfile();
			if(icn=='star')this.showstar();
			if(icn=='headphones')this.atshow(2,'信呼客服');
			if(icn=='facetime-video')this.tonghua();
			//if(lx==4)this.fatonghua();
		},
		showTool:function(){
			if(this.showToolstr)return this.showToolstr;
			var arr = [{name:'拍照',icon:'camera'},{name:'图片',icon:'picture'},{name:'发文件',icon:'folder-close'},{name:'收藏',icon:'star'}];
			if(this.type=='user' && js.getoption('tonghuabo')=='1' && apixhbool)arr.push({name:'音视频',icon:'facetime-video'});
			if(this.type=='gout')arr.push({name:'信呼客服',icon:'headphones'});
			var str = '<div style="margin:2px 8px"><table width="100%"><tr>';
			for(var i=0;i<arr.length;i++){
				if(i%4==0)str+='</tr><tr>';
				str+='<td width="25%" onclick="im{rand}.clickTool('+i+',\''+arr[i].icon+'\')" align="center">';
				str+='<div style="width:55px;height:55px;background:white;font-size:26px;line-height:55px;border-radius:10px;margin-top:20px" class="zhu"><i class="icon-'+arr[i].icon+'"></i></div>';
				str+='<div style="font-size:12px;color:#888888">'+arr[i].name+'</div>';
				str+='</td>';
			}
			str+='</tr></table></div>';
			this.showToolstr = str;
			return str;
		},
		showBiao:function(arr){
			var str = '<div style="margin:5px"><table width="100%"><tr>';
			var i,j=0,len=arr.length,w=34;
			for(i=0;i<len;i++){
				j++;
				str+='<td width="12.5%" onclick="im{rand}.clickBiao(\''+arr[i].text+'\')" align="center">';
				str+='<div style="margin:3px 0px"><img height="'+w+'" width="'+w+'" src="images/emotion/'+arr[i].name+'.png"></div>';
				str+='</td>';
				if(j%8==0)str+='</tr><tr>';
			}
			str+='</tr></table></div>';
			return str;
		},
		isluyin:false,
		yuyin:function(){
			//js.msg('msg','不支持语音',1);return;
			if(this.isluyin){
				get('luyinbtn').src='images/chatimg1.png';
				$('#contentss').show();
				$('#anzhubtn').hide();
				this.isluyin=false;
			}else{
				get('luyinbtn').src='images/chatimg3.png';
				$('#contentss').hide();
				$('#anzhubtn').show();
				this.isluyin=true;
				this.hideTool();
			}
		}, 
		startluyin:function(){
			api.rockFun('startRecord',{
			},function(ret){
				if(ret.status>3){
					im.sendapifile(ret.filepath, {filesize:ret.status});
				}else if(ret.status==2){
					if(ret.filepath)js.msg('success', ret.filepath, 2, 'bottom');
				}
			});
		},
		stopluyin:function(bo){
			
		},
		openfile:function(fid, fext, nuid){
			if(fext=='amr' && apicloud){
				if(this.isbofang){
					this.bofang();
					if(this.fileid==fid)return;
				}
				this.fileid = fid;
				this.yuyinpath = 'fs://media/'+fid+'_yuyin.'+fext+'';
				this.yuyinnuid = nuid;
				if(!this.fs)this.fs = api.require('fs');
				this.fs.exist({
					path: this.yuyinpath
				}, function(ret, err) {
					if (ret.exist){
						im.bofang();
					} else {
						im.downyuyin();
					}
				});
			}else{
				xcy.opennei({name:'文件',url:'fileopen',fileid:fid,fileext:fext,type:this.type});
			}
		},
		downyuyin:function(){
			this.isbofang=false;
			$('#qipaomsg_'+this.yuyinnuid +'').html('<div style="padding:5px"><span style="font-size:12px;color:#ff6600">下载中</span></div>');
			api.download({
				url:js.apiurl('file','down',{id:this.fileid}),
				report:false,cache: true,
				savePath: this.yuyinpath,
				allowResume: true
			}, function(ret, err) {
				if (ret.state == 1) {
					im.bofang();
				} else {
				}
			});
		},
		bofang:function(){
			var obj = $('#qipaomsg_'+this.yuyinnuid +'');
			if(this.isbofang){
				obj.html('');
				api.stopPlay();
				clearInterval(this.bofangtime);
				this.isbofang=false;
				return;
			}
			this.isbofang = true;
			this.bocishu  = 0;
			obj.html('<div class="r-zhu" style="padding:5px;font-size:18px"><i class="icon-volume-up"></i></div>');
			this.bofangtime = setInterval(function(){
				im.bocishu++;
				if(im.bocishu%2==0){
					obj.html('<div class="r-zhu" style="padding:5px;font-size:18px"><i class="icon-volume-up"></i></div>');
				}else{
					obj.html('<div class="r-zhu" style="padding:5px;font-size:18px"><i class="icon-volume-down"></i></div>');
				}
			},260);
			api.startPlay({
				path:this.yuyinpath
			}, function(ret,err){
				if (ret) {
					obj.html('');
					clearInterval(im.bofangtime);
					im.isbofang=false;
				} else {
					js.msg('msg','无法播放');
					im.bofang();
				}
			});
		},
		showstar:function(){
			xcy.opennei({name:'收藏的消息',type:this.type,gid:this.gid,'url':'star'});
		},
		staropen:function(d){
			if(d.fileid==0){
				var nr = jm.base64decode(d.cont);
				$('body').append('<div id="addcstre" style="display:none">'+nr+'</div>');
				this.clickBiao($('#addcstre').text());
				$('#addcstre').remove();
			}else{
				var str = '确定要发送“'+d.filename+'('+d.filesizecn+')”文件吗？';
				var imgpath = '';
				if(d.imgurl)imgpath = d.imgurl;
				api.confirm({
					msg:str,imgpath:imgpath
				},function(ret){
					if(ret.buttonIndex==1)im.staropensend(d);
				});
			}
		},
		staropensend:function(d){
			var sf = {
				fileext:d.fileext,
				filename:d.filename,
				filesizecn:d.filesizecn,
				id:d.fileid,
				filenum:d.filenum
			}
			if(d.imgurl){
				sf.filename = '图片';
				sf.isimg = true;
				sf.imgviewurl = d.imgurl;
				sf.thumbpath = d.imgurl;
			}
			this.sendfileshow(sf);
			this.sendfileok({},JSON.stringify(sf));
		},
		staradd:function(){
			var d = this.nowdata;
			js.loading('收藏中...');
			var da = {messid:d.id,fileid:d.fileid,cont:d.cont,type:this.type}
			if(d.filers){
				da.fileext = d.filers['fileext'];
				var img    = d.filers['thumbpath'];
				var nae    = d.filers['filename'];
				var sids   = d.filers['filesizecn'];
				var nnud   = d.filers['filenum'];
				if(js.isimg(da.fileext) && img)da.imgurl = img;
				if(nae)da.filename = nae;
				if(sids)da.filesizecn = sids;
				if(nnud)da.filenum = nnud;
			}
			var kev = ''+d.id+'';if(this.type=='gout' || this.type=='uout')kev='out_'+d.id+'';
			js.ajax('reim|savestar',{kev:kev,content:jm.base64encode(JSON.stringify(da))}, function(ret){
				api.showMsg({msg:'收藏成功'});
			},'post');
		},
		tonghua:function(){
			api.rockFun('tonghua',{
				id:this.receinfor.id,
				name:this.receinfor.name,
				ranking:this.receinfor.ranking,
				face:this.receinfor.face,
				myid:this.sendinfo.id
			});
		}
	}
	im.init();
	
	xcy.initApp=function(){
		initAppbool = true;
		if(api.reglongmenu)api.reglongmenu();
		im.initapp();
	}
	im{rand}=im;
	xcy.resieze=function(){
		im.resieze();
	}
	xcy.longmenu=function(){
		im.showmenu();
	}
});
</script>
<style>
.showblanks{padding:10px;color:#bbbbbb;font-size:13px;text-align:center;user-select:none}
</style>

<div id="showview">
<div align="center" style="margin-top:50px"><i style="height:40px;width:40px" class="rock-loading"></i></div>
</div>