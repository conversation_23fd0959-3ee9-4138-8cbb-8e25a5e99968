package com.rock.xinhuoanew;


import android.graphics.Color;
import android.os.Bundle;
import android.view.View;

import com.dialog.DialogLoad;
import com.baselib.Json;
import com.baselib.Rock;

import java.util.Map;

public class WebneiActivity extends WebviewActivity {

    protected void initCreate() {
        setContentView(R.layout.activity_web);
        Bundle bundle = this.getIntent().getExtras();
        String name   = bundle.getString("name");
        String url    = bundle.getString("url");
        String paramsstr    = bundle.getString("paramsstr");
        String strs;
        if(!Rock.isEmpt(paramsstr)){
            Map<String,String> ret = Json.getJsonObject(paramsstr);
            if(!Rock.isEmpt(ret.get("progress"))){
                DialogLoad.show(this);
            }
            strs = ret.get("menu");
            if(!Rock.isEmpt(strs))menustring = strs;
            String animtype = ret.get("animtype");//打开类型
            if(Rock.contain(animtype, "show"))overridePendingTransition(R.anim.main_zoom,R.anim.main_out);
        }
        setTitles(name);
        findViewById(R.id.back).setOnClickListener(OnViewClickListener);
        findViewById(R.id.more).setOnClickListener(OnViewClickListener);
        gotoUrl(url);
        //CLog.debug(paramsstr);

        if(!Rock.isEmpt(Rock.nowtheme)) {
            View headerv = (View) findViewById(R.id.headertop);
            headerv.setBackgroundColor(Color.parseColor(Rock.nowtheme));
            setStatusColor(Color.parseColor(Rock.nowtheme));
        }else{
            setStatusColor(getResources().getColor(R.color.mcolor));
        }

        setOnLongClick(new View.OnLongClickListener(){
            @Override
            public boolean onLongClick(View view) {
                Xinhu.sendBroadcastApi(mActivity, guanbo_Action, "longmenu");
                return false;
            }
        });

    }

}