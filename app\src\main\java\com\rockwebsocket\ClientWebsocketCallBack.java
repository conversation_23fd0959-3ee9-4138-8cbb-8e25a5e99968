package com.rockwebsocket;



public class ClientWebsocketCallBack {

    /**
     * 收到的消息处理
     * */
    public void  onMessage(String message)
    {

    }

    /**
     * 打开了
     * */
    public void onOpen() {

    }

    /**
     * 关闭
     * */
    public void onClose(int code, String reason) {

    }

    /**
     * 有错误就关闭
     * */
    public void onError(int code, String errmsg)
    {

    }
}
