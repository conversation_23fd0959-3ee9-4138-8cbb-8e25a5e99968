package com.view;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;

import com.baselib.Rock;


public class RockEditText extends androidx.appcompat.widget.AppCompatEditText {

    public RockEditText(Context context) {
        super(context);
    }

    public RockEditText(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public RockEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        float size = 18;
        if(Rock.fontsize==1)size = 20;
        if(Rock.fontsize==2)size = 22;
        this.setTextSize(size);
    }
}