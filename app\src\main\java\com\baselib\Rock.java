/**
 * 说明：Android基础使用
 * 创建：雨中磐石  from www.rili123.cn
 * 时间：2014-11-28
 * 邮箱：<EMAIL>
 * QQ：290802026/**********
 * */

package com.baselib;


import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.StateListDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.ClipboardManager;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Toast;


import androidx.core.app.ActivityCompat;

import com.rock.xinhuoanew.Xinhu;

import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;


public final class Rock{

	public static SqliteClass Sqlite	= null;
	public static String nowtheme 		= "";
	public static int fontsize 			= 0;
	public static String VERSION 		= "0.0.1";
	public static String defTheme 		= "#1389D3"; 	//默认主题颜色
	public static String APIURL 		= "";			//请求地址
	public static String deviceId 		= "";			//唯一id


	/**
	 * 主界面是否打开
	 * */
	public static Boolean mainOpen 		= false;

	/**
	 * 是否在前台
	 * */
	public static Boolean qianBool 		= false;

	/**
	 * 登录id
	 * */
	public static String adminid 		= "0";

	/**
	 * 登录token
	 * */
	public static String admintoken		= "";

	/**
	 * 极光注册的ID
	 * */
	public static String jpushregid		= "";


	/**
	 * 角标数量
	 * */
	public static int badge				= -1;


	/**
	 * 操作数据库
	 * */
	public static SqliteClass getSqlite(Context context)
	{
		if(Sqlite==null)Sqlite = new SqliteClass(context);
		return Sqlite;
	}

	/**
	 * 判断是否为空字符串
	 * */
	public static boolean isEmpt(String str)
	{
		boolean bo = false;
		if(str == null)bo=true;
		if(!bo)if(str.length()==0 || str == "" || str.equals("") || str.equals("null"))bo = true;
		return bo;
	}

	/**
	 * 判断字符串是否相等
	 * */
	public static boolean equals(String str, String str1)
	{
		if(str==null)str = "";
		if(str1==null)str1 = "";
		return str.equals(str1);
	}

	/**
	 * 判断字符串是否包含
	 * */
	public static boolean contain(String str, String str1)
	{
		boolean  bo = false;
		if(!isEmpt(str) && !isEmpt(str1)){
			if(str.indexOf(str1)>-1){
				bo = true;
			}
		}
		return bo;
	}

	public static Toast toastObj=null;
	public static void Toast(Context context, String s)
	{
		toastHide();
		toastObj = Toast.makeText(context, s, Toast.LENGTH_LONG);
		toastObj.show();
	}

	public static void toastHide()
	{
		if(toastObj!=null)toastObj.cancel();
		toastObj = null;
	}


	public static void alert(Context context, String s)
	{
		new AlertDialog.Builder(context).setTitle("提示")
				.setMessage(s)
				.setPositiveButton("确定", null)
				.show();
	}

	public static String printBundle(Bundle bundle)
	{
		String str 		 = "";
		StringBuilder sb = new StringBuilder();
		for (String key : bundle.keySet()) {
			sb.append(",'" + key +"':'" + bundle.getString(key)+"'");
		}
		str 	= sb.toString();
		if(!isEmpt(str))str = str.substring(1);
		return str;
	}



	/**
	 * 创建视图view
	 * */
	public static View getView(Context context, int viewid)
	{
		LayoutInflater factory = LayoutInflater.from(context);
		View view = factory.inflate(viewid, null);
		return view;
	}

	/**
	 * 复制
	 * */
	public static void copy(Context context, String txt)
	{
		ClipboardManager cm = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
		cm.setText(txt);
	}

	/**
	 * 获取剪切板内容
	 * */
	public static String getcopy(Context context)
	{
		ClipboardManager cm = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
		return cm.getText().toString();
	}


	/**
	 * 设备id
	 * */
	public static String getdeviceId(Context context)
	{
		String str = "";
		str+= Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
		str= str.replaceAll(" ", "");
		str= str.toLowerCase();
		return str;
	}

	/**
	 * 获取品牌
	 * */
	public static String getbrand()
	{
		String str = android.os.Build.BRAND;
		str= str.toLowerCase();
		if(str.equals("honor"))str+="huawei";
		//str= "huawei";
		return str;
	}

	/**
	 * 获取手机型号
	 * */
	public static String getmodel()
	{
		return android.os.Build.MODEL;
	}


	/**
	 * 判断权限
	 * */
	public static Boolean checkPermission(Context context, String qxname)
	{
		int quanx = ActivityCompat.checkSelfPermission(context, qxname);
		if(PackageManager.PERMISSION_GRANTED==quanx){
			return true;
		}else{
			return false;
		}
	}

	/**
	 * 获取Map下的值
	 * @param amap 对应Map
	 * @param key 键值
	 * @param dev 默认值
	 * */
	public static String getMapString(Map<String, String> amap, String key, String dev)
	{
		if(amap==null)return dev;
		String val = amap.get(key);
		if(isEmpt(val))val = dev;
		return val;
	}

	/**
	 * 获取Map下的值
	 * @param amap 对应Map
	 * @param key 键值
	 * */
	public static String getMapString(Map<String, String> amap, String key)
	{
		return getMapString(amap, key, "");
	}

	public static Map<String, String> getMap(String[] params)
	{
		Map<String, String> amap= new HashMap<String, String>();
		if(params!=null){
			int len = params.length;
			for(int i=0;i< len-1;i=i+2){
				amap.put(params[i], params[i+1]);
			}
		}
		return amap;
	}

	/**
	 * 定义一个Map
	 * */
	public static Map<String, String> getMap()
	{
		return getMap(null);
	}

	/**
	 * 打开设置
	 * */
	public static void openAppSettings(Context context)
	{
		Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
		Uri uri = Uri.fromParts("package", context.getPackageName(), null);
		intent.setData(uri);
		try {
			context.startActivity(intent);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 发送Handler
	 * */
	public static void sendHandler(Handler shandler, int what, String result, int arg1)
	{
		Message message = new Message();
		message.what = what;
		if(arg1 >0 )message.arg1 = arg1; //http的状态
		Bundle mBundle = new Bundle();
		mBundle.putString("result", result);
		message.setData(mBundle);
		shandler.sendMessage(message);
	}

	/**
	 * timer定时运行的
	 * */
	public static Timer runTimer(int what, int time, Handler shandler)
	{
		Timer timer = new Timer();
		timer.schedule(new TimerTask() {
			@Override
			public void run() { sendHandler(shandler, what, "", 0); }
		}, time);
		return timer;
	}



	/**
	 * 设置样式圆角
	 * */
	public static void setCornerRadius(View view, long i, String col)
	{
		GradientDrawable drawable = new GradientDrawable();
		drawable.setCornerRadius(i);
		drawable.setColor(Color.parseColor(col));
		view.setBackground(drawable);
	}

	/**
	 * 设置动态按钮样式
	 * */
	public static void setBackground(View view,long i, String col)
	{
		if(isEmpt(col))col = defTheme;
		StateListDrawable drawable 	= new StateListDrawable();
		GradientDrawable normal 	= new GradientDrawable();
		normal.setColor(Color.parseColor(col));
		normal.setCornerRadius(i);
		drawable.addState(new int[]{-android.R.attr.state_pressed}, normal); //默认

		GradientDrawable xuan = new GradientDrawable();
		xuan.setAlpha(220);
		xuan.setColor(Color.parseColor(col));
		xuan.setCornerRadius(i);
		drawable.addState(new int[]{android.R.attr.state_pressed}, xuan);
		view.setBackground(drawable);
	}
	public static void setBackground(View view) { setBackground(view, 20, ""); }


	/**
	 * 用默认浏览器打开地址
	 * */
	public static void openView(Context context, String url)
	{
		Intent intent = new Intent();
		intent.setAction("android.intent.action.VIEW");
		intent.setData(Uri.parse(url));
		context.startActivity(intent);
	}


	/**
	 * 获取浏览地址
	 * */
	public static String getAppHtml()
	{
		String url = ""+ AA.SHOWURL+"index.html";
		if(AA.DEBUG)url = ""+ AA.SHOWURL_DEBUG+"index.html";
		return url;
	}

	/**
	 * 获取api地址
	 * */
	public static String getApiUrl(String m, String a)
	{
		String url = APIURL;
		if(isEmpt(url)) {
			url = AA.APIURL;
			if (AA.DEBUG) url = AA.APIURL_DEBUG;
		}
		if(!isEmpt(m) && !isEmpt(a))url+="api.php?m="+m+"&a="+a+"&cfrom=nppandroid";
		return url;
	}

	/**
	 * 获取api地址
	 * */
	public static String getApiUrl()
	{
		return getApiUrl("","");
	}


	/**
	 * 获取api地址，带token
	 * */
	public static String getApiUrls(String m, String a)
	{
		String url = getApiUrl(m,a);
		url += "&adminid="+adminid+"&token="+admintoken+"";
		return url;
	}

	/**
	 * 给头像加地址
	 * */
	public static String getFace(String face, String url)
	{
		if(isEmpt(url))url = getApiUrl();
		if(!face.substring(0,4).equals("http"))face = url+face;
		return face;
	}
	public static String getFace(String face){return getFace(face, "");}

	/**
	 * 停顿
	 * */
	public static void Sleep(long time)
	{
		try { Thread.sleep(time); } catch (InterruptedException e) { }
	}
}