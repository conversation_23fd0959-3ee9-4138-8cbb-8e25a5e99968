<script>
$(document).ready(function(){
	var loginyzm = '';
	var c = {
		web:'',
		temptoken:'',
		loginsubmit:function(o){
			if(js.ajaxbool)return false;
			var user = '',pass = '';
			js.setmsg('');
			var ltype = get('logintype').value;
			if(this.temptoken==''){
				if(ltype=='0'){
					user = get('adminuser').value,pass = get('adminpass').value;
					if(user==''){
						rockalert('用户名不能为空', function(){get('adminuser').focus()});
						return false;
					}
					if(pass==''){
						rockalert('密码不能为空', function(){get('adminpass').focus()});
						return false;
					}
				}else{
					user = get('adminmobile').value;
					if(user==''){
						js.msg('msg','手机号不能为空');
						get('adminmobile').focus();
						return false;
					}
					js.setoption('adminmobile', user);
					this.setlogininfo({adminmobile:user});
					loginyzm = get('adminmobileyzm').value;
					if(loginyzm=='' || loginyzm.length!=6){
						js.msg('msg','手机验证码格式不对');
						get('adminmobileyzm').focus();
						return false;
					}
				}
				js.setoption('adminpass', pass);
				js.setoption('adminuser', user);
				this.setlogininfo({adminpass:pass,adminuser:user});
			}else{
				user = get('adminuser').value;
			}
			this.yangzhenback=false;
			var web = '';
			if(apixhbool){
				web = 'app2023'+api.deviceName+''+api.deviceModel+'';
				web = strreplace(web.toLowerCase());
				api.showProgress();
			}
			o.innerHTML='登录中...';
			o.disabled=true;
			xcy.initPushBool=false;
			var spd = {user:jm.base64encode(user),token:this.temptoken,web:web,device:device,cfrom:CFROM,pass:jm.base64encode(pass),ltype:ltype,yanzm:loginyzm};
			loginyzm = '';
			js.ajax('login|check|none',spd, function(ret){
				c.temptoken='';
				o.innerHTML='登录成功';
				c.loginsuccess(ret.data);
			},'post',function(str,code, ret){
				if(apixhbool)api.hideProgress();
				c.temptoken='';
				o.innerHTML='登录';
				o.disabled=false;
				if(ret){
					var a = ret.data;
					if(a && a.shouji){
						mobilejsho = a.mobile;
						js.wx.prompt('输入手机验证码','手机号：'+a.shouji+'&nbsp;<span><a class="r-zhu" href="javascript:;" onclick="getcodes{rand}(this)">[获取验证码]</a></span>',function(txt){
							if(txt){
								loginyzm = txt;
								c.loginsubmit(o);
							}
						});;
					}
				}
			});
			return false;
		},
		init:function(){
			if(apixhbool && !js.getoption('apiurl')){
				var nurl = api.apiUrl;
				if(nurl)apiurl = nurl;
			}
			TOKEN = '';
			var dlinfo = this.getlogininfo();
			if(dlinfo){
				var logesr=['adminpass','adminmobile','adminface','adminuser','title','logintype'],val1;
				for(var i=0;i<logesr.length;i++){
					val1 = dlinfo[logesr[i]];
					if(!val1)val1='';
					js.setoption(logesr[i], val1);
				}
			}
			var us=js.getoption('adminuser');
			if(us)$('#adminuser').val(us);
			$('#adminpass').val(js.getoption('adminpass'));
			$('#adminmobile').val(js.getoption('adminmobile'));
			var face = js.getoption('adminface');
			if(face)get('myface').src=face;
			if(logincogbool)$('#logincogbtn').show();
			$('#loginstring').html(js.getoption('loginfooter', loginstring));
			$('#footerdiv').hide();
			$('#homemenubtn').hide();
			
			this.changedys();
			this.yangzhen('加载中...');
			clearTimeout(xcy.homeretime);
			
			if(logincogbool){
				var str = '<span clickevt="setcogurl" class="r-position-right r-header-btn"><i class="icon-cog"></i></span>';
				$('#header_show').append(str);
			}
			
			var str = '<span clickevt="saoyisao" class="r-position-left r-header-btn"><i class="icon-qrcode"></i></span>';
			$('#header_show').append(str);
		},
		setlogininfo:function(cans, gbo){
			var gsstr = js.getoption('loginjson');
			if(!gsstr)gsstr='[{"url":"'+apiurl+'","title":"'+systemtitle+'"}]';
			var da 	  = JSON.parse(gsstr);
			this.data = da;
			var ds=[],i,d1,i1,czb=false,d;
			for(i=0;i<da.length;i++){
				d1 = da[i];
				if(!d1.isdel){
					if(d1.url==apiurl){
						for(i1 in cans)d1[i1]=cans[i1];
						czb = d1;
					}
					ds.push(d1);
				}
			}
			if(!czb){
				cans.url = apiurl;
				if(!cans.title)cans.title = systemtitle;
				ds.push(cans);
			}
			if(gbo)return czb;
			js.setoption('loginjson', JSON.stringify(ds));
		},
		getlogininfo:function(){
			return this.setlogininfo({geyxt:1}, true);
		},
		loginsuccess:function(d){
			
			TOKEN = d.token;
			adminid = d.uid;
			js.setoption(TOKENKEY, TOKEN);
			js.setoption('adminface', d.face);
			js.setoption('adminname',d.name);
			js.setoption('adminzhan',d.user);
			js.setoption('deptallname',d.deptallname);
			js.setoption('ranking',d.ranking);
			js.setoption('adminid',d.uid);
			js.setoption('showmyinfo','');
			get('myface').src=d.face;
			this.setlogininfo({adminface:d.face,adminzhan:d.user,deptallname:d.deptallname,adminname:d.name,ranking:d.ranking});
			//js.wx.msgok('登录成功');
			js.reload();
		},
		saoyisao:function(){
			if(!apixhbool)return;
			this.yangzhenback=false;
			
			api.openScan({
				msg:'扫描PC上打开的二维码快捷登录'
			},function(ret){
				c.saoyisaook(ret.result);
			});
		},
		saoyisaook:function(nr){
			if(nr.indexOf('d=we')<0){
				js.wx.msgerror('错误的二维码');
				return;
			}
			var nar = nr.split('?');
			var url = nar[0];
			this.setapiurl(url);
			var user = js.request('user','', nr);
			var toke = js.request('token','', nr);
			var us = jm.base64decode(user);
			js.setoption('adminuser', us);
			this.setlogininfo({adminuser:us});
			$('#adminuser').val(us);
			this.temptoken = toke;
			this.yangzhenback=function(){
				this.loginsubmit(get('loginbtn'));
			}
		},
		setapiurl:function(dz){
			if(!dz)return;
			if(dz.substr(0,4)!='http')dz='http://'+dz;
			if(dz.substr(-1)!='/')dz+='/';
			if(apiurl!=dz)js.setoption('nowtheme','');
			apiurl = dz;
			this.yangzhen('验证中...');
		},
		yangzhenback:false,
		yangzhen:function(str){
			if(str)js.loading(str);
			js.ajax('login|appinit|none', false, function(ret){
				var da = ret.data;
				c.setlogininfo(da);
				js.setoption('title', da.title);
				js.setoption('apiurl', apiurl);
				js.setoption('logintype', da.loginyzm); //登录方式
				
				if(da.apptheme){
					js.setoption('nowtheme', da.apptheme);
					if(apixhbool)api.rockFun('setOption',{value:da.apptheme,key:'nowtheme'});
					xcy.showtheme(da.apptheme);
				}
				if(apixhbool){
					api.rockFun('setOption',{value:apiurl,key:'apiurl'});
					api.rockFun('setOption',{value:da.title,key:'title'});
				}
				
				c.changedys();
				if(c.yangzhenback)c.yangzhenback();
			},'get', function(st){
				js.msgerror('验证失败：“'+apiurl+'”无法访问');
			});
		},
		changedys:function(){
			var lx = js.getoption('logintype');
			if(lx=='1' || lx=='2'){
				$('#yzmdengdiv').show();
			}else{
				$('#yzmdengdiv').hide();
			}
			if(lx=='3'){
				$('#loginview0').hide();
				$('#loginview1').show();
				get('logintype').value='1';
			}
		},
		setcogurl:function(){
			//setcogurl();
			xcy.opennei({name:'登录设置','url':'server',nlogin:true});
		}
	}

	c.init();
	js.initbtn(c);

	getcodes{rand}=function(o1){
		var da = {'mobile':mobilejsho,'device':device};
		var o2 = $(o1).parent();
		o2.html(js.getmsg('获取中...'));
		js.ajax('yanzm|index',da, function(da){
			o2.html(js.getmsg('获取成功','green'));
		},'get',function(str, ret){
			o2.html(js.getmsg(str));
		});
	}
	getyzm{rand}=function(o1){
		mobilejsho = get('adminmobile').value;
		if(!mobilejsho){
			js.msg('msg','请输入手机号');
			get('adminmobile').focus();
			return;
		}
		var da = {'mobile':mobilejsho,'device':device};
		o1.value = '获取中...';
		js.setmsg();
		o1.disabled=true;
		js.ajax('yanzm|glogin',da, function(da){
			o1.value = '获取成功';
			js.msg('success', '验证码已发送到手机上');
			dshitime(60, o1);
		},'get',function(str, ret){
			o1.value = '重新获取';
			o1.disabled=false;
			js.setmsg(str);
		});

	}
	function dshitime(sj,o1){
		if(sj==0){
			o1.disabled=false;
			o1.value='重新获取';
			return;
		}
		o1.disabled=true;
		o1.value=''+sj+'';
		setTimeout(function(){dshitime(sj-1, o1)},1000);
	}

	changlogin=function(o1){
		if(js.ajaxbool)return;
		var ltype = get('logintype').value;
		if(ltype=='0'){
			$(o1).html('帐号密码登录');
			get('logintype').value='1';
			$('#loginview0').hide();
			$('#loginview1').show();
		}else{
			$(o1).html('验证码登录');
			get('logintype').value='0';
			$('#loginview1').hide();
			$('#loginview0').show();
		}
	}

	xcy.initApp=function(){
		initAppbool = true;
		if(api.addBiaoti)api.addBiaoti('＋');
		api.setMenu({menu:'设置登录地址,扫一扫,APP设置'},function(ret){
			if(ret.menuIndex==1){
				c.setcogurl();
			}
			if(ret.menuIndex==2){
				c.saoyisao();
			}
			if(ret.menuIndex==3){
				api.rockFun('openCog');
			}
		});
		api.addEventListener({
			name: 'rocklogin'
		}, function(ret, err) {
			if(ret.name=='rocklogin'){
				if(ret.stype=='shuaxin')js.reload();
			}
		});
	};
});
</script>
<div style="padding:1em 0; text-align:center;margin-top:10px">
	<div><img id="myface" style="height:100px;width:100px;border-radius:50%" onclick="location.reload()" src="images/noface.png"></div>
</div>
<input type="hidden" id="logintype" value="0">
<div class="rock_cells rock_cells-form">
	<div id="loginview0">
		<div class="rock_cell">
			<div class="rock_cell__hd"><label style="width:80px" class="rock_label fontsize">用户名</label></div>
			<div class="rock_cell__bd rock_cell-primary">
				<input class="rock_input fontsize" onfocus="$('#loginfooter').hide()" autocomplete="off" onclick="this.focus()" type="text" value="" id="adminuser" onkeyup="if(event.keyCode==13)get('adminpass').focus()" maxlength="100" placeholder="用户名/姓名/手机号"/>
			</div>
		</div>

		<div class="rock_cell">
			<div class="rock_cell__hd"><label style="width:80px" class="rock_label fontsize">密码</label></div>
			<div class="rock_cell__bd rock_cell-primary">
				<input class="rock_input fontsize" onfocus="$('#loginfooter').hide()" autocomplete="off" onclick="this.focus()" id="adminpass" type="password" onkeyup="if(event.keyCode==13)get('loginbtn').click()"  placeholder="请输入密码"/>
			</div>
		</div>
	</div>
	<div id="loginview1" style="display:none">
		<div class="rock_cell">
			<div class="rock_cell__hd"><label style="width:80px" class="rock_label fontsize">手机号</label></div>
			<div class="rock_cell__bd rock_cell-primary">
				<input class="rock_input fontsize"  onfocus="$('#loginfooter').hide()" autocomplete="off" onclick="this.focus()" type="text" id="adminmobile"  onkeyup="if(event.keyCode==13)get('adminmobileyzm').focus()" maxlength="11" placeholder="请输入手机号"/>
			</div>
		</div>
		<div class="rock_cell">
			<div class="rock_cell__hd"><label style="width:80px" class="rock_label fontsize">验证码</label></div>
			<div class="rock_cell__bd rock_cell_primary">
				<input class="rock_input fontsize" onfocus="$('#loginfooter').hide()" autocomplete="off" onclick="this.focus()"  onkeyup="if(event.keyCode==13)get('loginbtn').click()" id="adminmobileyzm" maxlength="6" placeholder="请输入验证码"/>
			</div>
			<div class="rock_cell__ft">
                <input class="webbtn" style="font-size:16px" onclick="getyzm{rand}(this)" type="button"  value="获取">
            </div>
		</div>
	</div>
</div>

<div align="center" style="margin-top:24px">
	<button class="webbtn webbtn_big fontsize" id="loginbtn" type="button" clickevt="loginsubmit">登录</button>
</div>

<div align="center" id="yzmdengdiv" style="padding:10px 0px;display:none;margin-top:15px"><a href="javascript:;" onclick="changlogin(this)" class="r-zhu">验证码登录</a></div>


<div id="loginfooter" style="display:none" class="rock_footer rock_footer_fixed-bottom">
	<p class="rock_footer__text" id="loginstring" style="color:#cccccc"></p>
</div>
