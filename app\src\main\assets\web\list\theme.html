<script>
$(document).ready(function(){
	{params}
	
	var c = {
		defcolor:'#1389D3',
		init:function(){
			nowtheme = js.getoption('nowtheme');
			var nwex = ''+nowtheme+'';
			if(!nwex)nwex=this.defcolor;
			var zys = ['#1389D3','#99cc66','#003366','#6666CC','#CC3333','#009966','#333333'],str='',oi,sel='',xzl=false;
			for(var i=0;i<zys.length;i++){
				sel = '';
				if(zys[i]==nwex){sel='checked';xzl=true;}
				oi = i+1;
				str+='<label onclick="xuanzhe(\''+zys[i]+'\')" class="rock_cell rock_cell_active rock_check__label" for="ys'+oi+'">'+
					'<div class="rock_cell__hd">'+
					'	<input type="radio" name="checkbox1" '+sel+' class="rock_check" id="ys'+oi+'">'+
					'	<i class="rock_icon-checked"></i>'+
					'</div>'+
					'<div class="rock_cell__bd">'+
					'	<p class="fontsize">主题'+oi+'<font style="font-size:20px" color="'+zys[i]+'">■</font></p>'+
					'</div>'+
				'</label>';
			}
			$('#listdiv').append(str);
			if(nwex=='def'){
				get('ys0').checked=true;
			}else if(!xzl){
				get('ysa').checked=true;
			}
		},
		loginsubmit:function(){
			if(nowtheme==this.defcolor)nowtheme='';
			js.setoption('nowtheme', nowtheme);
			js.msgok('需要重启才能生效');
			if(apixhbool){
				xcy.sendEvent('shuaxin');
			}
		}
	}
	c.init();
	js.initbtn(c);
	
	xuanzhe = function(lx){
		nowtheme = lx;
		if(lx)xcy.showtheme(lx);
	}
	zidingyi=function(){
		rockprompt('自定义主题','请输入颜色值深色值#开头：',function(txt){
			if(txt){
				xuanzhe(txt);
			}
		},nowtheme);
	}
});
</script>

<div class="rock_cells__title">根据自己喜欢选择主题</div>
<div class="rock_cells__group rock_cells__group_form">
<div class="rock_cells rock_cells_checkbox" id="listdiv">
	<label class="rock_cell rock_cell_active rock_check__label" for="ysa">
		<div class="rock_cell__hd" >
			<input type="radio" class="rock_check" onclick="zidingyi()" name="checkbox1" id="ysa">
			<i class="rock_icon-checked"></i>
		</div>
		<div class="rock_cell__bd fontsize">
			自定义
		</div>
	</label>
</div>

<div align="center" style="margin-top:20px">
  <button class="webbtn webbtn_big fontsize" clickevt="loginsubmit" type="button">保存</button>
</div>

</div>
    

<div class="blank15"></div>