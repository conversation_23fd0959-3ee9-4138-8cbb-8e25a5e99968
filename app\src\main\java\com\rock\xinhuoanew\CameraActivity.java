package com.rock.xinhuoanew;


import android.Manifest;
import android.content.ContentResolver;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.view.View;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.ImageView;


import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.FileProvider;

import com.baselib.AA;
import com.baselib.AR;
import com.baselib.CDate;
import com.baselib.CLog;
import com.baselib.Image;
import com.baselib.Jiami;
import com.baselib.Rock;
import com.baselib.RockFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Locale;


public class CameraActivity extends ARockActivity {

    protected  ImageView  imgView;
    protected  String  filepath,callbackstr,sourceType,filepaths="";
    protected CheckBox checkBox;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_camera);
        findViewById(R.id.back).setOnClickListener(OnViewClickListener);

        View tv = findViewById(R.id.sendbtn);
        tv.setOnClickListener(OnViewClickListener);
        Rock.setBackground(tv);


        Bundle bundle = this.getIntent().getExtras();
        callbackstr   = bundle.getString("callbackstr");
        sourceType    = bundle.getString("sourceType");

        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
        imgView     = (ImageView) findViewById(R.id.ImageControl);
        checkBox    = (CheckBox) findViewById(R.id.yuanbtn);


        if(sourceType.equals("camera")) {
            Boolean bo = Rock.checkPermission(this, Manifest.permission.CAMERA);
            if (!bo) {
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, AA.PERMISSION_CAMERA);
            } else {
                openCameras();
            }
        }else{
            openAlbum();
        }
    }

    /**
     * 打开相机
     * */
    protected void openCamera()
    {
       // Intent intent1         = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
       // startActivityForResult(intent1, AA.RESULTCODE_CHANGECAMERA);
    }

    protected View.OnClickListener OnViewClickListener = new View.OnClickListener() {
        public void onClick(View v) {
            onViewClick(v);
        }
    };

    protected void onViewClick(View v) {
        int id = v.getId();
        if(id == R.id.back){
            finish();
        }
        if(id == R.id.sendbtn){
            sendCamera();
        }
    }

    /**
     * 拍照
     * */
    public Uri imageUri = null;
    public void openCameras()
    {
        Intent intent1         = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        String cappath         = RockFile.getCachedir(this, "camera");
        String capfname        = "cap"+ CDate.gettime()+".jpg";
        File file              = new File(cappath, capfname);
        filepath = ""+cappath+"/"+capfname+"";
        if (android.os.Build.VERSION.SDK_INT >= 24) {
            imageUri = FileProvider.getUriForFile(this, getPackageName() + ".fileprovider", file);
        }else{
            imageUri = Uri.fromFile(file);
        }
        intent1.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
        startActivityForResult(intent1, AA.RESULTCODE_CHANGECAMERB);
    }

    /**
     * 打开相册
     * */
    public void openAlbum()
    {
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");
        startActivityForResult(intent, AA.RESULTCODE_CHANGEALBUM);
    }

    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults){
        super.onRequestPermissionsResult(requestCode,permissions,grantResults);
        if(requestCode==AA.PERMISSION_CAMERA){
            int len = permissions.length;
            if(grantResults[0]== PackageManager.PERMISSION_DENIED){
                Rock.Toast(this, "拒绝了权限，无法使用拍照功能");
            }
            if(grantResults[0]== PackageManager.PERMISSION_GRANTED){
                openCameras();
            }
        }
    }

    protected void onActivityResult(int requestCode, int resultCode, Intent data){
        super.onActivityResult(requestCode, resultCode, data);
        //拍照返回
        if(resultCode == RESULT_OK){
            if(requestCode==AA.RESULTCODE_CHANGECAMERA){
                Bundle bundle = data.getExtras();// 从data中取出传递回来缩略图的信息，图片质量差，适合传递小图片
                Bitmap bitmap = (Bitmap) bundle.get("data");
                showimgsss(bitmap,0);
                return;
            }
            if(requestCode==AA.RESULTCODE_CHANGECAMERB){
                try {
                    Bitmap bitmap= BitmapFactory.decodeStream(getContentResolver().openInputStream(imageUri));
                    showimgsss(bitmap,1);
                    return;
                } catch (FileNotFoundException e) {
                    Rock.Toast(this,"无法读取图像");
                    e.printStackTrace();
                }
            }
            if(requestCode==AA.RESULTCODE_CHANGEALBUM){
                try {
                    Uri uri  = data.getData();
                    filepath = uri.getPath();
                    fosfrom  = getContentResolver().openInputStream(uri);
                    Bitmap bitmap = BitmapFactory.decodeStream(getContentResolver().openInputStream(uri));
                    showimgsss(bitmap,2);
                    return;
                } catch (FileNotFoundException e) {
                    Rock.Toast(this,"无法读取相册");
                    e.printStackTrace();
                }
            }
        }
        finish();
    }
    private InputStream fosfrom;
    private void showimgsss(Bitmap bitmap, int gcode)
    {
        imgView.setVisibility(View.VISIBLE);
        if(gcode==0){
            imgView.setImageBitmap(bitmap);
        }
        if(gcode==1){
            imgView.setImageBitmap(bitmap);
            String filesizecn = RockFile.getFilesizecn(filepath);
            checkBox.setText("发送原图("+filesizecn+")");
        }
        if(gcode==2){
            String cappath         = RockFile.getCachedir(this, "camera");
            String capfname        = RockFile.getFilename(filepath);
            if(!RockFile.isimg(RockFile.getExt(capfname)))capfname+=".jpg";
            filepath               = ""+cappath+"/"+capfname+"";
            File toFile            = new File(cappath, capfname);
            if(!toFile.exists() && 1==1) {
                try {
                    FileOutputStream fosto = new FileOutputStream(toFile);
                    byte[] bt = new byte[1024];
                    int c;
                    while ((c = fosfrom.read(bt)) > 0) {
                        fosto.write(bt, 0, c);
                    }
                    fosfrom.close();
                    fosto.close();
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(toFile.length()>5*1024*1024){
                filepaths = filepath.replace(".","_s.");
                File sfile = new File(filepaths);
                if(!sfile.exists())filepaths = Image.squashSize(filepath);
                BitmapFactory.Options newOpts = new BitmapFactory.Options();
                newOpts.inSampleSize = 1;
                bitmap = BitmapFactory.decodeFile(filepaths, newOpts);
            }
            imgView.setImageBitmap(bitmap);
            String filesizecn = RockFile.getFilesizecn(filepath);
            checkBox.setText("发送原图("+filesizecn+")");
        }
        ;
    }

    /**
     * 发送图片
     * */
    private void sendCamera()
    {
        Long size = RockFile.getFilesize(filepath);
        if(Rock.isEmpt(filepaths)){
            if(size>500*1024) {
                filepaths = Image.squashSize(filepath);
            }else{
                filepaths = filepath;
            }
        }
        if(!checkBox.isChecked()){
            filepath = filepaths;
            size = RockFile.getFilesize(filepath);
        }
        Intent result = new Intent();
        result.putExtra("filepath", filepath);
        result.putExtra("filepaths", filepaths);
        result.putExtra("sourceType", sourceType);
        result.putExtra("callbackstr", callbackstr);
        result.putExtra("filesize", size+"");
        setResult(RESULT_OK, result);
        finish();
    }
}
