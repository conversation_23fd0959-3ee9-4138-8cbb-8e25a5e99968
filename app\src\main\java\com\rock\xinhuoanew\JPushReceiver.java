package com.rock.xinhuoanew;

import android.content.Context;

import com.baselib.AA;
import com.baselib.CLog;
import com.baselib.Jiami;
import com.baselib.Json;
import com.baselib.RCache;
import com.baselib.RCall;
import com.baselib.Rock;
import com.baselib.RockHttp;

import java.util.Map;

import cn.jpush.android.api.CustomMessage;
import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.api.NotificationMessage;
import cn.jpush.android.local.JPushAction;
import cn.jpush.android.service.JPushMessageReceiver;

public class JPushReceiver extends JPushMessageReceiver {

    @Override
    public void onNotifyMessageArrived(Context context, NotificationMessage notificationMessage) {
        super.onNotifyMessageArrived(context, notificationMessage);
        CLog.error("收到推送："+notificationMessage.toString());
    }

    @Override
    public void onMessage(Context context, CustomMessage customMessage) {
        getRegid(context);
        //CLog.error("收到自定义："+customMessage.toString());
        String  data    = customMessage.message;

        if(Rock.contain(data, "calltonghua")) {
            if(XinhuService.mainOpenBool) {
                Xinhu.sendBroadcast(context, AA.TONGHUA_ACTION, data);
            }else{
                RCall.message(context, data);
            }
            return;
        }

        Map<String, String> da = Json.getJsonObject(data);
        String title    = Rock.getMapString(da, "title");
        String cont     = Rock.getMapString(da, "cont");
        String callurl  = Rock.getMapString(da, "callurl");

        if(!Rock.isEmpt(title) && !Rock.isEmpt(cont)){
            String msgtype   = Rock.getMapString(da, "msgtype");
            String channel   = Rock.getMapString(da, "channel");
            if(Rock.equals(msgtype, "call")){
                Xinhu.Notification(context,
                        Jiami.base64decode(title),
                        Jiami.base64decode(cont), "音视频通话来电", 0, 60);
            }else {
                Xinhu.Notification(context,
                        Jiami.base64decode(title),
                        Jiami.base64decode(cont), Jiami.base64decode(channel), 0, 0);
            }
        }
        //收到消息标识我已收到了
        if(!Rock.isEmpt(callurl)){
            String url = Jiami.base64decode(callurl)+"&regid="+Rock.jpushregid+"&state=1";
            RockHttp.get(url, null, 0);
        }
    }

    @Override
    public void onConnected(Context context, boolean b) {
        super.onConnected(context, b);
        if(b){
            CLog.debug("极光已链接");
            sendRegid(context, "on");
        }else{
            CLog.error("未链接");
        }
    }

    @Override
    public void onRegister(Context context, String s) {
        super.onRegister(context, s);
        sendRegid(context, "on");
    }

    //注册成功
    private void sendRegid(Context context, String lay)
    {
        getRegid(context);
        Xinhu.sendBroadcastApi(context, "rockhome", "jpush", ",pushstate:'jpush',regid:'"+Rock.jpushregid+"'");
    }

    /**
     * 获取regid
     * */
    public static String getRegid(Context context)
    {
        if(!Rock.isEmpt(Rock.jpushregid))return Rock.jpushregid;
        String regid    = JPushInterface.getRegistrationID(context);
        RCache.save(context, "regid", regid);
        Rock.jpushregid = regid;
        return regid;
    }

    /**
     * 获取regids
     * */
    public static String getRegids(Context context)
    {
        if(Rock.isEmpt(Rock.jpushregid))Rock.jpushregid = RCache.get(context, "regid");
        return Rock.jpushregid;
    }

    /**
     * 注册推送
     * */
    public static Boolean regJPush(Context context)
    {
        String jkey     = Xinhu.getBuildString(context, "JPUSH_APPKEY");
        if(Rock.isEmpt(jkey))return false;
        JPushInterface.setDebugMode(false);
        JPushInterface.init(context);
        Boolean bo = JPushInterface.isPushStopped(context);
        if(bo)JPushInterface.resumePush(context);
        return true;
    }

    /**
     * 判断是否推送
     * */
    public static Boolean regRJPush(Context context)
    {
        String jkey     = Xinhu.getBuildString(context, "JPUSH_APPKEY");
        if(Rock.isEmpt(jkey))return false;
        Boolean bo = JPushInterface.isPushStopped(context);
        if(bo || Rock.isEmpt(Rock.jpushregid))regJPush(context);
        return true;
    }

    /**
     * 停止推送
     * */
    public static void unregPush(Context context)
    {
        JPushInterface.stopPush(context);
    }
}
