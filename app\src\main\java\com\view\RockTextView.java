package com.view;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;

import com.baselib.Rock;


public class RockTextView extends androidx.appcompat.widget.AppCompatTextView {

    public RockTextView(Context context) {
        super(context);
    }

    public RockTextView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public RockTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setSizedx();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
    }

    //设置字体大小
    private void setSizedx(){
        float size = 18;
        if(Rock.fontsize==1)size = 20;
        if(Rock.fontsize==2)size = 22;
        if(size>0)this.setTextSize(size);
    }
}