<script>
$(document).ready(function(){

	var c = {
		init:function(){
			this.reloadss();
			
			this.showagent(xcy.agentarr);
			xcy.showqipao('historyjson', '0');
		},
		reloadss:function(){
			js.ajax('indexreim|mwebinit',false,function(da){
				var ret = da.data;
				js.setoption('agentjson', ret.agentjson);
				js.setoption('historyjson', ret.historyjson);
				
				xcy.agentarr = js.decode(ret.agentjson);
				c.showagent(xcy.agentarr);
				xcy.historyarr = js.decode(ret.historyjson);
				
				xcy.showqipao('historyjson', '0');
				xcy.reloadok();
			}, 'get', function(){
				xcy.showerror();
			});
		},
		showagent:function(a){
			var i,len=a.length,d,oshow,typearr={},s,ois=0,str='',j,zs=yingshu;
			oshow = $('#main_index1');
			oshow.html('');
			for(i=0;i<len;i++){
				d 	= a[i];
				d.i = i;
				if(!typearr[d.types])typearr[d.types]=[];
				typearr[d.types].push(d);
			}
			for(ty in typearr){
				ois++;j=0;
				len = typearr[ty].length;
				s = '<div style="color:#888888;padding:10px 10px 5px 10px;font-size:14px;">'+ty+'</div><div style="border-bottom:0.5px '+bordercolor+' solid;border-top:0.5px '+bordercolor+' solid"><table bgcolor="white" border="0" id="agentshow_'+ois+'" width="100%"></table></div>';
				oshow.append(s);
				str='<tr>';
				for(i=0;i<len;i++){
					j++;
					str+=this.showagents(typearr[ty][i],zs,j,len);
					if(j%zs==0)str+='</tr><tr>';
				}
				if(j<zs)str+='<td>&nbsp;</td>';
				str+='</tr>';
				$('#agentshow_'+ois+'').html(str);
			}
			this.changestotl('agentstotal_',1);
		},
		changestotl:function(jg,lx){
			if(!jg)jg='histotal_';
			var o=$("span[id^='"+jg+"']"),oi=0,i,len=o.length,v1;
			for(i=0;i<len;i++){
				v1=$(o[i]).text();
				if(v1=='')v1='0';
				oi=oi+parseFloat(v1);
			}
			if(oi==0)oi='';
			$('#stotal_ss'+lx+'').html(''+oi);
		},
		showagents:function(d,zs,j,len){
			var rnd= 'a'+js.getrand();
			xcytouch[rnd] = new touchclass({
				receid:d.id,num:d.num,
				onlongclick:function(){c.showagentsmenu(this.num,this.receid,this.obj);},
				onclick:function(){c.openagent(this.receid);}
			});
			var s='',hei=34,col=bordercolor,w=100/zs,bt='.5',br='.5',sy=len%zs;
			if(j%zs==0)br='0';
			if(sy>0 && len-j<sy)bt='0';
			if(sy==0 && len-j<zs)bt='0';
			if(zs<4)hei=40;
			var s1=d.stotal;if(s1==0)s1='';
			s='<td class="actives" ontouchstart="return xcytouch.'+rnd+'.touchstart(this,event)" style="width:'+w+'%;padding:16px 5px;position:relative;border-bottom:'+bt+'px '+col+' solid;border-right:'+br+'px '+col+' solid" align="center">';
			s+='<div style="height:'+hei+'px;overflow:hidden; ">';
			s+='	<img src="'+xcy.getface(d.face)+'" style="border-radius:6px;height:'+hei+'px;width:'+hei+'px">';
			s+='	<span style="position:absolute;top:3px;right:3px;" id="agentstotal_'+d.id+'" class="rock_badge">'+s1+'</span>';
			s+='</div>';
			s+='<div style="font-size:16px;margin-top:3px;">';
			s+=' '+d.name+'';
			s+='</div>';
			s+='</td>';
			agentarr[d.id]=d;
			return s;
		},
		openagent:function(yyid){
			var d = agentarr[yyid];
			var url = d.urlm;
			if(isempt(url))url = 'index.php?m=ying&d=we&num='+d.num+'';
			$('#agentstotal_'+d.id+'').html('');
			this.changestotl('agentstotal_',1);
			xcy.openurls(url+'&yingnum='+d.num+'',d.name);
		},
		showagentsmenu:function(num,id,o1){
			var menu = '打开';
			var myhomenum = ','+js.getoption('myhomenum')+',',bo=false;
			if(myhomenum.indexOf(','+num+',')>-1){
				menu+=',取消首页显示|'+deletecolor+'';
			}else{
				menu+=',添加到首页显示';
				bo = true;
			}
			if(apixhbool)api.createMenu({
				menu:menu
			},function(ret){
				if(ret.menuIndex==0)c.openagent(id);
				if(ret.menuIndex==1){
					js.ajax('indexreim|shecyy',{yynum:num},function(ret){
						api.toast({msg:ret.data.msg});
						c.addhomenum(num, bo);
					});
				}
			});
		},
		addhomenum:function(bh,bo){
			var str = js.getoption('myhomenum');
			if(bo){
				if(str)str+=',';
				str+=bh;
			}else{
				str = str.replace(','+bh,'');
				str = str.replace(bh,'');
			}
			js.setoption('myhomenum',str);
		}
	}
	c.init();
	xcy.touchload['ying']=function(){
		c.reloadss();
	}
	yy{rand} = c;
});
</script>
<div id="main_index1"></div>