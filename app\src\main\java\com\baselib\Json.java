/**
 * 说明：json解析
 * 创建：雨中磐石  from www.rili123.cn
 * 时间：2014-11-28
 * 邮箱：<EMAIL>
 * QQ：290802026/1073744729
 * */

package com.baselib;


import android.telecom.Call;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;






public final class Json{
	
	/**
	 * Json解析，解析字符串：[{"key":"value"},{},{}];
	 * return
	 * */
	public static List<Map<String, String>> getJsonArray(String str, String fields[])
	{
		ArrayList<Map<String, String>> jsonlist = new ArrayList<Map<String, String>>();
		if(Rock.isEmpt(str) || !str.substring(0,1).equals("["))return jsonlist;
		try{
			JSONArray json = new JSONArray(str);
			for (int i = 0; i < json.length(); i++) { 
				JSONObject data		= json.getJSONObject(i);		
				Map<String, String> newScanMap= new HashMap<String, String>();
				for(String fieldss:fields)newScanMap.put(fieldss, "");		
				Iterator<?> it 		= data.keys(); 
				String keys 		= "";
				String valus		= "";
				while(it.hasNext()){
					keys = (String) it.next().toString();
					valus= data.getString(keys);
					if(valus=="null")valus="";
					newScanMap.put(keys, valus);	
	            } 
				jsonlist.add(newScanMap);
			}
		} catch (JSONException e) {

		}
		return jsonlist;
	}
	public static List<Map<String, String>> getJsonArray(String str)
	{
		return getJsonArray(str, new String[]{});
	}


	/**
	 * Json解析，解析字符串：{"key":"value"};
	 * return
	 * */
	public static Map<String, String> getJsonObject(String str)
	{
		Map<String, String> newScanMap= new HashMap<String, String>();
		if(!isJson(str))return newScanMap;
		try{
			JSONObject json = new JSONObject(str);
			Iterator<?> it 		= json.keys();
			String keys 		= "";
			String valus		= "";
			while(it.hasNext()){
				keys = (String) it.next().toString();
				valus= json.getString(keys);
				if(valus=="null")valus="";
				newScanMap.put(keys, valus);
			}
		} catch (JSONException e) {

		}
		return newScanMap;
	}

	/**
	 * 返回jsonstr{"k":"v"}
	 * */
	public static String getJsonString(Map<String, String> map)
	{
		String str = "";
		int oi = 0;
		for(Map.Entry<String,String> entry:map.entrySet()){
			if(oi>0)str+=",";
			str+="\""+entry.getKey()+"\":\""+entry.getValue()+"\"";
			oi++;
		}
		if(!Rock.isEmpt(str))str="{"+str+"}";
		return str;
	}

	/**
	 * 获取提交的参数
	 * */
	public static String getJsonParams(Map<String, String> map)
	{
		String str = "";
		int oi = 0;
		for(Map.Entry<String,String> entry:map.entrySet()){
			if(oi>0)str+="&";
			str+=""+entry.getKey()+"="+entry.getValue()+"";
			oi++;
		}
		return str;
	}

	/**
	 * 是否json数据
	 * */
	public static Boolean isJson(String str)
	{
		Boolean isj = false;
		if(!Rock.isEmpt(str)) {
			int len = str.length();
			String fstr = str.substring(0, 1);
			String lstr = str.substring(len-1, len);
			if((fstr.equals("{") && lstr.equals("}")) || (fstr.equals("[") && lstr.equals("]"))){
				isj = true;
			}
		}
		return isj;
	}

	/**
	 * 返回json解析
	 * */
	public static void strParse(int code, String bstr, CallBack scall, CallBack ecall)
	{
		String errstr = "";
		if(code==AA.HTTPB_SUCCESS){
			Map<String,String> ret = getJsonObject(bstr);
			String success = Rock.getMapString(ret, "success");
			if(success.equals("true")){
				String data = ret.get("data");
				if(scall!=null)scall.backMap(getJsonObject(data));
			}else if(success.equals("false")){
				errstr = ret.get("msg");
				String data = ret.get("data");
				if(ecall!=null && !Rock.isEmpt(data)){
					ecall.backMap(getJsonObject(data));
					errstr = "";
				}
			}else{
				errstr = "json解析错误";
				CLog.error(bstr, "-berror");
			}
		}else{
			errstr = bstr;
		}
		if(!Rock.isEmpt(errstr) && ecall!=null)ecall.backstr(errstr);
	}
}