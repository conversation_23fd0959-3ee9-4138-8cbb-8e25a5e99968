package com.rock.xinhuoanew;

import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.baselib.AA;
import com.baselib.CDate;
import com.baselib.CLog;
import com.baselib.CallBack;
import com.baselib.Jiami;
import com.baselib.Json;
import com.baselib.Rock;
import com.baselib.RockHttp;
import com.baselib.SqliteClass;

import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class XinhuAlarmService extends Service {

    private Context mContext    = null;
    private int splitTime       = 2 * 60; //定时轮询时间秒
    private int runCishu        = 0; //运行次数


    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        mContext = this.getApplicationContext();
        CLog.debug("AlarmService进程启动了.");
        startThread();
        super.onCreate();
    }

    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        CLog.debug("AlarmService进程停止了.");
        stopThread();
        super.onDestroy();
    }

    private Timer timer = null;
    private void startThread(){
        stopThread();
        timer = new Timer();
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                runJin();
            }
        };
        timer.schedule(task, 0, splitTime*1000);
    }

    private void stopThread(){
        if(timer!=null){
            timer.cancel();
            timer = null;
        }
    }



    private void runJin(){
        runCishu ++;
        CLog.debug("运行了"+runCishu+"次数");
        Xinhu.startService(mContext, Xinhu.SERVICETYPE_RESTART, "alarm");
    }

}
