<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.rock.xinhuoanew">


    <!-- Required -->
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- 允许程序在手机屏幕关闭后后台进程仍然运行 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 访问手机当前状态, 需要某些信息用于网络定位 -->


    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />  <!--录音权限-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 访问WiFi状态. 需要WiFi信息用于网络定位 -->
    <uses-permission android:name="android.permission.CAMERA"/><!--拍照-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>


    <application
        android:name=".XinhuApplication"
        android:allowBackup="true"
        android:icon="@mipmap/logo"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true"
        android:hardwareAccelerated="true"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/Theme.Xinhuoa">
        <activity
            android:name=".MainActivity"
            android:launchMode="singleTop"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <provider
            android:authorities="${applicationId}.fileprovider"
            android:name="androidx.core.content.FileProvider"
            android:grantUriPermissions="true"
            android:exported="false">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths"/>
        </provider>

        <activity android:name=".MainsActivity" ></activity>
        <activity android:name=".HomeActivity" ></activity>
        <activity android:name=".WebActivity"></activity>
        <activity android:name=".TonghuaActivity"></activity>
        <activity android:name=".WebneiActivity"></activity>
        <activity android:name=".ImageViewActivity"></activity>
        <activity android:name=".CameraActivity"></activity>
        <activity android:name=".ScanActivity"></activity>
        <activity android:name=".XinhuActivity"></activity>
        <service android:name=".XinhuService" android:enabled="true" android:exported="false"></service>

        <meta-data android:name="TencentMapSDK" android:value="55QBZ-JGYLO-BALWX-SZE4H-5SV5K-JCFV7" />


        <!--静态广播注册-->
        <receiver
            android:name=".XinhuReceiver"
            android:enabled="true"
            android:exported="true"
            >
            <intent-filter>
                <action android:name="com.rock.xinhuoa.ACTIONSTART" />
            </intent-filter>
        </receiver>


        <service android:name=".XinhuJobService" android:permission="android.permission.BIND_JOB_SERVICE" />
        <service android:name=".XinhuAlarmService" android:enabled="true" android:exported="false"></service>


        <!-- Since JCore2.0.0 Required SDK核心功能-->
        <!-- 可配置android:process参数将Service放在其他进程中；android:enabled属性不能是false -->
        <!-- 这个是自定义Service，要继承极光JCommonService，可以在更多手机平台上使得推送通道保持的更稳定 -->
        <service android:name=".JPushService"
            android:enabled="true"
            android:exported="false"
            android:process=":pushcore">
            <intent-filter>
                <action android:name="cn.jiguang.user.service.action" />
            </intent-filter>
        </service>

        <!-- Required since 3.0.7 -->
        <!-- 新的 tag/alias 接口结果返回需要开发者配置一个自定的广播 -->
        <!-- 3.3.0开始所有事件将通过该类回调 -->
        <!-- 该广播需要继承 JPush 提供的 JPushMessageReceiver 类, 并如下新增一个 Intent-Filter -->
        <receiver
            android:name=".JPushReceiver"
            android:enabled="true"
            android:exported="false" >
            <intent-filter>
                <action android:name="cn.jpush.android.intent.RECEIVER_MESSAGE" />
                <category android:name="com.rock.xinhuoanew" />
            </intent-filter>
        </receiver>

    </application>

</manifest>