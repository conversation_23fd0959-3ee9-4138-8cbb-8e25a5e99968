/**
 * 服务
 * from http://www.rockoa.com/
 * 来自信呼开发团队
 * */

package com.rock.xinhuoanew;


import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.baselib.AA;
import com.baselib.CLog;
import com.baselib.CallBack;
import com.baselib.Jiami;
import com.baselib.Json;
import com.baselib.RCache;
import com.baselib.RCall;
import com.baselib.Rock;
import com.baselib.RockHttp;
import com.rockwebsocket.ClientWebsocket;
import com.rockwebsocket.ClientWebsocketCallBack;


import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;


public class XinhuService extends Service {

	private Context mContext = null;
	private static Timer Timers = null,rTimers = null;
	private static ClientWebsocket webSockets = null;

	//主界面是否有打开
	public static Boolean mainOpenBool  = false;

	private static Boolean websocketOpen = false;

	//服务端的参数
	private static String websocketparams = "";

	//通话参数
	private static String tonghuaparams = "";


	//启动的次数
	private static int websocketLoad = 0,websocketBeat = 0;

	@Override
	public void onCreate() {
		//CLog.debug("webService等待启动");
		super.onCreate();
	}

	@Nullable
	@Override
	public IBinder onBind(Intent intent) {
		return null;
	}

	@Override
	public int onStartCommand(Intent intent, int flags, int startId) {
		mContext = this.getApplicationContext();
		if (intent == null) {
			CLog.error("服务没有Intent");
			return super.onStartCommand(intent, flags, startId);
		}
		Bundle bundle  = intent.getExtras();
		String type    = bundle.getString(Xinhu.SERVICETYPE);
		String params  = bundle.getString(Xinhu.SERVICETYPE_PARAMS);
		//CLog.debug("服务："+type+"："+params+"");

		if (Rock.equals(type, Xinhu.SERVICETYPE_RUNTIME)) {
			//String[] paramsa = params.split(",");
		}
		if (Rock.equals(type, Xinhu.SERVICETYPE_WEBSOCKET)) {
			startWebsocket(params);
		}
		if (Rock.equals(type, Xinhu.SERVICETYPE_STOP)) {
			if (Timers != null) Timers.cancel();
			if(rTimers != null)rTimers.cancel();
			websocketparams = "";
			websocketLoad = 0;
			stopWebsocket();
		}
		if (Rock.equals(type, Xinhu.SERVICETYPE_RESTART) && websocketLoad>0) {
			restartWebsocket(true);
		}
		if (Rock.equals(type, Xinhu.SERVICETYPE_TONGHUA)) {
			tonghuaparams = params;
			startCallRunTimer();
		}

		if (Rock.equals(type, Xinhu.SERVICETYPE_TONGHUAE)) {
			stopCallRunTimer();
		}
		return super.onStartCommand(intent, flags, startId);
	}

	@Override
	public void onDestroy() {
		super.onDestroy();
		CLog.error("webService卸载服务");
		//restartWebsocket(true);
	}

	/**
	 * 获取服务端状态
	 * */
	public static int getState()
	{
		if(Rock.isEmpt(Rock.admintoken))return 0;
		return (websocketOpen) ? 1 : 2;
	}

	/**
	 * timer定时运行的
	 */
	protected void startCallRunTimer() {
		if (Timers != null) Timers.cancel();
		Timers = new Timer();
		Timers.schedule(new TimerTask() {
			@Override
			public void run() {
				startCallRunTimers();
			}
		}, 2000);
	}

	protected void stopCallRunTimer() {
		if (Timers != null) Timers.cancel();
		Timers = null;
		tonghuaparams = "";
	}

	protected void startCallRunTimers() {
		if (Rock.isEmpt(tonghuaparams)) return;
		RockHttp.get(tonghuaparams, null, 1, "", new CallBack() {
			@Override
			public void backhttp(int code, int arg1, String bstr) {
				Json.strParse(code, bstr, new CallBack() {
					public void backMap(Map<String, String> a) {
						String state = Rock.getMapString(a, "state");
						a.put("calltype", state);
						Xinhu.sendBroadcast(mContext, AA.TONGHUA_ACTION, Json.getJsonString(a));
					}
				}, null);
				startCallRunTimer();
			}
		});
	}

	/**
	 * 检测心跳包
	 * */
	protected void restartWebsocket(Boolean bool)
	{
		if(Rock.isEmpt(websocketparams) || webSockets == null)return;
		Boolean bot 	= webSockets.sendBeat();
		websocketBeat 	= 0;
		if(bot){
			//try { Thread.sleep(500); } catch (InterruptedException e) {}
			//if(websocketBeat != 1){ websocketOpen = false;restartWebsocket(); }
		}
	}

	/**
	 * 重启服务
	 * */
	protected void restartWebsocket()
	{
		if (!Rock.isEmpt(websocketparams) && !websocketOpen) {
			startWebsocket(websocketparams);
			CLog.error("重新链接了websocket");
		}
	}
	protected void restartWebsocket(int time)
	{
		if(rTimers!=null)rTimers.cancel();
		if(Rock.isEmpt(websocketparams))return;
		CLog.debug("登等待"+time+"");
		rTimers = new Timer();
		rTimers.schedule(new TimerTask() {
			@Override
			public void run() {
				restartWebsocket();
			}
		}, time * 1000);
	}

	/**
	 * 启动Websocket服务
	 * */
	protected void startWebsocket(String params)
	{
		websocketparams			= params;
		Map<String,String> ret 	= Json.getJsonObject(params);
		stopWebsocket();
		String recid 	= ""+ret.get("recid")+"_app";
		String adminid 	= ret.get("adminid");
		String scont = "{\"from\":\""+recid+"\"";
		scont += ",\"adminid\":\""+adminid+"\"";
		scont += ",\"sendname\":\""+ret.get("adminname")+"\"";
		scont += ",\"atype\":\"connect\"}";
		final String sconts = scont;
		final String action = ret.get("actionname");
		final String wsurl = Jiami.base64decode(ret.get("wsurl"));

		webSockets = ClientWebsocket.create(wsurl);
		webSockets.setRecidAndmyid(recid, adminid);
		ClientWebsocketCallBack clientcall = new ClientWebsocketCallBack(){
			public void onOpen() {
				webSockets.send(sconts);
				websocketOpen 	= true;
				websocketLoad ++;
				CLog.error("websocket启动"+websocketLoad+"次", "websocket");
				broadWebsocket(action, "open", "");
			}

			@Override
			public void onMessage(String message) {
				//CLog.error("收到消息onMessage："+message+"", "--websocket");
				websocketOpen = true;
				if(Rock.contain(message, "rocksystembeat")){
					websocketBeat = 1;
					CLog.error("收到心跳包onMessage："+message+"", "websocket");
				}else {
					broadWebsocket(action, "message", message);
				}
			}

			@Override
			public void onClose(int code, String reason) {
				websocketOpen = false;
				CLog.debug("关闭websocket服务：("+code+")"+reason+"");
				broadWebsocket(action, "close","");
				if(code != 1000)restartWebsocket(10);//非正常关闭
			}

			@Override
			public void onError(int code, String errmsg) {
				CLog.error("错误websocket服务("+code+")："+wsurl+"："+errmsg);
				int time = 10;
				if(code == 499)time = 2;
				if(websocketLoad > 0){
					restartWebsocket(time);
					if(Rock.contain(errmsg, "Connection timed out")){
						//Xinhu.Notification(mContext, "温馨提示", "APP被后台限制联网，请重新打开");
					}
				}
				websocketOpen = false;
			}
		};
		webSockets.setClientWebsocketCallBack(clientcall);
		webSockets.connect();
	}
	protected void stopWebsocket()
	{
		websocketOpen = false;
		if(webSockets != null){
			webSockets.close();
			webSockets=null;
		}
	}
	protected void broadWebsocket(String action, String stype, String data)
	{
		if(Rock.isEmpt(data))data="{}";
		Boolean titong = false;
		if(stype.equals("message") && !mainOpenBool){
			Map<String,String> ret = Json.getJsonObject(data);
			String title 	= ret.get("title");
			String content 	= ret.get("content");
			if(Rock.contain(data, "calltonghua")){
				RCall.message(mContext, data);
				titong = true;
			}
			if(!titong && !Rock.isEmpt(title) && !Rock.isEmpt(content)){
				Xinhu.Notification(mContext, title, Jiami.base64decode(content), "",0,0);
			}
		}
		if(stype.equals("message")){
			//Xinhu.sendBroadcast(mContext, AA.MAIN_ALARS, stype);
			if(Rock.contain(data, "calltonghua")){
				Xinhu.sendBroadcast(mContext,AA.TONGHUA_ACTION, data); //通话发送广播
			}
		}
		if(!Rock.isEmpt(action))Xinhu.sendBroadcast(mContext,action,"{name:'"+action+"',stype:'websocket',sockettype:'"+stype+"',socketdata:"+data+"}");
	}
}
